import os
from dataclasses import dataclass
from typing import List, Dict, Any
import yaml


@dataclass
class DataConfig:
    """数据生成配置"""
    num_workflows: int = 1000
    workflow_types: List[str] = None
    min_tasks: int = 10
    max_tasks: int = 100
    min_nodes: int = 4
    max_nodes: int = 16
    heterogeneity_factor: float = 0.5

    def __post_init__(self):
        if self.workflow_types is None:
            self.workflow_types = ['montage', 'cybershake', 'ligo', 'sipht']


@dataclass
class ModelConfig:
    """模型配置"""
    hidden_dim: int = 256
    num_heads: int = 8
    num_layers: int = 6
    dropout: float = 0.1
    activation: str = 'relu'
    constraint_weight: float = 1.0


@dataclass
class TrainingConfig:
    """训练配置"""
    batch_size: int = 32
    learning_rate: float = 1e-3
    num_epochs: int = 100
    weight_decay: float = 1e-4
    patience: int = 10


@dataclass
class ExperimentConfig:
    """实验配置"""
    data: DataConfig
    model: ModelConfig
    training: TrainingConfig
    output_dir: str = "./outputs"
    log_level: str = "INFO"

    @classmethod
    def from_yaml(cls, config_path: str):
        with open(config_path, 'r') as f:
            config_dict = yaml.safe_load(f)

        return cls(
            data=DataConfig(**config_dict.get('data', {})),
            model=ModelConfig(**config_dict.get('model', {})),
            training=TrainingConfig(**config_dict.get('training', {})),
            output_dir=config_dict.get('output_dir', './outputs'),
            log_level=config_dict.get('log_level', 'INFO')
        )