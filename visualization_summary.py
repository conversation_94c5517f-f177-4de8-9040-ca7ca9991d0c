#!/usr/bin/env python3
"""
可视化总结脚本
展示所有生成的实验数据流图表
"""

import os
import glob
from datetime import datetime

def count_files_in_directory(directory):
    """统计目录中的文件数量"""
    if not os.path.exists(directory):
        return 0
    return len([f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))])

def list_png_files(directory):
    """列出目录中的PNG文件"""
    if not os.path.exists(directory):
        return []
    return [f for f in os.listdir(directory) if f.endswith('.png')]

def main():
    """主函数"""
    print("🎨 实验数据流可视化总结报告")
    print("=" * 60)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 统计各个目录的图表
    directories = {
        "实验数据流可视化": "./visualizations/experimental_flow",
        "详细分析图表": "./visualizations/detailed_analysis", 
        "原始可视化图表": "./visualizations"
    }
    
    total_charts = 0
    
    for name, path in directories.items():
        print(f"📊 {name}")
        print("-" * 40)
        
        if name == "原始可视化图表":
            # 只统计根目录的PNG文件，不包括子目录
            png_files = [f for f in os.listdir(path) if f.endswith('.png') and os.path.isfile(os.path.join(path, f))]
        else:
            png_files = list_png_files(path)
        
        if png_files:
            for i, file in enumerate(png_files, 1):
                print(f"  {i:2d}. {file}")
            print(f"     小计: {len(png_files)} 个图表")
            total_charts += len(png_files)
        else:
            print("     无图表文件")
        print()
    
    # 显示详细的实验数据流图表说明
    print("📋 实验数据流图表详细说明")
    print("=" * 60)
    
    experimental_flow_descriptions = {
        "01_data_generation_overview.png": "数据生成阶段概览 - 工作流类型分布、任务节点统计",
        "02_preprocessing_pipeline.png": "数据预处理流水线 - 特征提取、图着色算法",
        "03_model_architecture_flow.png": "模型架构数据流 - 三层GNN架构流程",
        "04_training_dynamics.png": "训练动态过程 - 损失变化、性能改进",
        "05_evaluation_results.png": "评估结果分析 - 性能对比、调度可视化",
        "06_comprehensive_analysis.png": "综合分析图表 - 端到端流程展示",
        "07_ablation_study.png": "消融实验结果 - 组件贡献、参数敏感性"
    }
    
    detailed_analysis_descriptions = {
        "workflow_complexity_analysis.png": "工作流复杂度分析 - 任务节点关系、复杂度分布",
        "performance_metrics_deep_dive.png": "性能指标深度分析 - 多维度对比、资源分析",
        "training_session_analysis.png": "训练会话分析 - 训练监控、实验统计"
    }
    
    original_descriptions = {
        "system_architecture.png": "系统架构图 - 整体设计和组件关系",
        "algorithm_comparison.png": "算法性能对比 - 与传统算法比较",
        "comprehensive_results.png": "综合结果展示 - 多角度性能分析",
        "coloring_analysis.png": "图着色分析 - 着色算法效果",
        "feature_analysis.png": "特征分析 - 特征工程效果",
        "layer_outputs.png": "层输出可视化 - 模型各层输出"
    }
    
    print("🔄 实验数据流可视化 (7个图表)")
    for file, desc in experimental_flow_descriptions.items():
        status = "✅" if os.path.exists(f"./visualizations/experimental_flow/{file}") else "❌"
        print(f"  {status} {desc}")
    print()
    
    print("🔍 详细分析图表 (3个图表)")
    for file, desc in detailed_analysis_descriptions.items():
        status = "✅" if os.path.exists(f"./visualizations/detailed_analysis/{file}") else "❌"
        print(f"  {status} {desc}")
    print()
    
    print("📈 原始可视化图表 (6个图表)")
    for file, desc in original_descriptions.items():
        status = "✅" if os.path.exists(f"./visualizations/{file}") else "❌"
        print(f"  {status} {desc}")
    print()
    
    # 显示文件路径信息
    print("📁 文件路径信息")
    print("=" * 60)
    print("实验数据流图表目录: ./visualizations/experimental_flow/")
    print("详细分析图表目录:   ./visualizations/detailed_analysis/")
    print("原始可视化图表目录: ./visualizations/")
    print("综合HTML报告:      ./outputs/comprehensive_experimental_report.html")
    print()
    
    # 显示使用建议
    print("💡 使用建议")
    print("=" * 60)
    print("1. 论文撰写:")
    print("   - 使用实验数据流图表展示完整的实验过程")
    print("   - 使用详细分析图表支持深入的技术讨论")
    print("   - 使用原始图表进行算法对比和架构说明")
    print()
    print("2. 报告展示:")
    print("   - 打开 comprehensive_experimental_report.html 查看完整报告")
    print("   - 所有图表都是高分辨率(300 DPI)，适合打印和发表")
    print()
    print("3. 进一步分析:")
    print("   - 可以修改可视化脚本生成定制化图表")
    print("   - 可以基于实际训练数据更新图表内容")
    print()
    
    # 总结
    print("📊 总结")
    print("=" * 60)
    print(f"✅ 成功生成 {total_charts} 个实验数据流可视化图表")
    print("✅ 涵盖从数据生成到性能评估的完整流程")
    print("✅ 提供多层次、多角度的分析视图")
    print("✅ 生成综合HTML报告便于查看和分享")
    print()
    print("🎉 实验数据流可视化系统部署完成!")
    print("📧 如需技术支持，请查看各目录下的README.md文件")

if __name__ == "__main__":
    main()
