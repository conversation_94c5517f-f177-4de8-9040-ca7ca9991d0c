2025-07-29 18:54:55 | [32mINFO[0m | __main__ | main:372 | ✅ 成功加载配置文件: config/experiment_config.yaml
2025-07-29 18:54:55 | [32mINFO[0m | __main__ | main:381 | 🎯 GNN工作流调度器启动
2025-07-29 18:54:55 | [32mINFO[0m | __main__ | main:382 |    运行模式: debug
2025-07-29 18:54:55 | [32mINFO[0m | __main__ | main:383 |    数据目录: ./data
2025-07-29 18:54:55 | [32mINFO[0m | __main__ | main:384 |    输出目录: ./outputs
2025-07-29 18:54:55 | [32mINFO[0m | __main__ | main:385 |    调试模式: True
2025-07-29 18:54:55 | [32mINFO[0m | __main__ | main:420 | 🐛 进入调试模式...
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IHDR' 16 13
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'sBIT' 41 4
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | _open:728 | b'sBIT' 41 4 (unknown)
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'pHYs' 57 9
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IDAT' 78 1189
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IHDR' 16 13
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'sBIT' 41 4
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | _open:728 | b'sBIT' 41 4 (unknown)
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'pHYs' 57 9
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IDAT' 78 2994
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IHDR' 16 13
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'sBIT' 41 4
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | _open:728 | b'sBIT' 41 4 (unknown)
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'pHYs' 57 9
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IDAT' 78 696
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IHDR' 16 13
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'sBIT' 41 4
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | _open:728 | b'sBIT' 41 4 (unknown)
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'pHYs' 57 9
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IDAT' 78 526
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IHDR' 16 13
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'sBIT' 41 4
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | _open:728 | b'sBIT' 41 4 (unknown)
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'pHYs' 57 9
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IDAT' 78 499
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IHDR' 16 13
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'sBIT' 41 4
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | _open:728 | b'sBIT' 41 4 (unknown)
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'pHYs' 57 9
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IDAT' 78 673
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IHDR' 16 13
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'sBIT' 41 4
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | _open:728 | b'sBIT' 41 4 (unknown)
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'pHYs' 57 9
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IDAT' 78 922
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IHDR' 16 13
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'sBIT' 41 4
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | _open:728 | b'sBIT' 41 4 (unknown)
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'pHYs' 57 9
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IDAT' 78 568
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IHDR' 16 13
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'sBIT' 41 4
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | _open:728 | b'sBIT' 41 4 (unknown)
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'pHYs' 57 9
2025-07-29 18:54:56 | [36mDEBUG[0m | PIL.PngImagePlugin | call:191 | STREAM b'IDAT' 78 626
2025-07-29 18:54:58 | [32mINFO[0m | __main__ | main:436 | 🎉 程序执行完成!
