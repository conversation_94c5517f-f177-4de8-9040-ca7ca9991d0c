2025-07-30 15:08:48 | [32mINFO[0m | __main__ | main:405 | ✅ 成功加载配置文件: config/experiment_config.yaml
2025-07-30 15:08:48 | [32mINFO[0m | __main__ | main:414 | 🎯 GNN工作流调度器启动
2025-07-30 15:08:48 | [32mINFO[0m | __main__ | main:415 |    运行模式: all
2025-07-30 15:08:48 | [32mINFO[0m | __main__ | main:416 |    数据目录: ./data
2025-07-30 15:08:48 | [32mINFO[0m | __main__ | main:417 |    输出目录: ./outputs
2025-07-30 15:08:48 | [32mINFO[0m | __main__ | main:418 |    调试模式: False
2025-07-30 15:08:51 | [32mINFO[0m | __main__ | main:435 | ✅ 加载已处理的数据: 100 个工作流
2025-07-30 15:08:52 | [32mINFO[0m | __main__ | main:469 | 🎉 程序执行完成!
