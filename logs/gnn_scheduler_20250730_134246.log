2025-07-30 13:42:46 | [32mINFO[0m | __main__ | main:382 | ✅ 成功加载配置文件: config/experiment_config.yaml
2025-07-30 13:42:46 | [32mINFO[0m | __main__ | main:391 | 🎯 GNN工作流调度器启动
2025-07-30 13:42:46 | [32mINFO[0m | __main__ | main:392 |    运行模式: debug
2025-07-30 13:42:46 | [32mINFO[0m | __main__ | main:393 |    数据目录: ./data
2025-07-30 13:42:46 | [32mINFO[0m | __main__ | main:394 |    输出目录: ./outputs
2025-07-30 13:42:46 | [32mINFO[0m | __main__ | main:395 |    调试模式: True
2025-07-30 13:42:46 | [32mINFO[0m | __main__ | main:430 | 🐛 进入调试模式...
2025-07-30 13:42:46 | [31mERROR[0m | __main__ | main:441 | ❌ 程序执行出错: 'TrainingConfig' object is not subscriptable
