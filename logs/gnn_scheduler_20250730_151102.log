2025-07-30 15:11:02 | [32mINFO[0m | __main__ | main:416 | ✅ 成功加载配置文件: config/experiment_config.yaml
2025-07-30 15:11:02 | [32mINFO[0m | __main__ | main:425 | 🎯 GNN工作流调度器启动
2025-07-30 15:11:02 | [32mINFO[0m | __main__ | main:426 |    运行模式: all
2025-07-30 15:11:02 | [32mINFO[0m | __main__ | main:427 |    数据目录: ./data
2025-07-30 15:11:02 | [32mINFO[0m | __main__ | main:428 |    输出目录: ./outputs
2025-07-30 15:11:02 | [32mINFO[0m | __main__ | main:429 |    调试模式: False
2025-07-30 15:11:05 | [32mINFO[0m | __main__ | main:446 | ✅ 加载已处理的数据: 100 个工作流
2025-07-30 15:11:12 | [32mINFO[0m | __main__ | main:480 | 🎉 程序执行完成!
