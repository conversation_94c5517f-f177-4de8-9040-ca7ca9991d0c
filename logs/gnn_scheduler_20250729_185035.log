2025-07-29 18:50:35 | [32mINFO[0m | __main__ | main:372 | ✅ 成功加载配置文件: config/experiment_config.yaml
2025-07-29 18:50:35 | [32mINFO[0m | __main__ | main:381 | 🎯 GNN工作流调度器启动
2025-07-29 18:50:35 | [32mINFO[0m | __main__ | main:382 |    运行模式: debug
2025-07-29 18:50:35 | [32mINFO[0m | __main__ | main:383 |    数据目录: ./data
2025-07-29 18:50:35 | [32mINFO[0m | __main__ | main:384 |    输出目录: ./outputs
2025-07-29 18:50:35 | [32mINFO[0m | __main__ | main:385 |    调试模式: True
2025-07-29 18:50:35 | [32mINFO[0m | __main__ | main:420 | 🐛 进入调试模式...
2025-07-29 18:50:35 | [32mINFO[0m | __main__ | main:436 | 🎉 程序执行完成!
