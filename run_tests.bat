@echo off
echo 🚀 激活GNN_Workflow_Scheduler环境并运行测试...

call conda activate GNN_Workflow_Scheduler

echo ✅ 环境已激活，检查PyTorch...
python -c "import torch; print('PyTorch版本:', torch.__version__); print('CUDA可用:', torch.cuda.is_available())"

echo.
echo 🔄 开始执行改进功能测试...

echo.
echo 📊 1. 测试特征提取器...
python test_feature_extraction.py

echo.
echo 📊 2. 运行主程序调试模式...
python main.py --mode debug --debug

echo.
echo ✅ 所有测试完成！

pause
