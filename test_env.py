# 验证脚本
import torch
import torch_geometric
import dgl
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import networkx as nx
import sklearn
import scipy
import yaml
import tensorboard
import plotly

print("🎉 环境配置完成！")
print(f"PyTorch: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
print(f"CUDA版本: {torch.version.cuda}")
print(f"PyTorch Geometric: {torch_geometric.__version__}")
print(f"DGL: {dgl.__version__}")
print(f"NumPy: {np.__version__}")
print(f"Pandas: {pd.__version__}")
print(f"Scikit-learn: {sklearn.__version__}")