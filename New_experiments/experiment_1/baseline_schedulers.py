import numpy as np
import networkx as nx
import random
import torch
import torch.nn as nn

# ------------------ 经典启发式算法 ------------------
class HEFTScheduler:
    """异构最早完成时间算法 (HEFT)"""
    def __init__(self):
        self.name = "HEFT"
    def schedule(self, workflow_dag: nx.DiGraph, nodes: list) -> dict:
        task_priorities = self._compute_upward_rank(workflow_dag, nodes)
        sorted_tasks = sorted(task_priorities.items(), key=lambda x: x[1], reverse=True)
        node_available_time = [0.0] * len(nodes)
        task_assignments = {}
        task_finish_times = {}
        for task_id, _ in sorted_tasks:
            best_node = None
            best_finish_time = float('inf')
            for node_id in range(len(nodes)):
                earliest_start_time = node_available_time[node_id]
                for pred in workflow_dag.predecessors(task_id):
                    if pred in task_assignments:
                        pred_node = task_assignments[pred]
                        pred_finish_time = task_finish_times[pred]
                        if pred_node != node_id:
                            comm_time = self._get_communication_time(workflow_dag, pred, task_id, pred_node, node_id)
                            earliest_start_time = max(earliest_start_time, pred_finish_time + comm_time)
                        else:
                            earliest_start_time = max(earliest_start_time, pred_finish_time)
                execution_time = self._get_execution_time(workflow_dag, task_id, nodes[node_id])
                finish_time = earliest_start_time + execution_time
                if finish_time < best_finish_time:
                    best_finish_time = finish_time
                    best_node = node_id
            task_assignments[task_id] = best_node
            task_finish_times[task_id] = best_finish_time
            node_available_time[best_node] = best_finish_time
        return task_assignments
    def _compute_upward_rank(self, dag, nodes):
        upward_rank = {}
        topo_order = list(nx.topological_sort(dag))
        for task_id in reversed(topo_order):
            avg_execution_time = np.mean([self._get_execution_time(dag, task_id, node) for node in nodes])
            max_successor_rank = 0.0
            for succ in dag.successors(task_id):
                if succ in upward_rank:
                    avg_comm_time = np.mean([self._get_communication_time(dag, task_id, succ, i, j)
                                             for i in range(len(nodes)) for j in range(len(nodes)) if i != j])
                    max_successor_rank = max(max_successor_rank, avg_comm_time + upward_rank[succ])
            upward_rank[task_id] = avg_execution_time + max_successor_rank
        return upward_rank
    def _get_execution_time(self, dag, task_id, node):
        task_data = dag.nodes[task_id]
        runtime = task_data.get('runtime', 1.0)
        cpu_capacity = node.get('cpu_capacity', 1.0)
        return runtime / max(cpu_capacity, 0.1)
    def _get_communication_time(self, dag, task1, task2, node1_id, node2_id):
        if node1_id == node2_id:
            return 0.0
        edge_data = dag.get_edge_data(task1, task2)
        if edge_data:
            return edge_data.get('communication_cost', 1.0)
        return 0.0

class CPOPScheduler:
    """关键路径优先算法 (CPOP)"""
    def __init__(self):
        self.name = "CPOP"
    def schedule(self, workflow_dag: nx.DiGraph, nodes: list) -> dict:
        critical_path = self._find_critical_path(workflow_dag, nodes)
        critical_node = self._select_critical_node(workflow_dag, nodes, critical_path)
        task_assignments = {task_id: critical_node for task_id in critical_path}
        remaining_tasks = [t for t in workflow_dag.nodes() if t not in critical_path]
        heft = HEFTScheduler()
        remaining_dag = workflow_dag.subgraph(remaining_tasks + critical_path)
        remaining_assignments = heft.schedule(remaining_dag, nodes)
        for task_id, node_id in remaining_assignments.items():
            if task_id not in task_assignments:
                task_assignments[task_id] = node_id
        return task_assignments
    def _find_critical_path(self, dag, nodes):
        try:
            path = nx.dag_longest_path(dag, weight=lambda u, v, d:
                np.mean([self._get_execution_time(dag, u, node) for node in nodes]))
            return path
        except:
            topo_order = list(nx.topological_sort(dag))
            return topo_order[:min(len(topo_order), 5)]
    def _select_critical_node(self, dag, nodes, critical_path):
        best_node = 0
        best_execution_time = float('inf')
        for node_id, node in enumerate(nodes):
            total_execution_time = sum([self._get_execution_time(dag, task_id, node) for task_id in critical_path])
            if total_execution_time < best_execution_time:
                best_execution_time = total_execution_time
                best_node = node_id
        return best_node
    def _get_execution_time(self, dag, task_id, node):
        task_data = dag.nodes[task_id]
        runtime = task_data.get('runtime', 1.0)
        cpu_capacity = node.get('cpu_capacity', 1.0)
        return runtime / max(cpu_capacity, 0.1)

class RandomScheduler:
    """随机调度算法（基线）"""
    def __init__(self, seed=42):
        self.name = "Random"
        self.seed = seed
    def schedule(self, workflow_dag: nx.DiGraph, nodes: list) -> dict:
        random.seed(self.seed)
        return {task_id: random.randint(0, len(nodes)-1) for task_id in workflow_dag.nodes()}

class RoundRobinScheduler:
    """轮询调度算法"""
    def __init__(self):
        self.name = "RoundRobin"
    def schedule(self, workflow_dag: nx.DiGraph, nodes: list) -> dict:
        num_nodes = len(nodes)
        return {task_id: i % num_nodes for i, task_id in enumerate(workflow_dag.nodes())}

# ------------------ 进化/群体智能算法 ------------------
class GAScheduler:
    """遗传算法调度（简化纯Python实现）"""
    def __init__(self, pop_size=30, generations=30, mutation_rate=0.1, seed=42):
        self.name = "GA-WS"
        self.pop_size = pop_size
        self.generations = generations
        self.mutation_rate = mutation_rate
        self.seed = seed
    def schedule(self, workflow_dag: nx.DiGraph, nodes: list) -> dict:
        random.seed(self.seed)
        num_tasks = len(workflow_dag.nodes())
        num_nodes = len(nodes)
        def fitness(assign):
            # 以最大完成时间为目标
            finish_times = [0.0] * num_nodes
            for task_id in nx.topological_sort(workflow_dag):
                node = assign[task_id]
                preds = list(workflow_dag.predecessors(task_id))
                pred_finish = max([finish_times[assign[p]] for p in preds] or [0.0])
                exec_time = workflow_dag.nodes[task_id].get('runtime', 1.0) / max(nodes[node].get('cpu_capacity', 1.0), 0.1)
                finish_times[node] = max(finish_times[node], pred_finish + exec_time)
            return -max(finish_times)  # 负makespan
        # 初始化种群
        population = [ {task_id: random.randint(0, num_nodes-1) for task_id in workflow_dag.nodes()} for _ in range(self.pop_size)]
        for _ in range(self.generations):
            population.sort(key=fitness, reverse=True)
            next_gen = population[:2]  # 精英保留
            while len(next_gen) < self.pop_size:
                p1, p2 = random.sample(population[:10], 2)
                child = {}
                for task_id in workflow_dag.nodes():
                    child[task_id] = p1[task_id] if random.random() < 0.5 else p2[task_id]
                # 变异
                if random.random() < self.mutation_rate:
                    mutate_task = random.choice(list(workflow_dag.nodes()))
                    child[mutate_task] = random.randint(0, num_nodes-1)
                next_gen.append(child)
            population = next_gen
        best = max(population, key=fitness)
        return best

class PSOScheduler:
    """粒子群优化调度（简化纯Python实现）"""
    def __init__(self, swarm_size=30, iterations=30, seed=42):
        self.name = "PSO-WS"
        self.swarm_size = swarm_size
        self.iterations = iterations
        self.seed = seed
    def schedule(self, workflow_dag: nx.DiGraph, nodes: list) -> dict:
        random.seed(self.seed)
        num_tasks = len(workflow_dag.nodes())
        num_nodes = len(nodes)
        def fitness(assign):
            finish_times = [0.0] * num_nodes
            for task_id in nx.topological_sort(workflow_dag):
                node = assign[task_id]
                preds = list(workflow_dag.predecessors(task_id))
                pred_finish = max([finish_times[assign[p]] for p in preds] or [0.0])
                exec_time = workflow_dag.nodes[task_id].get('runtime', 1.0) / max(nodes[node].get('cpu_capacity', 1.0), 0.1)
                finish_times[node] = max(finish_times[node], pred_finish + exec_time)
            return -max(finish_times)
        # 初始化粒子
        particles = [ {task_id: random.randint(0, num_nodes-1) for task_id in workflow_dag.nodes()} for _ in range(self.swarm_size)]
        pbest = particles[:]
        gbest = max(particles, key=fitness)
        for _ in range(self.iterations):
            for i, particle in enumerate(particles):
                # 简单“跳跃”到pbest/gbest
                for task_id in workflow_dag.nodes():
                    if random.random() < 0.5:
                        particle[task_id] = pbest[i][task_id]
                    if random.random() < 0.3:
                        particle[task_id] = gbest[task_id]
                    if random.random() < 0.1:
                        particle[task_id] = random.randint(0, num_nodes-1)
                if fitness(particle) > fitness(pbest[i]):
                    pbest[i] = dict(particle)
            gbest = max(pbest, key=fitness)
        return gbest

class CGWSAScheduler:
    """混沌灰狼调度算法（简化纯Python实现）"""
    def __init__(self, wolves=20, iterations=30, seed=42):
        self.name = "CGWSA"
        self.wolves = wolves
        self.iterations = iterations
        self.seed = seed
    def schedule(self, workflow_dag: nx.DiGraph, nodes: list) -> dict:
        random.seed(self.seed)
        num_tasks = len(workflow_dag.nodes())
        num_nodes = len(nodes)
        def fitness(assign):
            finish_times = [0.0] * num_nodes
            for task_id in nx.topological_sort(workflow_dag):
                node = assign[task_id]
                preds = list(workflow_dag.predecessors(task_id))
                pred_finish = max([finish_times[assign[p]] for p in preds] or [0.0])
                exec_time = workflow_dag.nodes[task_id].get('runtime', 1.0) / max(nodes[node].get('cpu_capacity', 1.0), 0.1)
                finish_times[node] = max(finish_times[node], pred_finish + exec_time)
            return -max(finish_times)
        # 初始化狼群
        wolves = [ {task_id: random.randint(0, num_nodes-1) for task_id in workflow_dag.nodes()} for _ in range(self.wolves)]
        for _ in range(self.iterations):
            wolves.sort(key=fitness, reverse=True)
            alpha, beta, delta = wolves[0], wolves[1], wolves[2]
            for i in range(3, self.wolves):
                wolf = wolves[i]
                for task_id in workflow_dag.nodes():
                    r = random.random()
                    if r < 0.33:
                        wolf[task_id] = alpha[task_id]
                    elif r < 0.66:
                        wolf[task_id] = beta[task_id]
                    else:
                        wolf[task_id] = delta[task_id]
                    if random.random() < 0.1:
                        wolf[task_id] = random.randint(0, num_nodes-1)
        best = max(wolves, key=fitness)
        return best

# ------------------ 简单GNN基线 ------------------
class BasicGCNScheduler:
    """Basic GCN Scheduler (single-layer GCN, greedy assignment)"""
    def __init__(self, input_dim=16, hidden_dim=32):
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        class GCNLayer(nn.Module):
            def __init__(self, in_dim, out_dim):
                super().__init__()
                self.linear = nn.Linear(in_dim, out_dim)
            def forward(self, x, adj):
                h = torch.matmul(adj, x)
                return self.linear(h)
        self.model = GCNLayer(input_dim, hidden_dim)
    def schedule(self, workflow_dag: nx.DiGraph, nodes: list) -> dict:
        num_tasks = len(workflow_dag.nodes())
        num_nodes = len(nodes)
        x = torch.eye(num_tasks, self.input_dim)
        adj = nx.to_numpy_array(workflow_dag)
        adj = torch.tensor(adj, dtype=torch.float32)
        h = self.model(x, adj)
        assignment = {i: int(torch.argmax(h[i]) % num_nodes) for i in range(num_tasks)}
        return assignment

class BasicGATScheduler:
    """Basic GAT Scheduler (single-layer GAT, greedy assignment)"""
    def __init__(self, input_dim=16, hidden_dim=32, num_heads=2):
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        class GATLayer(nn.Module):
            def __init__(self, in_dim, out_dim, heads):
                super().__init__()
                self.gat = nn.Linear(in_dim, out_dim)  # 简化为线性层
            def forward(self, x, edge_index):
                return self.gat(x)
        self.model = GATLayer(input_dim, hidden_dim, num_heads)
    def schedule(self, workflow_dag: nx.DiGraph, nodes: list) -> dict:
        num_tasks = len(workflow_dag.nodes())
        num_nodes = len(nodes)
        x = torch.eye(num_tasks, self.input_dim)
        h = self.model(x, None)
        assignment = {i: int(torch.argmax(h[i]) % num_nodes) for i in range(num_tasks)}
        return assignment

# ------------------ 简单DQN基线 ------------------
class DQNScheduler:
    """Q表强化学习调度（简化纯Python实现）"""
    def __init__(self, episodes=30, epsilon=0.1, seed=42):
        self.name = "DQN-Scheduler"
        self.episodes = episodes
        self.epsilon = epsilon
        self.seed = seed
    def schedule(self, workflow_dag: nx.DiGraph, nodes: list) -> dict:
        random.seed(self.seed)
        num_tasks = len(workflow_dag.nodes())
        num_nodes = len(nodes)
        Q = np.zeros((num_tasks, num_nodes))
        for _ in range(self.episodes):
            for task_id in range(num_tasks):
                if random.random() < self.epsilon:
                    action = random.randint(0, num_nodes-1)
                else:
                    action = np.argmax(Q[task_id])
                reward = -workflow_dag.nodes[task_id].get('runtime', 1.0) / max(nodes[action].get('cpu_capacity', 1.0), 0.1)
                Q[task_id, action] += 0.1 * (reward - Q[task_id, action])
        assignment = {task_id: int(np.argmax(Q[task_id])) for task_id in range(num_tasks)}
        return assignment

# 所有算法统一注册
ALL_SCHEDULERS = {
    'HEFT': HEFTScheduler,
    'CPOP': CPOPScheduler,
    'Random': RandomScheduler,
    'RoundRobin': RoundRobinScheduler,
    'GA': GAScheduler,
    'PSO': PSOScheduler,
    'CGWSA': CGWSAScheduler,
    'GCN': BasicGCNScheduler,
    'GAT': BasicGATScheduler,
    'DQN': DQNScheduler,
}