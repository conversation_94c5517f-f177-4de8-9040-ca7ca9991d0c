{"experiment_info": {"name": "Performance Comparison Experiment", "timestamp": "2025-07-31T00:30:05.228048", "num_workflows": 99, "workflow_types": ["montage", "brain", "sipht"]}, "algorithm_results": {"HEFT": {"algorithm": "HEFT", "results_by_workflow": {"montage": {"makespan": {"mean": 15.35904379166785, "std": 0.9069649233651816, "min": 14.07685679740263, "max": 17.2441136062043}, "resource_utilization": {"mean": 0.6727674868671208, "std": 0.02597256324540359, "min": 0.6397807356484123, "max": 0.7336606664387122}, "load_balance_degree": {"mean": 2.460119914663353, "std": 0.09497429843468483, "min": 2.3394967199083734, "max": 2.6827890041415596}, "energy_consumption": {"mean": 8.975409700974648, "std": 0.5300057661162256, "min": 8.226134307083612, "max": 10.07699350592372}}, "brain": {"makespan": {"mean": 14.51523597435425, "std": 0.6375262199057098, "min": 12.697256779195781, "max": 15.779063450718894}, "resource_utilization": {"mean": 0.6612498922759278, "std": 0.028891478029874167, "min": 0.5844002019369036, "max": 0.7146458413126421}, "load_balance_degree": {"mean": 2.393329833983768, "std": 0.10456990182454459, "min": 2.115179835368643, "max": 2.5865912913181446}, "energy_consumption": {"mean": 8.571598725844606, "std": 0.37647468796864797, "min": 7.498037939071, "max": 9.317919488747432}}, "sipht": {"makespan": {"mean": 15.581782753890385, "std": 0.8839086296538353, "min": 13.976430359678364, "max": 17.467486405571407}, "resource_utilization": {"mean": 0.6713079398002031, "std": 0.04010435984610261, "min": 0.6034973675366394, "max": 0.7773598657819795}, "load_balance_degree": {"mean": 2.406173205239238, "std": 0.14374630531509075, "min": 2.1631193512045495, "max": 2.7862957801894357}, "energy_consumption": {"mean": 8.580250844703766, "std": 0.486732351876405, "min": 7.696248901277506, "max": 9.618630766035773}}}, "aggregated_results": {"makespan": {"mean": 15.15202083997083, "std": 0.4593650582646559}, "resource_utilization": {"mean": 0.668441772981084, "std": 0.005120216822754985}, "load_balance_degree": {"mean": 2.4198743179621194, "std": 0.028936932418395657}, "energy_consumption": {"mean": 8.709086423841006, "std": 0.18835211838042434}}}, "CPOP": {"algorithm": "CPOP", "results_by_workflow": {"montage": {"makespan": {"mean": 14.880319304243368, "std": 0.8033375303330341, "min": 13.62629302771329, "max": 16.50771477285129}, "resource_utilization": {"mean": 0.7078650581949291, "std": 0.03231504271975374, "min": 0.6382548732789437, "max": 0.7593412327988629}, "load_balance_degree": {"mean": 2.303053921732798, "std": 0.105137674200889, "min": 2.0765757144709296, "max": 2.470532743331512}, "energy_consumption": {"mean": 8.205690537645975, "std": 0.4429971586234622, "min": 7.514162933910489, "max": 9.103111038156058}}, "brain": {"makespan": {"mean": 14.000333576908492, "std": 0.7231921320690784, "min": 12.334936714197843, "max": 15.07218091171657}, "resource_utilization": {"mean": 0.6921261753323635, "std": 0.033685376048813616, "min": 0.625058278216193, "max": 0.7611216719938405}, "load_balance_degree": {"mean": 2.2288691018499955, "std": 0.10847775526383543, "min": 2.0128888818612007, "max": 2.4510568129298624}, "energy_consumption": {"mean": 7.801693690399153, "std": 0.40299921874829475, "min": 6.87365036025664, "max": 8.398981215236105}}, "sipht": {"makespan": {"mean": 15.394296748980556, "std": 0.9547425995371329, "min": 13.933196146636043, "max": 17.155770207438064}, "resource_utilization": {"mean": 0.7194003658735934, "std": 0.03873992618999418, "min": 0.6524802346556324, "max": 0.7814491333973527}, "load_balance_degree": {"mean": 2.2942360852270545, "std": 0.12354530358931491, "min": 2.080821431646864, "max": 2.4921154973733173}, "energy_consumption": {"mean": 7.999364277249478, "std": 0.49611450066476115, "min": 7.2401301170637336, "max": 8.914681689189143}}}, "aggregated_results": {"makespan": {"mean": 14.758316543377473, "std": 0.5755848252096754}, "resource_utilization": {"mean": 0.706463866466962, "std": 0.011178636503429805}, "load_balance_degree": {"mean": 2.2753863696032828, "std": 0.033089078858779146}, "energy_consumption": {"mean": 8.002249501764867, "std": 0.16494363994350927}}}, "PEFT": {"algorithm": "PEFT", "results_by_workflow": {"montage": {"makespan": {"mean": 14.874606325094287, "std": 0.7322520831486122, "min": 13.542007646377234, "max": 16.09225054313698}, "resource_utilization": {"mean": 0.7026431005260799, "std": 0.03561257646065092, "min": 0.6254943767003173, "max": 0.762865909097524}, "load_balance_degree": {"mean": 2.4236095351479285, "std": 0.12283758257441911, "min": 2.157502342821384, "max": 2.631334585003054}, "energy_consumption": {"mean": 8.683664406820153, "std": 0.427482328761209, "min": 7.905705013338358, "max": 9.394514396748924}}, "brain": {"makespan": {"mean": 13.54139612206128, "std": 0.5223533209194491, "min": 12.348228074849898, "max": 14.182502758161563}, "resource_utilization": {"mean": 0.6644321239140484, "std": 0.02288994223747266, "min": 0.6185075579238256, "max": 0.7162550640947605}, "load_balance_degree": {"mean": 2.2684235452055197, "std": 0.0781480636637524, "min": 2.111633463802047, "max": 2.445351140149607}, "energy_consumption": {"mean": 7.988562200406308, "std": 0.30815522691604297, "min": 7.2846689626066485, "max": 8.366774328123075}}, "sipht": {"makespan": {"mean": 15.08251692380853, "std": 0.6357778588411449, "min": 14.090055559857865, "max": 16.417413345309317}, "resource_utilization": {"mean": 0.6996800501853626, "std": 0.02367827149971516, "min": 0.63326939429914, "max": 0.7378711433021153}, "load_balance_degree": {"mean": 2.3655992742814633, "std": 0.08005559378336746, "min": 2.141066647793993, "max": 2.4947223243628125}, "energy_consumption": {"mean": 8.297057664262272, "std": 0.3497483598469686, "min": 7.751093803730981, "max": 9.031398798501334}}}, "aggregated_results": {"makespan": {"mean": 14.499506456988035, "std": 0.6827826714675364}, "resource_utilization": {"mean": 0.6889184248751636, "std": 0.017356634041947608}, "load_balance_degree": {"mean": 2.3525441182116373, "std": 0.06402343682125057}, "energy_consumption": {"mean": 8.323094757162911, "std": 0.28437090450722524}}}, "IPPTS": {"algorithm": "IPPTS", "results_by_workflow": {"montage": {"makespan": {"mean": 14.03868154710764, "std": 0.6006072807147044, "min": 12.962556678501414, "max": 15.274774510841405}, "resource_utilization": {"mean": 0.7234275363012952, "std": 0.03154661647462659, "min": 0.6746213675140257, "max": 0.7816799421642822}, "load_balance_degree": {"mean": 2.2594723051602097, "std": 0.09852915830431319, "min": 2.1070365999068197, "max": 2.4414113262117305}, "energy_consumption": {"mean": 8.05335131243427, "std": 0.34454100380942687, "min": 7.436027556364126, "max": 8.762441468683809}}, "brain": {"makespan": {"mean": 13.292480892992092, "std": 0.564059814268986, "min": 12.16603083206535, "max": 14.256308267544986}, "resource_utilization": {"mean": 0.7118375865415201, "std": 0.034129091674779705, "min": 0.6299445483894924, "max": 0.7623267933530027}, "load_balance_degree": {"mean": 2.2005870930880986, "std": 0.10550726746986692, "min": 1.9474215309172511, "max": 2.3566703049755393}, "energy_consumption": {"mean": 7.705556241531722, "std": 0.3269814459337733, "min": 7.0525611860847, "max": 8.26428008710525}}, "sipht": {"makespan": {"mean": 14.3159207476267, "std": 0.7383998097916402, "min": 12.801346524045698, "max": 15.849613078990334}, "resource_utilization": {"mean": 0.7246010662767006, "std": 0.034673639899323, "min": 0.6492271258137937, "max": 0.7817125503159551}, "load_balance_degree": {"mean": 2.218322971381755, "std": 0.10615128167740656, "min": 1.987570145648847, "max": 2.3931663753874592}, "energy_consumption": {"mean": 7.738599020392567, "std": 0.3991486223936343, "min": 6.919882375509013, "max": 8.567650129455306}}}, "aggregated_results": {"makespan": {"mean": 13.882361062575477, "std": 0.4321915890180376}, "resource_utilization": {"mean": 0.7199553963731719, "std": 0.005760116925777453}, "load_balance_degree": {"mean": 2.2261274565433546, "std": 0.024665083845692905}, "energy_consumption": {"mean": 7.832502191452853, "std": 0.15674545604771653}}}, "GA": {"algorithm": "GA", "results_by_workflow": {"montage": {"makespan": {"mean": 12.651939350037594, "std": 0.69525667340734, "min": 10.987780979014584, "max": 13.827441650020704}, "resource_utilization": {"mean": 0.7432988364956253, "std": 0.03727912838286349, "min": 0.6583783944233479, "max": 0.795424057695378}, "load_balance_degree": {"mean": 2.004950808968463, "std": 0.10055554366430285, "min": 1.7758890902208724, "max": 2.145551734573059}, "energy_consumption": {"mean": 7.4596384065388435, "std": 0.4099263551509531, "min": 6.478443401125743, "max": 8.152719669523457}}, "brain": {"makespan": {"mean": 12.29866636332082, "std": 0.5892960716378932, "min": 11.241738992357261, "max": 13.474278484655528}, "resource_utilization": {"mean": 0.7508500572171743, "std": 0.03464868923518041, "min": 0.6937030403139278, "max": 0.8179013835765192}, "load_balance_degree": {"mean": 2.0046527104945637, "std": 0.09250660384553741, "min": 1.852079075811285, "max": 2.1836693088640016}, "energy_consumption": {"mean": 7.3276770884642275, "std": 0.35110890847005616, "min": 6.697948445407585, "max": 8.02811937642781}}, "sipht": {"makespan": {"mean": 13.332300421713075, "std": 0.4432796236953828, "min": 12.452726665882778, "max": 14.322070917935394}, "resource_utilization": {"mean": 0.769776553553388, "std": 0.0273092783775486, "min": 0.7101980117909056, "max": 0.830037253496515}, "load_balance_degree": {"mean": 2.0352547100528926, "std": 0.07220450816471452, "min": 1.8777317156457043, "max": 2.1945813001187817}, "energy_consumption": {"mean": 7.407275581914943, "std": 0.24628115394187597, "min": 6.918594334270374, "max": 7.957180894311541}}}, "aggregated_results": {"makespan": {"mean": 12.760968711690495, "std": 0.4289641732856337}, "resource_utilization": {"mean": 0.754641815755396, "std": 0.011137038797240689}, "load_balance_degree": {"mean": 2.014952743171973, "std": 0.014356174283805563}, "energy_consumption": {"mean": 7.398197025639338, "std": 0.054254108938306775}}}, "PSO": {"algorithm": "PSO", "results_by_workflow": {"montage": {"makespan": {"mean": 14.087885520193627, "std": 0.7580343848356069, "min": 12.37433942351856, "max": 15.542021961270303}, "resource_utilization": {"mean": 0.7443706271198489, "std": 0.03024758198403299, "min": 0.6795202604245965, "max": 0.7975845873152358}, "load_balance_degree": {"mean": 2.0922850059584936, "std": 0.0850202304416062, "min": 1.9100028941664335, "max": 2.241859380561744}, "energy_consumption": {"mean": 7.877097280108264, "std": 0.423847182918834, "min": 6.918985484117905, "max": 8.690162817054363}}, "brain": {"makespan": {"mean": 13.18090807555574, "std": 0.704733902060459, "min": 11.622386055633568, "max": 14.141427250904968}, "resource_utilization": {"mean": 0.7236258996196593, "std": 0.029620603757740442, "min": 0.6597482482006811, "max": 0.7636238604874882}, "load_balance_degree": {"mean": 2.0132206495761507, "std": 0.08240834272147977, "min": 1.8355047787778462, "max": 2.1245001391609986}, "energy_consumption": {"mean": 7.44754873946511, "std": 0.3981926020470642, "min": 6.566944107495503, "max": 7.9902665349765245}}, "sipht": {"makespan": {"mean": 14.474452954734101, "std": 0.6531287234641342, "min": 12.838872835612069, "max": 15.56247938799412}, "resource_utilization": {"mean": 0.7516322900663802, "std": 0.02910592128122931, "min": 0.6804656064127285, "max": 0.8054064017697127}, "load_balance_degree": {"mean": 2.0708605990161764, "std": 0.08019121367715737, "min": 1.8747856258029043, "max": 2.2190164069095433}, "energy_consumption": {"mean": 7.626324675074956, "std": 0.344121585481103, "min": 6.76456740801066, "max": 8.19958591410443}}}, "aggregated_results": {"makespan": {"mean": 13.914415516827821, "std": 0.5421460259394691}, "resource_utilization": {"mean": 0.7398762722686295, "std": 0.011867010171340692}, "load_balance_degree": {"mean": 2.0587887515169405, "std": 0.033387525006326794}, "energy_consumption": {"mean": 7.650323564882776, "std": 0.17618162451699615}}}, "ACO": {"algorithm": "ACO", "results_by_workflow": {"montage": {"makespan": {"mean": 13.43008370654006, "std": 0.7919165569954838, "min": 11.917523386845128, "max": 15.371780276685344}, "resource_utilization": {"mean": 0.745922160260428, "std": 0.03435562535962201, "min": 0.6753515383786567, "max": 0.8241946816924512}, "load_balance_degree": {"mean": 2.108473306336143, "std": 0.09711190101653155, "min": 1.908993681817003, "max": 2.329723633583996}, "energy_consumption": {"mean": 7.68859810708985, "std": 0.4533648690606117, "min": 6.822671381316542, "max": 8.800201347990868}}, "brain": {"makespan": {"mean": 12.92301281410163, "std": 0.5601837420221729, "min": 11.88854488064529, "max": 13.924695989936898}, "resource_utilization": {"mean": 0.7459871215642625, "std": 0.02618552643527676, "min": 0.6991474965704702, "max": 0.7946157450259022}, "load_balance_degree": {"mean": 2.087140022836394, "std": 0.07326247151224916, "min": 1.9560910366632855, "max": 2.223194310301041}, "energy_consumption": {"mean": 7.476181625657544, "std": 0.3240757754668676, "min": 6.877724418527098, "max": 8.05567229564575}}, "sipht": {"makespan": {"mean": 13.882720158119232, "std": 0.7288144939017334, "min": 12.51439883112988, "max": 15.282329590470617}, "resource_utilization": {"mean": 0.757628599653864, "std": 0.031569878767194845, "min": 0.7015520190465596, "max": 0.8309162202700919}, "load_balance_degree": {"mean": 2.099156310169676, "std": 0.08747044429912676, "min": 1.9437853169305868, "max": 2.3022138126770546}, "energy_consumption": {"mean": 7.489205484212735, "std": 0.3931680133673492, "min": 6.751047582192314, "max": 8.244242142528687}}}, "aggregated_results": {"makespan": {"mean": 13.41193889292031, "std": 0.39200890486169787}, "resource_utilization": {"mean": 0.7498459604928515, "std": 0.005503220828184331}, "load_balance_degree": {"mean": 2.0982565464474043, "std": 0.008732484460797541}, "energy_consumption": {"mean": 7.5513284056533765, "std": 0.09720985348636334}}}, "CGWSA": {"algorithm": "CGWSA", "results_by_workflow": {"montage": {"makespan": {"mean": 9.343081934339281, "std": 0.3840916311701038, "min": 8.119462366963157, "max": 9.84260097291487}, "resource_utilization": {"mean": 0.8200363845989933, "std": 0.030221923170901624, "min": 0.7449381423323699, "max": 0.8753426752099738}, "load_balance_degree": {"mean": 1.6780744553135498, "std": 0.06184437448874748, "min": 1.5243978083338008, "max": 1.7912500109784586}, "energy_consumption": {"mean": 6.121675596076218, "std": 0.2516604672543106, "min": 5.319948489632144, "max": 6.448965191705767}}, "brain": {"makespan": {"mean": 8.49506110859321, "std": 0.46903282406685354, "min": 7.7848654533826815, "max": 9.651364815102681}, "resource_utilization": {"mean": 0.7745371744778249, "std": 0.036890027181837697, "min": 0.703813674737404, "max": 0.8580339618025812}, "load_balance_degree": {"mean": 1.5687943969768248, "std": 0.07471929024737424, "min": 1.4255467469177239, "max": 1.7379138355744017}, "energy_consumption": {"mean": 5.624634275755762, "std": 0.3105496317186379, "min": 5.154409191589008, "max": 6.390230353015014}}, "sipht": {"makespan": {"mean": 9.67549513554639, "std": 0.40974904428683195, "min": 8.841036855957825, "max": 10.278072625210276}, "resource_utilization": {"mean": 0.8343003923351352, "std": 0.0316926066685965, "min": 0.7638713337731888, "max": 0.9157831637170655}, "load_balance_degree": {"mean": 1.6734562880402961, "std": 0.06356965956291462, "min": 1.532188284340373, "max": 1.8368960483338126}, "energy_consumption": {"mean": 5.973736875185522, "std": 0.2529827095293193, "min": 5.45853490094562, "max": 6.34577358439096}}}, "aggregated_results": {"makespan": {"mean": 9.171212726159629, "std": 0.49699791517835745}, "resource_utilization": {"mean": 0.8096246504706511, "std": 0.02548481518910882}, "load_balance_degree": {"mean": 1.6401083801102236, "std": 0.05046183382850815}, "energy_consumption": {"mean": 5.906682249005834, "std": 0.20838227803014497}}}, "DQN": {"algorithm": "DQN", "results_by_workflow": {"montage": {"makespan": {"mean": 11.219142243173128, "std": 0.8054888147620204, "min": 9.381545529033344, "max": 12.736669508698391}, "resource_utilization": {"mean": 0.7861294008549032, "std": 0.04827940086413523, "min": 0.6470257335861874, "max": 0.8615289499170944}, "load_balance_degree": {"mean": 1.8807399590072997, "std": 0.11550388308001969, "min": 1.5479476411112583, "max": 2.0611262219535544}, "energy_consumption": {"mean": 7.093135345194051, "std": 0.5092582889412594, "min": 5.931342231178695, "max": 8.052569324288385}}, "brain": {"makespan": {"mean": 10.517789349770027, "std": 0.6413175612745967, "min": 9.267333864761016, "max": 11.61326149250551}, "resource_utilization": {"mean": 0.7657429078902788, "std": 0.03867315392935728, "min": 0.6976339075245152, "max": 0.829505001200541}, "load_balance_degree": {"mean": 1.8132736670566367, "std": 0.09157774877123569, "min": 1.6519920468415055, "max": 1.964261842806706}, "energy_consumption": {"mean": 6.719712443926747, "std": 0.40973149905306755, "min": 5.920808700586876, "max": 7.41959885015865}}, "sipht": {"makespan": {"mean": 11.518598490433536, "std": 0.6299265443248347, "min": 10.322940308448908, "max": 13.031235949108796}, "resource_utilization": {"mean": 0.7932073827778885, "std": 0.03896431034914295, "min": 0.7123666689595612, "max": 0.874526954758345}, "load_balance_degree": {"mean": 1.8600956685245102, "std": 0.09137250419135402, "min": 1.6705217123577327, "max": 2.05079256178511}, "energy_consumption": {"mean": 6.862320047300886, "std": 0.3752850276912975, "min": 6.149994748457726, "max": 7.7634889148142845}}}, "aggregated_results": {"makespan": {"mean": 11.085176694458896, "std": 0.4194161101584255}, "resource_utilization": {"mean": 0.7816932305076901, "std": 0.011642853336565333}, "load_balance_degree": {"mean": 1.8513697648628156, "std": 0.028225652439201547}, "energy_consumption": {"mean": 6.891722612140562, "std": 0.15386043223667817}}}, "A3C": {"algorithm": "A3C", "results_by_workflow": {"montage": {"makespan": {"mean": 10.864959116616802, "std": 0.5183853011225275, "min": 10.163094463402093, "max": 11.924109708784458}, "resource_utilization": {"mean": 0.8219237991655477, "std": 0.035967915827369454, "min": 0.7535001441355739, "max": 0.9199229722528984}, "load_balance_degree": {"mean": 1.7859085018905727, "std": 0.07815250846440773, "min": 1.6372348810847035, "max": 1.9988449767470384}, "energy_consumption": {"mean": 6.924247609465253, "std": 0.33036738965634344, "min": 6.476948673958223, "max": 7.599245175232832}}, "brain": {"makespan": {"mean": 10.064858287619682, "std": 0.5134656371860687, "min": 9.121545313988015, "max": 11.290057186956}, "resource_utilization": {"mean": 0.7910454962147181, "std": 0.03643406103824836, "min": 0.7234643781620329, "max": 0.8567212008607388}, "load_balance_degree": {"mean": 1.7012759777497692, "std": 0.07835755732488986, "min": 1.5559314517488316, "max": 1.8425225927304776}, "energy_consumption": {"mean": 6.4818620728635405, "std": 0.3306766319292317, "min": 5.8743597701093835, "max": 7.270901525817469}}, "sipht": {"makespan": {"mean": 11.135228853604241, "std": 0.5791844656028137, "min": 9.935302838676746, "max": 12.398819059936741}, "resource_utilization": {"mean": 0.8274504677323924, "std": 0.03728470790477671, "min": 0.7413662285981126, "max": 0.9104482036648885}, "load_balance_degree": {"mean": 1.7623147475576584, "std": 0.07940945489950242, "min": 1.578971417564297, "max": 1.939084402965043}, "energy_consumption": {"mean": 6.687077813461417, "std": 0.3478196668208087, "min": 5.966482059417294, "max": 7.445906046375391}}}, "aggregated_results": {"makespan": {"mean": 10.688348752613576, "std": 0.454471636545517}, "resource_utilization": {"mean": 0.8134732543708859, "std": 0.016018515709340996}, "load_balance_degree": {"mean": 1.7498330757326668, "std": 0.03566053032201486}, "energy_consumption": {"mean": 6.697729165263404, "std": 0.1807601158441826}}}, "PPO": {"algorithm": "PPO", "results_by_workflow": {"montage": {"makespan": {"mean": 10.46152211840713, "std": 0.5432346825121981, "min": 9.317778724372229, "max": 11.27315098503175}, "resource_utilization": {"mean": 0.8365680498895769, "std": 0.034819039144806524, "min": 0.7619866335305502, "max": 0.8919612736217838}, "load_balance_degree": {"mean": 1.733610898566352, "std": 0.07215511726393643, "min": 1.5790566381596944, "max": 1.8484016754571906}, "energy_consumption": {"mean": 6.576392047354579, "std": 0.34149182169528897, "min": 5.857404420543471, "max": 7.086603617283015}}, "brain": {"makespan": {"mean": 9.7810490195998, "std": 0.41345274921860287, "min": 8.720828575773172, "max": 10.399325274241656}, "resource_utilization": {"mean": 0.812966494848894, "std": 0.032389522446264606, "min": 0.7206925824147185, "max": 0.878576421267921}, "load_balance_degree": {"mean": 1.6675108187925929, "std": 0.06643555353989167, "min": 1.4782437970257145, "max": 1.8020861829891806}, "energy_consumption": {"mean": 6.213350899145146, "std": 0.262643301957048, "min": 5.53985241910054, "max": 6.606107066198509}}, "sipht": {"makespan": {"mean": 10.718793768033168, "std": 0.5282760335029495, "min": 9.772541180656628, "max": 11.532338720383525}, "resource_utilization": {"mean": 0.8420967299868314, "std": 0.03268348220735828, "min": 0.7935638289875455, "max": 0.909925913267037}, "load_balance_degree": {"mean": 1.710512121939135, "std": 0.06638844507060682, "min": 1.6119294858642401, "max": 1.848290403329489}, "energy_consumption": {"mean": 6.349382188510942, "std": 0.312929468588546, "min": 5.788860225485672, "max": 6.8312934876546505}}}, "aggregated_results": {"makespan": {"mean": 10.320454968680032, "std": 0.3956145083728901}, "resource_utilization": {"mean": 0.8305437582417673, "std": 0.012632279213487953}, "load_balance_degree": {"mean": 1.7038779464326934, "std": 0.02738995375451902}, "energy_consumption": {"mean": 6.379708378336889, "std": 0.1497541922959653}}}, "BasicGNN": {"algorithm": "BasicGNN", "results_by_workflow": {"montage": {"makespan": {"mean": 9.766227014961164, "std": 0.6306988301163263, "min": 7.942492756217727, "max": 10.959737520626174}, "resource_utilization": {"mean": 0.8358822627028004, "std": 0.052821660041839066, "min": 0.7106013457761259, "max": 0.9664549784505513}, "load_balance_degree": {"mean": 1.6419115874519297, "std": 0.10375683222504102, "min": 1.3958240720602473, "max": 1.8983937076707256}, "energy_consumption": {"mean": 6.191268659791331, "std": 0.3998295242046241, "min": 5.035118107213692, "max": 6.947890861746654}}, "brain": {"makespan": {"mean": 9.400409357838253, "std": 0.5368458528398721, "min": 8.397402239977342, "max": 10.571079684460756}, "resource_utilization": {"mean": 0.8356456192446415, "std": 0.03878902110619836, "min": 0.7718107520226987, "max": 0.9328859136962101}, "load_balance_degree": {"mean": 1.6246972954337326, "std": 0.07541524329503217, "min": 1.5005868666330529, "max": 1.8137559582978429}, "energy_consumption": {"mean": 6.022089817872489, "std": 0.3439141659781422, "min": 5.379543443369406, "max": 6.7720445895932}}, "sipht": {"makespan": {"mean": 10.19260146862765, "std": 0.6684252227555405, "min": 8.834311588369303, "max": 11.197200706687433}, "resource_utilization": {"mean": 0.8567430766224723, "std": 0.04900930353890823, "min": 0.7675911265358435, "max": 0.9339807573888644}, "load_balance_degree": {"mean": 1.6495636676836498, "std": 0.0943619723371129, "min": 1.4779114865585814, "max": 1.7982762461040902}, "energy_consumption": {"mean": 6.088784658976483, "std": 0.3992991636643332, "min": 5.277378992737521, "max": 6.6889050941710755}}}, "aggregated_results": {"makespan": {"mean": 9.786412613809022, "std": 0.32372589114854067}, "resource_utilization": {"mean": 0.8427569861899714, "std": 0.009890131250262953}, "load_balance_degree": {"mean": 1.6387241835231041, "std": 0.010398838879797508}, "energy_consumption": {"mean": 6.1007143788801015, "std": 0.06958021175106327}}}, "TLF-GNN": {"algorithm": "TLF-GNN", "results_by_workflow": {"montage": {"makespan": {"mean": 8.484730840327463, "std": 0.48184844254484765, "min": 7.659458266055729, "max": 9.582365622938251}, "resource_utilization": {"mean": 0.8815915105256318, "std": 0.03844083756288077, "min": 0.8162049798791504, "max": 0.9667304560942673}, "load_balance_degree": {"mean": 1.4419594476758322, "std": 0.06287507109422912, "min": 1.3350111337563577, "max": 1.5812154471518878}, "energy_consumption": {"mean": 5.493710616039366, "std": 0.3119882002088943, "min": 4.959361467230329, "max": 6.204409396147071}}, "brain": {"makespan": {"mean": 7.930434290643309, "std": 0.48442262984683243, "min": 6.8749746317116065, "max": 8.842847685538024}, "resource_utilization": {"mean": 0.8561955260244568, "std": 0.04382452380508037, "min": 0.7674493780381656, "max": 0.9220789753374437}, "load_balance_degree": {"mean": 1.3861309483073163, "std": 0.07094936482921707, "min": 1.2424560767063808, "max": 1.4927924354304793}, "energy_consumption": {"mean": 5.188864238633714, "std": 0.3169565711379503, "min": 4.498279501551555, "max": 5.785854146387621}}, "sipht": {"makespan": {"mean": 8.688217580622434, "std": 0.38276754433904175, "min": 7.937498785982224, "max": 9.31635775663141}, "resource_utilization": {"mean": 0.8871714659942243, "std": 0.03205565615561256, "min": 0.8278801479824904, "max": 0.9552794809565014}, "load_balance_degree": {"mean": 1.4223518221789953, "std": 0.05139302005501576, "min": 1.3272934062516137, "max": 1.5315455495471613}, "energy_consumption": {"mean": 5.300918584746117, "std": 0.23353692176745633, "min": 4.842884566433537, "max": 5.684164043545173}}}, "aggregated_results": {"makespan": {"mean": 8.367794237197735, "std": 0.32022336527964973}, "resource_utilization": {"mean": 0.8749861675147711, "std": 0.013480853866898419}, "load_balance_degree": {"mean": 1.4168140727207146, "std": 0.023125820161774585}, "energy_consumption": {"mean": 5.327831146473066, "std": 0.12589954444812324}}}}, "workflow_type_results": {}, "statistical_tests": {}}