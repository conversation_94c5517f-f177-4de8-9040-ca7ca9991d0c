#!/usr/bin/env python3
"""
TLF-GNN (Three-Layer Fusion Graph Neural Network) 调度器实现
包含图着色、DAG Transformer、PINN约束、GAT决策四个核心组件
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import networkx as nx
import numpy as np
from typing import Dict, List, Tuple
import math

class GraphColoring:
    """改进的资源感知图着色算法"""
    
    def __init__(self):
        self.name = "ResourceAwareColoring"
    
    def color_workflow(self, workflow_dag: nx.DiGraph) -> Dict[int, int]:
        """对工作流进行资源感知图着色"""
        
        # 1. 分析任务资源主导性
        resource_types = {}
        for task_id in workflow_dag.nodes():
            task_data = workflow_dag.nodes[task_id]
            cpu_demand = task_data.get('cpu_demand', 1.0)
            memory_demand = task_data.get('memory_demand', 1000.0)
            io_ops = task_data.get('io_operations', 50)
            
            # 确定主导资源类型
            if cpu_demand > 2.5:
                resource_types[task_id] = 'compute'
            elif memory_demand > 1500:
                resource_types[task_id] = 'memory'
            elif io_ops > 100:
                resource_types[task_id] = 'io'
            else:
                resource_types[task_id] = 'network'
        
        # 2. 构建冲突图（依赖关系 + 资源竞争）
        conflict_graph = nx.Graph()
        conflict_graph.add_nodes_from(workflow_dag.nodes())
        
        # 添加依赖关系冲突
        for edge in workflow_dag.edges():
            conflict_graph.add_edge(edge[0], edge[1])
        
        # 添加资源竞争冲突
        tasks = list(workflow_dag.nodes())
        for i in range(len(tasks)):
            for j in range(i+1, len(tasks)):
                task1, task2 = tasks[i], tasks[j]
                # 相同资源类型且强度相近的任务可以并行
                if (resource_types[task1] == resource_types[task2] and 
                    not conflict_graph.has_edge(task1, task2)):
                    # 不添加冲突边，允许并行
                    pass
                elif resource_types[task1] != resource_types[task2]:
                    # 不同资源类型，可能可以并行
                    pass
        
        # 3. 贪心着色算法
        coloring = {}
        color_to_tasks = {}
        
        # 按度数排序（度数高的先着色）
        nodes_by_degree = sorted(conflict_graph.nodes(), 
                               key=lambda x: conflict_graph.degree(x), reverse=True)
        
        for node in nodes_by_degree:
            # 找到邻居使用的颜色
            neighbor_colors = set()
            for neighbor in conflict_graph.neighbors(node):
                if neighbor in coloring:
                    neighbor_colors.add(coloring[neighbor])
            
            # 优先选择相同资源类型已使用的颜色
            preferred_color = None
            node_resource_type = resource_types[node]
            
            for color, tasks_in_color in color_to_tasks.items():
                if color not in neighbor_colors:
                    # 检查是否有相同资源类型的任务
                    same_resource_tasks = [t for t in tasks_in_color 
                                         if resource_types[t] == node_resource_type]
                    if same_resource_tasks:
                        preferred_color = color
                        break
            
            if preferred_color is not None:
                chosen_color = preferred_color
            else:
                # 选择最小可用颜色
                chosen_color = 0
                while chosen_color in neighbor_colors:
                    chosen_color += 1
            
            coloring[node] = chosen_color
            if chosen_color not in color_to_tasks:
                color_to_tasks[chosen_color] = []
            color_to_tasks[chosen_color].append(node)
        
        return coloring

class DAGTransformer(nn.Module):
    """DAG结构感知的Transformer层"""
    
    def __init__(self, d_model=128, num_heads=8, num_layers=4):
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        
        # DAG位置编码
        self.topo_embedding = nn.Embedding(100, d_model//4)  # 拓扑位置
        self.depth_embedding = nn.Embedding(20, d_model//4)   # 深度
        self.critical_embedding = nn.Embedding(2, d_model//4) # 是否关键路径
        self.dep_embedding = nn.Embedding(20, d_model//4)     # 依赖数量
        
        # Transformer层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, nhead=num_heads, dim_feedforward=d_model*4,
            dropout=0.1, activation='relu', batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 距离偏置
        self.distance_bias = nn.Embedding(10, num_heads)
    
    def forward(self, x, adjacency_matrix):
        """
        x: [batch_size, num_tasks, d_model]
        adjacency_matrix: [batch_size, num_tasks, num_tasks]
        """
        batch_size, num_tasks, _ = x.shape
        
        # 计算DAG信息
        dag_info = self.compute_dag_info(adjacency_matrix)
        
        # DAG位置编码
        topo_enc = self.topo_embedding(dag_info['topo_order'])
        depth_enc = self.depth_embedding(dag_info['depth'])
        critical_enc = self.critical_embedding(dag_info['critical_path'])
        dep_enc = self.dep_embedding(dag_info['dependency_count'])
        
        # 拼接DAG编码
        dag_encoding = torch.cat([topo_enc, depth_enc, critical_enc, dep_enc], dim=-1)
        
        # 添加到输入特征
        enhanced_x = x + dag_encoding
        
        # Transformer处理
        output = self.transformer(enhanced_x)
        
        return output
    
    def compute_dag_info(self, adjacency_matrix):
        """计算DAG结构信息"""
        batch_size, num_tasks, _ = adjacency_matrix.shape
        device = adjacency_matrix.device
        
        # 简化实现：使用随机值模拟DAG信息
        topo_order = torch.randint(0, num_tasks, (batch_size, num_tasks), device=device)
        depth = torch.randint(0, 10, (batch_size, num_tasks), device=device)
        critical_path = torch.randint(0, 2, (batch_size, num_tasks), device=device)
        dependency_count = torch.randint(0, 10, (batch_size, num_tasks), device=device)
        
        return {
            'topo_order': topo_order,
            'depth': depth,
            'critical_path': critical_path,
            'dependency_count': dependency_count
        }

class PINNConstraintLayer(nn.Module):
    """物理信息神经网络约束层"""
    
    def __init__(self, d_model=128):
        super().__init__()
        self.d_model = d_model
        
        # 约束编码器
        self.dependency_encoder = nn.Linear(1, d_model//4)
        self.resource_encoder = nn.Linear(4, d_model//4)
        self.temporal_encoder = nn.Linear(1, d_model//4)
        self.communication_encoder = nn.Linear(1, d_model//4)
        
        # 约束融合
        self.constraint_fusion = nn.MultiheadAttention(d_model, num_heads=8, batch_first=True)
        
        # 约束感知变换
        self.constraint_transform = nn.Sequential(
            nn.Linear(d_model, d_model*2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model*2, d_model)
        )
        
        # 约束验证器
        self.constraint_validator = nn.Linear(d_model, 1)
    
    def forward(self, task_features, constraint_data):
        """
        task_features: [batch_size, num_tasks, d_model]
        constraint_data: dict with constraint information
        """
        batch_size, num_tasks, d_model = task_features.shape
        
        # 约束嵌入（简化实现）
        constraint_embeddings = []
        
        # 依赖约束
        dep_emb = self.dependency_encoder(torch.ones(batch_size, num_tasks, 1, device=task_features.device))
        constraint_embeddings.append(dep_emb)
        
        # 资源约束
        resource_emb = self.resource_encoder(torch.ones(batch_size, num_tasks, 4, device=task_features.device))
        constraint_embeddings.append(resource_emb)
        
        # 时间约束
        temporal_emb = self.temporal_encoder(torch.ones(batch_size, num_tasks, 1, device=task_features.device))
        constraint_embeddings.append(temporal_emb)
        
        # 通信约束
        comm_emb = self.communication_encoder(torch.ones(batch_size, num_tasks, 1, device=task_features.device))
        constraint_embeddings.append(comm_emb)
        
        # 拼接约束嵌入
        combined_constraints = torch.cat(constraint_embeddings, dim=-1)
        
        # 约束融合
        fused_features, _ = self.constraint_fusion(task_features, combined_constraints, combined_constraints)
        
        # 约束感知变换
        constraint_aware_features = self.constraint_transform(fused_features)
        
        # 约束验证
        constraint_validity = torch.sigmoid(self.constraint_validator(constraint_aware_features))
        
        return constraint_aware_features, constraint_validity

class GATDecisionLayer(nn.Module):
    """图注意力决策层"""
    
    def __init__(self, task_dim=128, node_dim=32, hidden_dim=64, num_heads=4):
        super().__init__()
        self.task_dim = task_dim
        self.node_dim = node_dim
        self.hidden_dim = hidden_dim
        
        # 节点特征投影
        self.node_projection = nn.Linear(node_dim, task_dim)
        
        # 兼容性网络
        self.compatibility_net = nn.Sequential(
            nn.Linear(task_dim + task_dim + task_dim, hidden_dim*2),  # task + node + interaction
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim*2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
        
        # GAT层
        self.gat_layers = nn.ModuleList([
            nn.MultiheadAttention(task_dim, num_heads, batch_first=True)
            for _ in range(3)
        ])
        
        # 决策网络
        self.decision_net = nn.Sequential(
            nn.Linear(task_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, 1)
        )
    
    def forward(self, task_features, node_features):
        """
        task_features: [batch_size, num_tasks, task_dim]
        node_features: [batch_size, num_nodes, node_dim]
        """
        batch_size, num_tasks, task_dim = task_features.shape
        batch_size, num_nodes, node_dim = node_features.shape
        
        # 投影节点特征
        projected_node_features = self.node_projection(node_features)  # [batch_size, num_nodes, task_dim]
        
        # 计算任务-节点兼容性
        compatibility_scores = torch.zeros(batch_size, num_tasks, num_nodes, device=task_features.device)
        
        for i in range(num_tasks):
            for j in range(num_nodes):
                task_feat = task_features[:, i, :]  # [batch_size, task_dim]
                node_feat = projected_node_features[:, j, :]  # [batch_size, task_dim]
                interaction_feat = task_feat * node_feat  # [batch_size, task_dim]
                
                combined_feat = torch.cat([task_feat, node_feat, interaction_feat], dim=-1)  # [batch_size, task_dim*3]
                compatibility = self.compatibility_net(combined_feat)  # [batch_size, 1]
                compatibility_scores[:, i, j] = compatibility.squeeze(-1)
        
        # GAT处理
        x = task_features
        for gat_layer in self.gat_layers:
            x, _ = gat_layer(x, x, x)
            x = F.relu(x)
        
        # 决策
        decision_logits = self.decision_net(x)  # [batch_size, num_tasks, 1]
        
        # 结合兼容性分数
        assignment_logits = decision_logits.squeeze(-1).unsqueeze(-1) + compatibility_scores  # [batch_size, num_tasks, num_nodes]
        
        # Softmax归一化
        assignment_probs = F.softmax(assignment_logits, dim=-1)
        
        return assignment_probs

class TLFGNNScheduler:
    """TLF-GNN完整调度器"""
    
    def __init__(self, task_feature_dim=128, node_feature_dim=32):
        self.name = "TLF-GNN"
        
        # 初始化各组件
        self.graph_coloring = GraphColoring()
        self.dag_transformer = DAGTransformer(d_model=task_feature_dim)
        self.pinn_constraint = PINNConstraintLayer(d_model=task_feature_dim)
        self.gat_decision = GATDecisionLayer(task_dim=task_feature_dim, node_dim=node_feature_dim)
        
        # 特征提取器
        self.task_feature_extractor = nn.Linear(16, task_feature_dim)  # 简化的任务特征提取
        self.node_feature_extractor = nn.Linear(8, node_feature_dim)   # 简化的节点特征提取
    
    def schedule(self, workflow_dag: nx.DiGraph, nodes: list) -> dict:
        """执行调度"""
        
        # 1. 图着色
        coloring = self.graph_coloring.color_workflow(workflow_dag)
        
        # 2. 特征提取
        task_features = self.extract_task_features(workflow_dag)
        node_features = self.extract_node_features(nodes)
        
        # 3. 构建邻接矩阵
        adjacency_matrix = self.build_adjacency_matrix(workflow_dag)
        
        # 4. DAG Transformer
        with torch.no_grad():
            enhanced_features = self.dag_transformer(task_features, adjacency_matrix)
            
            # 5. PINN约束增强
            constraint_data = {}  # 简化
            constraint_features, constraint_validity = self.pinn_constraint(enhanced_features, constraint_data)
            
            # 6. GAT决策
            assignment_probs = self.gat_decision(constraint_features, node_features)
            
            # 7. 转换为离散分配
            assignment = {}
            for task_idx in range(len(workflow_dag.nodes())):
                node_idx = torch.argmax(assignment_probs[0, task_idx]).item()
                assignment[task_idx] = node_idx
        
        return assignment
    
    def extract_task_features(self, workflow_dag: nx.DiGraph):
        """提取任务特征"""
        num_tasks = len(workflow_dag.nodes())
        features = torch.zeros(1, num_tasks, 16)  # batch_size=1
        
        for i, task_id in enumerate(workflow_dag.nodes()):
            task_data = workflow_dag.nodes[task_id]
            features[0, i, 0] = task_data.get('runtime', 1.0) / 100.0  # 归一化
            features[0, i, 1] = task_data.get('cpu_demand', 1.0) / 8.0
            features[0, i, 2] = task_data.get('memory_demand', 1000.0) / 16384.0
            features[0, i, 3] = task_data.get('io_operations', 50) / 200.0
            # 其他特征设为随机值
            features[0, i, 4:] = torch.randn(12) * 0.1
        
        return self.task_feature_extractor(features)
    
    def extract_node_features(self, nodes: list):
        """提取节点特征"""
        num_nodes = len(nodes)
        features = torch.zeros(1, num_nodes, 8)  # batch_size=1
        
        for i, node in enumerate(nodes):
            features[0, i, 0] = node.get('cpu_capacity', 1.0) / 16.0
            features[0, i, 1] = node.get('memory_capacity', 1000.0) / 65536.0
            features[0, i, 2] = node.get('io_capacity', 100.0) / 5000.0
            features[0, i, 3] = node.get('network_capacity', 100.0) / 3000.0
            features[0, i, 4] = node.get('energy_efficiency', 0.8)
            features[0, i, 5] = node.get('cost_per_hour', 0.1) / 0.2
            # 其他特征
            features[0, i, 6:] = torch.randn(2) * 0.1
        
        return self.node_feature_extractor(features)
    
    def build_adjacency_matrix(self, workflow_dag: nx.DiGraph):
        """构建邻接矩阵"""
        num_tasks = len(workflow_dag.nodes())
        adj_matrix = torch.zeros(1, num_tasks, num_tasks)  # batch_size=1
        
        for edge in workflow_dag.edges():
            source, target = edge
            adj_matrix[0, source, target] = 1.0
        
        return adj_matrix
