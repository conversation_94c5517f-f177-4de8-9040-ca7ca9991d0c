#!/usr/bin/env python3
"""
TLF-GNN实验数据生成脚本
根据核心实验设计文档生成所需的实验数据
"""

import os
import json
import random
import numpy as np
import networkx as nx
from datetime import datetime
from typing import Dict, List

class ExperimentDataGenerator:
    """实验数据生成器"""

    def __init__(self, output_dir: str = "New_experiments/data"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        # 实验说明文档要求的配置
        self.config = {
            'workflow_types': ['montage', 'brain', 'sipht', 'epigenomics', 'cybershake'],
            'scale_configurations': {
                'small': {'tasks': [10, 30], 'nodes': [4, 8], 'count': 150},
                'medium': {'tasks': [30, 70], 'nodes': [8, 12], 'count': 300},
                'large': {'tasks': [70, 100], 'nodes': [12, 16], 'count': 150}
            },
            'total_workflows': 600
        }
        # 设置随机种子确保可重现性
        random.seed(42)
        np.random.seed(42)
    
    def generate_workflow_by_type(self, workflow_type: str, num_tasks: int) -> Dict:
        """根据类型生成工作流"""
        
        if workflow_type == 'montage':
            return self._generate_montage_workflow(num_tasks)
        elif workflow_type == 'brain':
            return self._generate_brain_workflow(num_tasks)
        elif workflow_type == 'sipht':
            return self._generate_sipht_workflow(num_tasks)
        elif workflow_type == 'epigenomics':
            return self._generate_epigenomics_workflow(num_tasks)
        elif workflow_type == 'cybershake':
            return self._generate_cybershake_workflow(num_tasks)
        else:
            raise ValueError(f"Unknown workflow type: {workflow_type}")
    
    def _generate_montage_workflow(self, num_tasks: int) -> Dict:
        """生成Montage天文图像拼接工作流"""
        
        # 创建DAG结构 - 典型的扇入扇出结构
        dag = nx.DiGraph()
        
        # 添加任务节点
        for i in range(num_tasks):
            # Montage工作流特征：CPU和I/O密集型
            task_data = {
                'task_id': f'task_{i}',
                'runtime': random.uniform(20, 80),  # 执行时间
                'input_size': random.uniform(100, 1000),  # 输入数据大小(MB)
                'output_size': random.uniform(50, 500),   # 输出数据大小(MB)
                'cpu_demand': random.uniform(1.5, 4.0),   # CPU需求
                'memory_demand': random.uniform(512, 2048), # 内存需求(MB)
                'io_operations': random.randint(50, 200),   # I/O操作次数
                'network_transfer': random.uniform(10, 100), # 网络传输(MB)
                'task_type': random.choice(['compute', 'io']),
                'priority': random.randint(1, 5)
            }
            dag.add_node(i, **task_data)
        
        # 添加依赖关系 - 扇入扇出结构
        self._add_fanin_fanout_edges(dag, num_tasks)
        
        return {
            'type': 'montage',
            'num_tasks': num_tasks,
            'dag': nx.node_link_data(dag),
            'metadata': {
                'description': 'Astronomical image mosaic workflow',
                'characteristics': 'CPU and I/O intensive',
                'parallelism_degree': self._calculate_parallelism(dag)
            }
        }
    
    def _generate_brain_workflow(self, num_tasks: int) -> Dict:
        """生成Brain神经影像处理工作流"""
        
        dag = nx.DiGraph()
        
        # 添加任务节点
        for i in range(num_tasks):
            # Brain工作流特征：内存密集型
            task_data = {
                'task_id': f'task_{i}',
                'runtime': random.uniform(30, 120),
                'input_size': random.uniform(200, 2000),
                'output_size': random.uniform(100, 1000),
                'cpu_demand': random.uniform(1.0, 3.0),
                'memory_demand': random.uniform(1024, 8192),  # 更高内存需求
                'io_operations': random.randint(30, 150),
                'network_transfer': random.uniform(20, 200),
                'task_type': random.choice(['compute', 'memory']),
                'priority': random.randint(1, 5)
            }
            dag.add_node(i, **task_data)
        
        # 添加流水线结构的依赖关系
        self._add_pipeline_edges(dag, num_tasks)
        
        return {
            'type': 'brain',
            'num_tasks': num_tasks,
            'dag': nx.node_link_data(dag),
            'metadata': {
                'description': 'Brain neuroimaging processing workflow',
                'characteristics': 'Memory intensive',
                'parallelism_degree': self._calculate_parallelism(dag)
            }
        }
    
    def _generate_sipht_workflow(self, num_tasks: int) -> Dict:
        """生成Sipht生物信息学分析工作流"""
        
        dag = nx.DiGraph()
        
        # 添加任务节点
        for i in range(num_tasks):
            # Sipht工作流特征：计算密集型
            task_data = {
                'task_id': f'task_{i}',
                'runtime': random.uniform(40, 150),
                'input_size': random.uniform(50, 500),
                'output_size': random.uniform(25, 250),
                'cpu_demand': random.uniform(2.0, 6.0),  # 更高CPU需求
                'memory_demand': random.uniform(256, 1024),
                'io_operations': random.randint(20, 100),
                'network_transfer': random.uniform(5, 50),
                'task_type': random.choice(['compute', 'network']),
                'priority': random.randint(1, 5)
            }
            dag.add_node(i, **task_data)
        
        # 添加树状结构的依赖关系
        self._add_tree_edges(dag, num_tasks)
        
        return {
            'type': 'sipht',
            'num_tasks': num_tasks,
            'dag': nx.node_link_data(dag),
            'metadata': {
                'description': 'Bioinformatics analysis workflow',
                'characteristics': 'Compute intensive',
                'parallelism_degree': self._calculate_parallelism(dag)
            }
        }
    def _generate_epigenomics_workflow(self, num_tasks: int) -> Dict:
        """生成Epigenomics表观基因组学数据流工作流"""
        dag = nx.DiGraph()
        # Epigenomics特征：数据流密集型，任务间数据依赖复杂
        for i in range(num_tasks):
            task_data = {
                'task_id': f'task_{i}',
                'runtime': random.uniform(30, 120),
                'input_size': random.uniform(100, 800),  # 输入数据量大
                'output_size': random.uniform(80, 600),
                'cpu_demand': random.uniform(1.0, 4.0),
                'memory_demand': random.uniform(512, 2048),
                'io_operations': random.randint(40, 200),  # I/O密集
                'network_transfer': random.uniform(20, 120),  # 网络传输量大
                'task_type': random.choice(['dataflow', 'aggregation', 'filter']),
                'priority': random.randint(1, 5)
            }
            dag.add_node(i, **task_data)
        # 添加数据流密集型的依赖关系（多扇入扇出+部分树结构）
        self._add_fanin_fanout_edges(dag, num_tasks)
        self._add_tree_edges(dag, num_tasks // 2)
        return {
            'type': 'epigenomics',
            'num_tasks': num_tasks,
            'dag': nx.node_link_data(dag),
            'metadata': {
                'description': 'Epigenomics dataflow workflow',
                'characteristics': 'Dataflow intensive',
                'parallelism_degree': self._calculate_parallelism(dag)
            }
        }

    def _generate_cybershake_workflow(self, num_tasks: int) -> Dict:
        """生成CyberShake地震模拟工作流"""
        dag = nx.DiGraph()
        # CyberShake特征：通信密集型，任务间有大量数据交换
        for i in range(num_tasks):
            task_data = {
                'task_id': f'task_{i}',
                'runtime': random.uniform(60, 200),
                'input_size': random.uniform(50, 400),
                'output_size': random.uniform(40, 300),
                'cpu_demand': random.uniform(2.0, 8.0),
                'memory_demand': random.uniform(256, 2048),
                'io_operations': random.randint(20, 80),
                'network_transfer': random.uniform(50, 200),  # 通信量极大
                'task_type': random.choice(['simulation', 'communication']),
                'priority': random.randint(1, 5)
            }
            dag.add_node(i, **task_data)
        # 添加通信密集型的依赖关系（多对多+部分流水线结构）
        self._add_fanin_fanout_edges(dag, num_tasks)
        self._add_pipeline_edges(dag, num_tasks // 2)
        # 增加部分全连接以模拟高通信
        for i in range(num_tasks // 4):
            src = random.randint(0, num_tasks - 2)
            tgt = random.randint(src + 1, num_tasks - 1)
            if not dag.has_edge(src, tgt):
                dag.add_edge(src, tgt, data_size=random.uniform(30, 150))
        return {
            'type': 'cybershake',
            'num_tasks': num_tasks,
            'dag': nx.node_link_data(dag),
            'metadata': {
                'description': 'CyberShake earthquake simulation workflow',
                'characteristics': 'Communication intensive',
                'parallelism_degree': self._calculate_parallelism(dag)
            }
        }
    def _add_fanin_fanout_edges(self, dag: nx.DiGraph, num_tasks: int):
        """添加扇入扇出结构的边"""
        if num_tasks < 3:
            return
        
        # 第一阶段：并行任务
        stage1_size = min(num_tasks // 3, 5)
        # 第二阶段：汇聚任务
        stage2_start = stage1_size
        stage2_size = min(num_tasks // 4, 3)
        # 第三阶段：最终任务
        stage3_start = stage2_start + stage2_size
        
        # 添加第一阶段到第二阶段的边
        for i in range(stage1_size):
            for j in range(stage2_start, stage2_start + stage2_size):
                if j < num_tasks:
                    dag.add_edge(i, j, data_size=random.uniform(10, 100))
        
        # 添加第二阶段到第三阶段的边
        for i in range(stage2_start, stage2_start + stage2_size):
            for j in range(stage3_start, num_tasks):
                if random.random() < 0.6:  # 60%概率添加边
                    dag.add_edge(i, j, data_size=random.uniform(10, 100))
    
    def _add_pipeline_edges(self, dag: nx.DiGraph, num_tasks: int):
        """添加流水线结构的边"""
        # 创建主要的流水线路径
        for i in range(num_tasks - 1):
            if random.random() < 0.8:  # 80%概率添加顺序边
                dag.add_edge(i, i + 1, data_size=random.uniform(10, 100))
        
        # 添加一些跳跃连接
        for i in range(num_tasks - 2):
            if random.random() < 0.3:  # 30%概率添加跳跃边
                j = random.randint(i + 2, min(i + 5, num_tasks - 1))
                dag.add_edge(i, j, data_size=random.uniform(5, 50))
    
    def _add_tree_edges(self, dag: nx.DiGraph, num_tasks: int):
        """添加树状结构的边"""
        # 创建近似平衡的树结构
        for i in range(1, num_tasks):
            parent = random.randint(0, i - 1)
            dag.add_edge(parent, i, data_size=random.uniform(10, 100))
        
        # 添加一些额外的依赖关系增加复杂度
        for i in range(num_tasks // 4):
            source = random.randint(0, num_tasks - 2)
            target = random.randint(source + 1, num_tasks - 1)
            if not dag.has_edge(source, target):
                dag.add_edge(source, target, data_size=random.uniform(5, 50))
    
    def _calculate_parallelism(self, dag: nx.DiGraph) -> float:
        """计算工作流的并行度"""
        if dag.number_of_nodes() == 0:
            return 0.0
        # 计算每一层的任务数量
        try:
            levels = list(nx.topological_generations(dag))
            avg_parallel = sum(len(level) for level in levels) / len(levels)
            return avg_parallel / dag.number_of_nodes()
        except Exception:
            return 0.5  # 默认值
    
    def generate_heterogeneous_nodes(self, num_nodes: int) -> List[Dict]:
        """生成异构计算节点"""
        nodes = []
        
        for i in range(num_nodes):
            # 随机生成不同类型的节点
            node_type = random.choice(['cpu_intensive', 'memory_intensive', 'io_intensive', 'balanced'])
            
            if node_type == 'cpu_intensive':
                node_data = {
                    'node_id': f'node_{i}',
                    'cpu_capacity': random.uniform(6.0, 16.0),
                    'memory_capacity': random.uniform(4096, 16384),
                    'io_capacity': random.uniform(500, 1500),
                    'network_capacity': random.uniform(500, 1500),
                    'energy_efficiency': random.uniform(0.7, 0.9),
                    'cost_per_hour': random.uniform(0.08, 0.15),
                    'node_type': node_type,
                    'availability': random.uniform(0.95, 0.99)
                }
            elif node_type == 'memory_intensive':
                node_data = {
                    'node_id': f'node_{i}',
                    'cpu_capacity': random.uniform(4.0, 8.0),
                    'memory_capacity': random.uniform(16384, 65536),
                    'io_capacity': random.uniform(300, 1000),
                    'network_capacity': random.uniform(800, 2000),
                    'energy_efficiency': random.uniform(0.6, 0.8),
                    'cost_per_hour': random.uniform(0.12, 0.20),
                    'node_type': node_type,
                    'availability': random.uniform(0.95, 0.99)
                }
            elif node_type == 'io_intensive':
                node_data = {
                    'node_id': f'node_{i}',
                    'cpu_capacity': random.uniform(2.0, 6.0),
                    'memory_capacity': random.uniform(2048, 8192),
                    'io_capacity': random.uniform(2000, 5000),
                    'network_capacity': random.uniform(1000, 3000),
                    'energy_efficiency': random.uniform(0.8, 0.95),
                    'cost_per_hour': random.uniform(0.06, 0.12),
                    'node_type': node_type,
                    'availability': random.uniform(0.95, 0.99)
                }
            else:  # balanced
                node_data = {
                    'node_id': f'node_{i}',
                    'cpu_capacity': random.uniform(4.0, 8.0),
                    'memory_capacity': random.uniform(8192, 16384),
                    'io_capacity': random.uniform(1000, 2000),
                    'network_capacity': random.uniform(1000, 2000),
                    'energy_efficiency': random.uniform(0.75, 0.85),
                    'cost_per_hour': random.uniform(0.10, 0.16),
                    'node_type': node_type,
                    'availability': random.uniform(0.95, 0.99)
                }
            
            nodes.append(node_data)
        
        return nodes
    
    def generate_experiment_data(self):
        """生成所有实验所需的数据"""
        print("🚀 开始生成实验数据...")
        all_data = {
            'generation_time': datetime.now().isoformat(),
            'config': self.config,
            'workflows_by_type': {},
            'workflows_by_scale': {},
            'nodes_by_scale': {}
        }
        log_info = {
            'generation_time': all_data['generation_time'],
            'output_dir': self.output_dir,
            'files': [],
            'workflows_by_type': {},
            'workflows_by_scale': {}
        }
        # 按工作流类型和规模生成数据
        for workflow_type in self.config['workflow_types']:
            print(f"📊 生成 {workflow_type} 工作流数据...")
            type_workflows = []
            for scale, scale_config in self.config['scale_configurations'].items():
                num_per_type_scale = scale_config['count'] // len(self.config['workflow_types'])
                for i in range(num_per_type_scale):
                    num_tasks = random.randint(scale_config['tasks'][0], scale_config['tasks'][1])
                    workflow = self.generate_workflow_by_type(workflow_type, num_tasks)
                    workflow['scale'] = scale
                    workflow['workflow_id'] = f"{workflow_type}_{scale}_{i}"
                    type_workflows.append(workflow)
            all_data['workflows_by_type'][workflow_type] = type_workflows
            # 保存单独的类型文件
            type_file = os.path.join(self.output_dir, f'{workflow_type}_workflows.json')
            with open(type_file, 'w') as f:
                json.dump(type_workflows, f, indent=2)
            log_info['files'].append(type_file)
            log_info['workflows_by_type'][workflow_type] = len(type_workflows)
        # 按规模生成数据，确保每种类型都包含
        for scale, scale_config in self.config['scale_configurations'].items():
            print(f"📏 生成 {scale} 规模数据...")
            scale_workflows = []
            scale_nodes = []
            for workflow_type in self.config['workflow_types']:
                num_per_type_scale = scale_config['count'] // len(self.config['workflow_types'])
                for i in range(num_per_type_scale):
                    num_tasks = random.randint(scale_config['tasks'][0], scale_config['tasks'][1])
                    num_nodes = random.randint(scale_config['nodes'][0], scale_config['nodes'][1])
                    workflow = self.generate_workflow_by_type(workflow_type, num_tasks)
                    workflow['scale'] = scale
                    workflow['workflow_id'] = f"{workflow_type}_{scale}_{i}"
                    nodes = self.generate_heterogeneous_nodes(num_nodes)
                    workflow['nodes'] = nodes  # 保证每个workflow都带有nodes信息
                    scale_workflows.append(workflow)
                    scale_nodes.append({
                        'workflow_id': workflow['workflow_id'],
                        'nodes': nodes
                    })
            all_data['workflows_by_scale'][scale] = scale_workflows
            all_data['nodes_by_scale'][scale] = scale_nodes
            # 保存单独的规模文件
            scale_file = os.path.join(self.output_dir, f'{scale}_scale_data.json')
            with open(scale_file, 'w') as f:
                json.dump({
                    'workflows': scale_workflows,
                    'nodes': scale_nodes
                }, f, indent=2)
            log_info['files'].append(scale_file)
            log_info['workflows_by_scale'][scale] = len(scale_workflows)
        # 保存完整数据
        all_file = os.path.join(self.output_dir, 'all_experiment_data.json')
        with open(all_file, 'w') as f:
            json.dump(all_data, f, indent=2)
        log_info['files'].append(all_file)
        # 生成数据统计
        self.generate_data_statistics(all_data)
        # 生成日志文件
        log_file = os.path.join(self.output_dir, 'data_generation_log.json')
        with open(log_file, 'w') as f:
            json.dump(log_info, f, indent=2)
        print("✅ 实验数据生成完成!")
        print(f"📁 数据保存位置: {self.output_dir}")
    
    def generate_data_statistics(self, all_data: Dict):
        """生成数据统计信息"""
        stats = {
            'generation_time': all_data['generation_time'],
            'total_workflows': 0,
            'workflows_by_type': {},
            'workflows_by_scale': {},
            'task_statistics': {},
            'node_statistics': {}
        }
        
        # 统计各类型工作流
        for workflow_type, workflows in all_data['workflows_by_type'].items():
            stats['workflows_by_type'][workflow_type] = len(workflows)
            stats['total_workflows'] += len(workflows)
        
        # 统计各规模工作流
        for scale, workflows in all_data['workflows_by_scale'].items():
            stats['workflows_by_scale'][scale] = len(workflows)
        
        # 统计任务数量
        all_workflows = []
        for workflows in all_data['workflows_by_type'].values():
            all_workflows.extend(workflows)
        
        task_counts = [w['num_tasks'] for w in all_workflows]
        stats['task_statistics'] = {
            'min_tasks': min(task_counts),
            'max_tasks': max(task_counts),
            'avg_tasks': sum(task_counts) / len(task_counts),
            'total_tasks': sum(task_counts)
        }
        
        # 统计节点数量
        all_nodes = []
        for nodes_list in all_data['nodes_by_scale'].values():
            all_nodes.extend(nodes_list)
        
        node_counts = [len(n['nodes']) for n in all_nodes]
        stats['node_statistics'] = {
            'min_nodes': min(node_counts),
            'max_nodes': max(node_counts),
            'avg_nodes': sum(node_counts) / len(node_counts),
            'total_node_instances': sum(node_counts)
        }
        
        # 保存统计信息
        with open(os.path.join(self.output_dir, 'data_statistics.json'), 'w') as f:
            json.dump(stats, f, indent=2)
        
        print("📊 数据统计:")
        print(f"  - 总工作流数量: {stats['total_workflows']}")
        print(f"  - 任务数量范围: {stats['task_statistics']['min_tasks']}-{stats['task_statistics']['max_tasks']}")
        print(f"  - 节点数量范围: {stats['node_statistics']['min_nodes']}-{stats['node_statistics']['max_nodes']}")

def main():
    """主函数"""
    print("🎯 TLF-GNN实验数据生成器")
    print("=" * 50)
    
    generator = ExperimentDataGenerator()
    generator.generate_experiment_data()
    
    print("🎉 数据生成完成!")

if __name__ == "__main__":
    main()
