{"experiment_info": {"name": "Ablation Study Experiment", "timestamp": "2025-07-31T00:34:00.583973", "configurations": ["TLF-GNN-Full", "TLF-GNN-NoColoring", "TLF-GNN-NoTransformer", "TLF-GNN-NoPINN", "TLF-GNN-NoGAT", "BasicGNN"], "num_workflows": 99}, "results_by_config": {"TLF-GNN-Full": {"configuration": {"graph_coloring": true, "dag_transformer": true, "pinn_constraint": true, "gat_decision": true, "description": "完整TLF-GNN方法"}, "results_by_workflow_type": {"montage": {"makespan": {"mean": 8.442581409609858, "std": 0.11069888407040844}, "resource_utilization": {"mean": 0.8807009384125392, "std": 0.011547725316697309}, "load_balance_degree": {"mean": 1.4405027992655666, "std": 0.018887831178919838}, "energy_consumption": {"mean": 5.466419617733001, "std": 0.07167553644846598}, "constraint_violation_rate": {"mean": 1.214759915051778, "std": 0.015927896988547983}}, "brain": {"makespan": {"mean": 7.987798485709887, "std": 0.20930012078984908}, "resource_utilization": {"mean": 0.8706599109899241, "std": 0.022813447893443604}, "load_balance_degree": {"mean": 1.397105438565227, "std": 0.03660762568947928}, "energy_consumption": {"mean": 5.264455275753031, "std": 0.13794177796035667}, "constraint_violation_rate": {"mean": 1.1136347698708335, "std": 0.029179991491613922}}, "sipht": {"makespan": {"mean": 8.677726081018243, "std": 0.14403964905526875}, "resource_utilization": {"mean": 0.8807841927677107, "std": 0.014619941311261443}, "load_balance_degree": {"mean": 1.4112564906846272, "std": 0.02342513323736206}, "energy_consumption": {"mean": 5.304722979169167, "std": 0.08805191926100649}, "constraint_violation_rate": {"mean": 1.3011584665886635, "std": 0.021597640573454424}}}, "aggregated_results": {"makespan": {"mean": 8.36936865877933, "std": 0.28637981988392297}, "resource_utilization": {"mean": 0.8773816807233913, "std": 0.004753130483111844}, "load_balance_degree": {"mean": 1.4162882428384735, "std": 0.018070632275271874}, "energy_consumption": {"mean": 5.345199290885066, "std": 0.08727789989622246}, "constraint_violation_rate": {"mean": 1.2098510505037583, "std": 0.07663487857385075}}}, "TLF-GNN-NoColoring": {"configuration": {"graph_coloring": false, "dag_transformer": true, "pinn_constraint": true, "gat_decision": true, "description": "无图着色模块"}, "results_by_workflow_type": {"montage": {"makespan": {"mean": 9.78536165033252, "std": 0.2088593366935887}, "resource_utilization": {"mean": 0.8131598542496782, "std": 0.017356131929847692}, "load_balance_degree": {"mean": 1.6696126652785577, "std": 0.03563631128476942}, "energy_consumption": {"mean": 6.067377635890849, "std": 0.12950246641713195}, "constraint_violation_rate": {"mean": 2.7801356719027885, "std": 0.059339379892215834}}, "brain": {"makespan": {"mean": 9.30775559739188, "std": 0.14644552458337667}, "resource_utilization": {"mean": 0.8081877537466001, "std": 0.012715791505359895}, "load_balance_degree": {"mean": 1.6279724619012415, "std": 0.025614046124849157}, "energy_consumption": {"mean": 5.874457250142284, "std": 0.09242700505380355}, "constraint_violation_rate": {"mean": 2.5623270817077395, "std": 0.040314910475306096}}, "sipht": {"makespan": {"mean": 10.21819572738682, "std": 0.20281618424505612}, "resource_utilization": {"mean": 0.8261970466731551, "std": 0.016398798468077173}, "load_balance_degree": {"mean": 1.661782696149414, "std": 0.03298394691874613}, "energy_consumption": {"mean": 5.9817385306159645, "std": 0.11872872827044759}, "constraint_violation_rate": {"mean": 3.0253274351704387, "std": 0.06004830822026812}}}, "aggregated_results": {"makespan": {"mean": 9.770437658370406, "std": 0.3718354042031366}, "resource_utilization": {"mean": 0.8158482182231445, "std": 0.007594038607969153}, "load_balance_degree": {"mean": 1.6531226077764043, "std": 0.01806884025603777}, "energy_consumption": {"mean": 5.974524472216366, "std": 0.07892443976482975}, "constraint_violation_rate": {"mean": 2.7892633962603224, "std": 0.18912926503779795}}}, "TLF-GNN-NoTransformer": {"configuration": {"graph_coloring": true, "dag_transformer": false, "pinn_constraint": true, "gat_decision": true, "description": "无DAG Transformer层"}, "results_by_workflow_type": {"montage": {"makespan": {"mean": 12.719117701634298, "std": 0.18763487617939628}, "resource_utilization": {"mean": 0.7943428534317708, "std": 0.011718298898083762}, "load_balance_degree": {"mean": 1.8989078451135977, "std": 0.02801305458067998}, "energy_consumption": {"mean": 7.205974956861158, "std": 0.10630393164839907}, "constraint_violation_rate": {"mean": 3.515697054976538, "std": 0.05186424067056394}}, "brain": {"makespan": {"mean": 11.977217485239537, "std": 0.22583998442529946}, "resource_utilization": {"mean": 0.78158315021917, "std": 0.01473737340831493}, "load_balance_degree": {"mean": 1.833015223501488, "std": 0.034562963395887075}, "energy_consumption": {"mean": 6.907013885657782, "std": 0.13023725337580644}, "constraint_violation_rate": {"mean": 3.20782657616148, "std": 0.06048612750767641}}, "sipht": {"makespan": {"mean": 13.211916704075678, "std": 0.25177929433725504}, "resource_utilization": {"mean": 0.8028366794621352, "std": 0.015299646307994455}, "load_balance_degree": {"mean": 1.8800694617823954, "std": 0.03582845494678067}, "energy_consumption": {"mean": 7.066927764146593, "std": 0.13467433419711883}, "constraint_violation_rate": {"mean": 3.8056543896581734, "std": 0.07252429743399964}}}, "aggregated_results": {"makespan": {"mean": 12.636083963649838, "std": 0.5074718327036476}, "resource_utilization": {"mean": 0.7929208943710253, "std": 0.008734781126745392}, "load_balance_degree": {"mean": 1.8706641767991605, "std": 0.02771045452461416}, "energy_consumption": {"mean": 7.059972202221844, "std": 0.12214940416019027}, "constraint_violation_rate": {"mean": 3.509726006932064, "std": 0.24409870095055308}}}, "TLF-GNN-NoPINN": {"configuration": {"graph_coloring": true, "dag_transformer": true, "pinn_constraint": false, "gat_decision": true, "description": "无PINN约束层"}, "results_by_workflow_type": {"montage": {"makespan": {"mean": 10.231951382680949, "std": 0.2711622153332429}, "resource_utilization": {"mean": 0.8417406340141985, "std": 0.022307402226484523}, "load_balance_degree": {"mean": 1.561294720254088, "std": 0.04137667579703309}, "energy_consumption": {"mean": 5.870938940404755, "std": 0.15558877770480847}, "constraint_violation_rate": {"mean": 8.677738494991736, "std": 0.229973218829737}}, "brain": {"makespan": {"mean": 9.635851301264118, "std": 0.15085130810176314}, "resource_utilization": {"mean": 0.8282818794516602, "std": 0.012966929551505023}, "load_balance_degree": {"mean": 1.5072307516274521, "std": 0.023596019011249858}, "energy_consumption": {"mean": 5.627789130582609, "std": 0.08810423962830323}, "constraint_violation_rate": {"mean": 7.91842242702828, "std": 0.12396459263156645}}, "sipht": {"makespan": {"mean": 10.659264085022931, "std": 0.24086181312471433}, "resource_utilization": {"mean": 0.8532129127610932, "std": 0.019279605750438945}, "load_balance_degree": {"mean": 1.550296658496316, "std": 0.03503124240736975}, "energy_consumption": {"mean": 5.774380268299948, "std": 0.13048064950830127}, "constraint_violation_rate": {"mean": 9.420725987316429, "std": 0.21287521578946492}}}, "aggregated_results": {"makespan": {"mean": 10.175688922989332, "std": 0.4196963424256111}, "resource_utilization": {"mean": 0.8410784754089841, "std": 0.010188815632279006}, "load_balance_degree": {"mean": 1.5396073767926186, "std": 0.02332986260027004}, "energy_consumption": {"mean": 5.757702779762437, "std": 0.09996353159052743}, "constraint_violation_rate": {"mean": 8.672295636445481, "std": 0.6133249358186778}}}, "TLF-GNN-NoGAT": {"configuration": {"graph_coloring": true, "dag_transformer": true, "pinn_constraint": true, "gat_decision": false, "description": "无GAT决策层"}, "results_by_workflow_type": {"montage": {"makespan": {"mean": 11.366442092644883, "std": 0.1425841245614424}, "resource_utilization": {"mean": 0.8048966634422949, "std": 0.010096869819414237}, "load_balance_degree": {"mean": 1.7270410351447663, "std": 0.02166453073624058}, "energy_consumption": {"mean": 6.768652698135428, "std": 0.08490804875947883}, "constraint_violation_rate": {"mean": 4.178180677861375, "std": 0.052412375777456055}}, "brain": {"makespan": {"mean": 10.798722060346615, "std": 0.18086436005262818}, "resource_utilization": {"mean": 0.7990173187778321, "std": 0.013382486855771602}, "load_balance_degree": {"mean": 1.6819524354768167, "std": 0.028170486209523723}, "energy_consumption": {"mean": 6.545588237849908, "std": 0.10962997484248004}, "constraint_violation_rate": {"mean": 3.8462324047194967, "std": 0.06441932282410691}}, "sipht": {"makespan": {"mean": 11.90601817818799, "std": 0.15472034685319652}, "resource_utilization": {"mean": 0.8203373725177893, "std": 0.010660397196866091}, "load_balance_degree": {"mean": 1.7242721752261743, "std": 0.02240715451863276}, "energy_consumption": {"mean": 6.693808618858499, "std": 0.08698696539671516}, "constraint_violation_rate": {"mean": 4.5607710715492935, "std": 0.059267848540738986}}}, "aggregated_results": {"makespan": {"mean": 11.357060777059829, "std": 0.45210041654351174}, "resource_utilization": {"mean": 0.8080837849126388, "std": 0.008990902080018084}, "load_balance_degree": {"mean": 1.711088548615919, "std": 0.020633330042322724}, "energy_consumption": {"mean": 6.669349851614612, "std": 0.09269344396786489}, "constraint_violation_rate": {"mean": 4.195061384710055, "std": 0.29195330139882436}}}, "BasicGNN": {"configuration": {"graph_coloring": false, "dag_transformer": false, "pinn_constraint": false, "gat_decision": false, "description": "仅基础GNN"}, "results_by_workflow_type": {"montage": {"makespan": {"mean": 14.580502244622304, "std": 0.2777288466185581}, "resource_utilization": {"mean": 0.7561481431281004, "std": 0.014403080781472195}, "load_balance_degree": {"mean": 2.11816385708841, "std": 0.04034670377663102}, "energy_consumption": {"mean": 8.091953969266235, "std": 0.15413522833917917}, "constraint_violation_rate": {"mean": 12.383686592965955, "std": 0.2358839864805514}}, "brain": {"makespan": {"mean": 13.784901332263944, "std": 0.37716916733897976}, "resource_utilization": {"mean": 0.7469754247184415, "std": 0.02043802071359947}, "load_balance_degree": {"mean": 2.0528346275141134, "std": 0.05616768002045344}, "energy_consumption": {"mean": 7.787233778139325, "std": 0.21306677568307403}, "constraint_violation_rate": {"mean": 11.344402233461174, "std": 0.31039458616497023}}, "sipht": {"makespan": {"mean": 15.127712613850338, "std": 0.2771048734119475}, "resource_utilization": {"mean": 0.7633400413355622, "std": 0.0139826324655915}, "load_balance_degree": {"mean": 2.094698397128896, "std": 0.038370052961025525}, "energy_consumption": {"mean": 7.926532560890438, "std": 0.1451958308535168}, "constraint_violation_rate": {"mean": 13.38935921511921, "std": 0.24526224057004747}}}, "aggregated_results": {"makespan": {"mean": 14.497705396912195, "std": 0.5513178290738002}, "resource_utilization": {"mean": 0.7554878697273679, "std": 0.0066971207733434275}, "load_balance_degree": {"mean": 2.0885656272438067, "std": 0.027020797040309352}, "energy_consumption": {"mean": 7.935240102765332, "std": 0.12455377602795371}, "constraint_violation_rate": {"mean": 12.372482680515446, "std": 0.8348877808618493}}}}, "component_contributions": {"graph_coloring": {"makespan": 16.740438337859317, "resource_utilization": 7.013306050510786, "load_balance_degree": 16.722186753684824, "energy_consumption": 11.773652338921403, "constraint_violation_rate": 130.54601598262263}, "dag_transformer": {"makespan": 50.980133374753365, "resource_utilization": 9.626458838612747, "load_balance_degree": 32.08216521306731, "energy_consumption": 32.08061698019201, "constraint_violation_rate": 190.09571099439742}, "pinn_constraint": {"makespan": 21.582515215352625, "resource_utilization": 4.137675325575027, "load_balance_degree": 8.707205936200763, "energy_consumption": 7.71727051563437, "constraint_violation_rate": 616.8068856769193}, "gat_decision": {"makespan": 35.69793899742317, "resource_utilization": 7.898261079900504, "load_balance_degree": 20.81499350630896, "energy_consumption": 24.7727070342833, "constraint_violation_rate": 246.74197149833557}}}