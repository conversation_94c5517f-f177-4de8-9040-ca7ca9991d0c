# TLF-GNN工作流调度系统实验说明文档

## 📋 实验概述

本文件夹包含了TLF-GNN（Three-Layer Fusion Graph Neural Network）工作流调度系统的完整实验代码和结果。实验设计严格按照《核心实验设计文档.md》和《实验结果数据模板.md》进行实施。

## 📁 文件夹结构

```
New_experiments/
├── 实验说明文档.md                    # 本文档
├── data/                             # 实验数据
│   ├── montage_workflows.json        # Montage工作流数据
│   ├── brain_workflows.json          # Brain工作流数据
│   ├── sipht_workflows.json          # Sipht工作流数据
│   ├── epigenomics_workflows.json    # Epigenomics工作流数据
│   ├── cybershake_workflows.json     # CyberShake工作流数据
│   ├── small_scale_data.json         # 小规模实验数据
│   ├── medium_scale_data.json        # 中规模实验数据
│   ├── large_scale_data.json         # 大规模实验数据
│   ├── all_experiment_data.json      # 完整实验数据
│   ├── data_statistics.json          # 数据统计信息
│   └── data_generation_log.json      # 数据生成日志
├── experiment_1/                     # 实验1：方法有效性验证与性能对比
│   ├── exp1_main.py                  # 实验1主程序
│   ├── baseline_schedulers.py        # 基线调度算法实现
│   ├── results/                      # 实验1结果
│   │   ├── performance_comparison_results.json  # 性能对比结果
│   │   └── experiment_1_summary.json # 实验1总结
│   └── figures/                      # 实验1图表
│       ├── performance_radar.png     # 性能雷达图
│       └── algorithm_comparison.png  # 算法对比图
├── experiment_2/                     # 实验2：可扩展性分析
│   ├── exp2_main.py                  # 实验2主程序
│   ├── scalability_analysis.py       # 可扩展性分析
│   ├── results/                      # 实验2结果
│   └── figures/                      # 实验2图表
├── experiment_3/                     # 实验3：消融研究
│   ├── exp3_main.py                  # 实验3主程序
│   ├── ablation_study.py             # 消融实验分析
│   ├── results/                      # 实验3结果
│   └── figures/                      # 实验3图表
├── experiment_4/                     # 实验4：可视化验证
│   ├── exp4_main.py                  # 实验4主程序
│   ├── visualization_analysis.py     # 可视化分析
│   ├── results/                      # 实验4结果
│   └── figures/                      # 实验4图表
├── results/                          # 汇总结果
│   ├── all_experiments_summary.json  # 所有实验汇总
│   └── final_report.json            # 最终实验报告
└── figures/                          # 汇总图表
    └── comprehensive_results.png     # 综合结果图
```

## 🎯 实验目标

### 实验1：方法有效性验证与性能对比分析
- **目标**：验证TLF-GNN的学习有效性，与10种主流算法进行性能对比
- **输入数据**：中规模工作流数据（30-70任务，8-12节点）
- **输出结果**：性能对比表、统计显著性检验结果
- **关键图表**：性能雷达图、算法对比柱状图

### 实验2：可扩展性分析实验
- **目标**：验证TLF-GNN在不同规模下的适应性
- **输入数据**：小、中、大三种规模的工作流数据
- **输出结果**：不同规模下的性能数据、扩展性评分
- **关键图表**：可扩展性柱状图、性能保持率曲线图

### 实验3：消融研究实验
- **目标**：评估TLF-GNN各组件的贡献度
- **输入数据**：中规模工作流数据
- **输出结果**：6种配置的性能数据、组件贡献度分析
- **关键图表**：消融实验热力图、组件贡献度柱状图

### 实验4：可视化验证实验
- **目标**：通过可视化展示调度效果
- **输入数据**：代表性工作流样本
- **输出结果**：着色效果图、任务分配矩阵、甘特图
- **关键图表**：图着色对比图、任务分配热力图、节点甘特图

## 🔬 实验数据说明

### 数据生成策略
```python
data_generation_config = {
    'workflow_types': ['montage', 'brain', 'sipht', 'epigenomics', 'cybershake'],
    'scale_configurations': {
        'small': {'tasks': [10, 30], 'nodes': [4, 8], 'count': 150},
        'medium': {'tasks': [30, 70], 'nodes': [8, 12], 'count': 300},
        'large': {'tasks': [70, 100], 'nodes': [12, 16], 'count': 150}
    },
    'total_workflows': 600
}
```

### 数据文件说明
- **montage_workflows.json**: 天文图像拼接工作流，CPU和I/O密集型
- **brain_workflows.json**: 神经影像处理工作流，内存密集型
- **sipht_workflows.json**: 生物信息学分析工作流，计算密集型
- **epigenomics_workflows.json**: 表观基因组学数据流工作流，数据流密集型
- **cybershake_workflows.json**: 地震模拟工作流，通信密集型
- **small_scale_data.json**: 小规模工作流数据（10-30任务，4-8节点）
- **medium_scale_data.json**: 中规模工作流数据（30-70任务，8-12节点）
- **large_scale_data.json**: 大规模工作流数据（70-100任务，12-16节点）

## 🚀 实验执行流程

### 第一步：数据生成
```bash
cd New_experiments
python data_generation.py
```

### 第二步：执行实验1
```bash
cd experiment_1
python exp1_main.py
```

### 第三步：执行实验2
```bash
cd experiment_2
python exp2_main.py
```

### 第四步：执行实验3
```bash
cd experiment_3
python exp3_main.py
```

### 第五步：执行实验4
```bash
cd experiment_4
python exp4_main.py
```

### 第六步：生成综合报告
```bash
python generate_comprehensive_report.py
```

## 📊 预期输出结果

### JSON结果文件
- **performance_comparison_results.json**: 性能对比详细数据
- **scalability_results.json**: 可扩展性分析数据
- **ablation_results.json**: 消融实验数据
- **visualization_data.json**: 可视化相关数据

### 图表文件
- **performance_radar.png**: 多算法性能雷达图
- **algorithm_comparison.png**: 算法对比柱状图
- **scalability_chart.png**: 可扩展性分析图
- **ablation_heatmap.png**: 消融实验热力图
- **coloring_comparison.png**: 图着色效果对比
- **gantt_charts.png**: 节点甘特图

## 🔧 技术实现说明

### 算法实现
- **TLF-GNN**: 完整的三层融合图神经网络
- **对比算法**: HEFT、CPOP、GA、PSO、CGWSA、GCN、GAT、DQN、Random、RoundRobin等10种算法
- **评估指标**: Makespan、资源利用率、负载均衡度、能耗

### 真实算法实现
所有对比算法均为真实实现，包括：
- **经典启发式**: HEFT、CPOP、Random、RoundRobin
- **进化算法**: GA（遗传算法）
- **群体智能**: PSO（粒子群优化）、CGWSA（混沌灰狼算法）
- **机器学习**: DQN（Q表强化学习）
- **图神经网络**: GCN（图卷积网络）、GAT（图注意力网络）

### 统计分析
- **性能对比**: 基于真实调度结果的指标计算
- **统计检验**: 支持显著性检验和效应大小分析
- **可视化**: 多维度性能对比和算法效果展示

### 可视化技术
- **性能雷达图**: 多维度性能对比
- **算法对比图**: 柱状图展示各算法性能
- **热力图**: seaborn热力图展示
- **甘特图**: 时间轴任务执行图

## 📈 实验验证标准

### 有效性验证
- TLF-GNN在所有工作流类型上都有改进
- 相比最佳基线方法性能提升显著
- 统计显著性p值<0.01

### 性能对比
- 相比最佳基线方法性能提升>20%
- 在所有工作流类型上都有改进
- 真实算法实现，无模拟数据

### 可扩展性
- 大规模场景性能保持>80%
- 计算时间增长<O(n²)
- 内存使用增长<O(n)

### 消融实验
- 每个组件贡献度>10%
- 完整方法性能>各组件简单相加
- 移除任何组件都导致性能下降

## 🎯 使用说明

1. **环境准备**: 确保安装了所需的Python包（numpy, networkx, matplotlib, seaborn, torch等）
2. **数据生成**: 运行数据生成脚本 `python data_generation.py`
3. **实验执行**: 按顺序执行各个实验
4. **结果分析**: 查看生成的JSON文件和图表
5. **报告生成**: 运行综合报告生成脚本

## 🔍 数据生成说明

### 生成的数据文件
- **5类工作流数据**: montage、brain、sipht、epigenomics、cybershake
- **3种规模数据**: small、medium、large
- **完整数据**: all_experiment_data.json
- **统计信息**: data_statistics.json
- **生成日志**: data_generation_log.json

### 数据特点
- 每种类型每种规模生成对应数量的workflow
- 每个workflow包含完整的DAG结构和节点信息
- 支持异构计算节点和多样化任务特征
- 确保数据完整性和实验可重现性

本实验系统提供了完整的、可重现的实验流程，确保实验结果的科学性和可信度。
