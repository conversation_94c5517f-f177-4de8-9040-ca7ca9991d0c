#!/usr/bin/env python3
"""
TLF-GNN实验套件主运行脚本
按顺序执行所有实验并生成综合报告
"""

import os
import sys
import subprocess
import time
from datetime import datetime

class ExperimentSuite:
    """实验套件管理器"""
    
    def __init__(self):
        self.experiments = [
            {
                'id': 'data_generation',
                'name': '数据生成',
                'script': 'data_generation.py',
                'directory': '.',
                'description': '生成所有实验所需的工作流和节点数据'
            },
            {
                'id': 'experiment_1',
                'name': '实验1：方法有效性验证与性能对比',
                'script': 'exp1_main.py',
                'directory': 'experiment_1',
                'description': '验证TLF-GNN的学习有效性并与12种算法进行性能对比'
            },
            {
                'id': 'experiment_2',
                'name': '实验2：可扩展性分析',
                'script': 'exp2_main.py',
                'directory': 'experiment_2',
                'description': '验证TLF-GNN在小、中、大三种规模下的适应性'
            },
            {
                'id': 'experiment_3',
                'name': '实验3：消融研究',
                'script': 'exp3_main.py',
                'directory': 'experiment_3',
                'description': '评估TLF-GNN各组件的贡献度'
            },
            {
                'id': 'experiment_4',
                'name': '实验4：可视化验证',
                'script': 'exp4_main.py',
                'directory': 'experiment_4',
                'description': '生成着色效果图、任务分配结果和甘特图'
            },
            {
                'id': 'comprehensive_report',
                'name': '综合报告生成',
                'script': 'generate_comprehensive_report.py',
                'directory': '.',
                'description': '汇总所有实验结果并生成最终报告'
            }
        ]
        
        self.start_time = None
        self.results = {}
    
    def print_banner(self):
        """打印横幅"""
        print("=" * 80)
        print("🎯 TLF-GNN工作流调度系统 - 完整实验套件")
        print("=" * 80)
        print("📋 实验概述:")
        for i, exp in enumerate(self.experiments, 1):
            print(f"  {i}. {exp['name']}")
            print(f"     {exp['description']}")
        print("=" * 80)
    
    def run_experiment(self, experiment: dict) -> bool:
        """运行单个实验"""
        exp_id = experiment['id']
        exp_name = experiment['name']
        script = experiment['script']
        directory = experiment['directory']
        
        print(f"\n🚀 开始执行: {exp_name}")
        print(f"📁 目录: {directory}")
        print(f"📄 脚本: {script}")
        print("-" * 60)
        
        start_time = time.time()
        
        try:
            # 切换到实验目录
            original_dir = os.getcwd()
            if directory != '.':
                os.chdir(directory)
            
            # 运行实验脚本
            result = subprocess.run([sys.executable, script], 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=300)  # 5分钟超时
            
            # 返回原目录
            os.chdir(original_dir)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result.returncode == 0:
                print(f"✅ {exp_name} 执行成功!")
                print(f"⏱️ 执行时间: {duration:.2f} 秒")
                
                self.results[exp_id] = {
                    'status': 'success',
                    'duration': duration,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
                return True
            else:
                print(f"❌ {exp_name} 执行失败!")
                print(f"错误信息: {result.stderr}")
                
                self.results[exp_id] = {
                    'status': 'failed',
                    'duration': duration,
                    'stdout': result.stdout,
                    'stderr': result.stderr,
                    'return_code': result.returncode
                }
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {exp_name} 执行超时!")
            self.results[exp_id] = {
                'status': 'timeout',
                'duration': 300,
                'error': 'Execution timeout'
            }
            return False
            
        except Exception as e:
            print(f"💥 {exp_name} 执行异常: {str(e)}")
            self.results[exp_id] = {
                'status': 'error',
                'duration': time.time() - start_time,
                'error': str(e)
            }
            return False
    
    def run_all_experiments(self):
        """运行所有实验"""
        self.print_banner()
        
        self.start_time = time.time()
        success_count = 0
        
        for i, experiment in enumerate(self.experiments, 1):
            print(f"\n📊 进度: {i}/{len(self.experiments)}")
            
            success = self.run_experiment(experiment)
            if success:
                success_count += 1
            else:
                print(f"⚠️ {experiment['name']} 失败，但继续执行后续实验...")
        
        total_time = time.time() - self.start_time
        
        # 打印最终结果
        print("\n" + "=" * 80)
        print("🎉 实验套件执行完成!")
        print("=" * 80)
        print(f"📊 执行统计:")
        print(f"  - 总实验数: {len(self.experiments)}")
        print(f"  - 成功数: {success_count}")
        print(f"  - 失败数: {len(self.experiments) - success_count}")
        print(f"  - 总耗时: {total_time:.2f} 秒")
        print(f"  - 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 详细结果
        print(f"\n📋 详细结果:")
        for exp in self.experiments:
            exp_id = exp['id']
            exp_name = exp['name']
            
            if exp_id in self.results:
                result = self.results[exp_id]
                status = result['status']
                duration = result.get('duration', 0)
                
                if status == 'success':
                    print(f"  ✅ {exp_name} - 成功 ({duration:.2f}s)")
                elif status == 'failed':
                    print(f"  ❌ {exp_name} - 失败 ({duration:.2f}s)")
                elif status == 'timeout':
                    print(f"  ⏰ {exp_name} - 超时 (300s)")
                else:
                    print(f"  💥 {exp_name} - 异常 ({duration:.2f}s)")
            else:
                print(f"  ❓ {exp_name} - 未执行")
        
        # 生成的文件统计
        print(f"\n📁 生成的文件:")
        self.print_generated_files()
        
        # 保存执行日志
        self.save_execution_log()
        
        return success_count == len(self.experiments)
    
    def print_generated_files(self):
        """打印生成的文件统计"""
        
        file_counts = {
            'data_files': 0,
            'result_files': 0,
            'figure_files': 0
        }
        
        # 统计数据文件
        data_dir = 'data'
        if os.path.exists(data_dir):
            file_counts['data_files'] = len([f for f in os.listdir(data_dir) if f.endswith('.json')])
        
        # 统计结果文件
        for exp in self.experiments[1:-1]:  # 排除数据生成和报告生成
            results_dir = os.path.join(exp['directory'], 'results')
            if os.path.exists(results_dir):
                file_counts['result_files'] += len([f for f in os.listdir(results_dir) if f.endswith('.json')])
        
        # 统计图表文件
        for exp in self.experiments[1:-1]:
            figures_dir = os.path.join(exp['directory'], 'figures')
            if os.path.exists(figures_dir):
                file_counts['figure_files'] += len([f for f in os.listdir(figures_dir) if f.endswith('.png')])
        
        # 综合报告文件
        if os.path.exists('results'):
            file_counts['result_files'] += len([f for f in os.listdir('results') if f.endswith('.json')])
        if os.path.exists('figures'):
            file_counts['figure_files'] += len([f for f in os.listdir('figures') if f.endswith('.png')])
        
        print(f"  - 数据文件: {file_counts['data_files']} 个")
        print(f"  - 结果文件: {file_counts['result_files']} 个")
        print(f"  - 图表文件: {file_counts['figure_files']} 个")
        print(f"  - 总计: {sum(file_counts.values())} 个文件")
    
    def save_execution_log(self):
        """保存执行日志"""
        import json
        
        log_data = {
            'execution_info': {
                'start_time': datetime.fromtimestamp(self.start_time).isoformat(),
                'end_time': datetime.now().isoformat(),
                'total_duration': time.time() - self.start_time,
                'experiments_count': len(self.experiments),
                'success_count': sum(1 for r in self.results.values() if r['status'] == 'success')
            },
            'experiment_results': self.results,
            'experiments_config': self.experiments
        }
        
        os.makedirs('logs', exist_ok=True)
        log_file = f"logs/execution_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(log_file, 'w') as f:
            json.dump(log_data, f, indent=2, default=str)
        
        print(f"\n📝 执行日志已保存: {log_file}")
    
    def quick_run(self):
        """快速运行模式（跳过已完成的实验）"""
        print("🚀 快速运行模式 - 检查已完成的实验...")
        
        # 检查哪些实验已经完成
        completed = []
        for exp in self.experiments:
            if exp['id'] == 'data_generation':
                if os.path.exists('data/all_experiment_data.json'):
                    completed.append(exp['id'])
            elif exp['id'].startswith('experiment_'):
                summary_file = f"{exp['directory']}/results/{exp['id']}_summary.json"
                if os.path.exists(summary_file):
                    completed.append(exp['id'])
            elif exp['id'] == 'comprehensive_report':
                if os.path.exists('results/comprehensive_final_report.json'):
                    completed.append(exp['id'])
        
        print(f"✅ 已完成的实验: {len(completed)}/{len(self.experiments)}")
        
        if len(completed) == len(self.experiments):
            print("🎉 所有实验已完成!")
            return True
        
        # 只运行未完成的实验
        remaining_experiments = [exp for exp in self.experiments if exp['id'] not in completed]
        
        print(f"🔄 需要运行的实验: {len(remaining_experiments)}")
        for exp in remaining_experiments:
            print(f"  - {exp['name']}")
        
        # 运行剩余实验
        original_experiments = self.experiments
        self.experiments = remaining_experiments
        result = self.run_all_experiments()
        self.experiments = original_experiments
        
        return result

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='TLF-GNN实验套件')
    parser.add_argument('--quick', action='store_true', help='快速运行模式（跳过已完成的实验）')
    parser.add_argument('--experiment', type=str, help='运行指定实验 (1-6)')
    
    args = parser.parse_args()
    
    suite = ExperimentSuite()
    
    if args.experiment:
        # 运行指定实验
        try:
            exp_index = int(args.experiment) - 1
            if 0 <= exp_index < len(suite.experiments):
                experiment = suite.experiments[exp_index]
                print(f"🎯 运行指定实验: {experiment['name']}")
                success = suite.run_experiment(experiment)
                if success:
                    print("✅ 实验执行成功!")
                else:
                    print("❌ 实验执行失败!")
            else:
                print(f"❌ 无效的实验编号: {args.experiment}")
        except ValueError:
            print(f"❌ 无效的实验编号: {args.experiment}")
    
    elif args.quick:
        # 快速运行模式
        success = suite.quick_run()
        if success:
            print("✅ 快速运行完成!")
        else:
            print("❌ 快速运行失败!")
    
    else:
        # 运行所有实验
        success = suite.run_all_experiments()
        if success:
            print("✅ 所有实验执行成功!")
        else:
            print("⚠️ 部分实验执行失败，请检查日志!")

if __name__ == "__main__":
    main()
