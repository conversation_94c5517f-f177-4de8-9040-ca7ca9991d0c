#!/usr/bin/env python3
"""
测试增强的特征提取器功能
"""

import sys
import os
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
sys.path.append('.')

def test_feature_extraction():
    """测试特征提取功能"""
    print('🔄 测试增强的特征提取器...')
    
    # 创建测试DAG
    dag = nx.DiGraph()
    dag.add_node(0, cpu_demand=1.0, memory_demand=2.0, io_demand=0.5, network_demand=0.3, runtime=5.0)
    dag.add_node(1, cpu_demand=2.0, memory_demand=1.5, io_demand=1.0, network_demand=0.8, runtime=3.0)
    dag.add_node(2, cpu_demand=1.5, memory_demand=3.0, io_demand=0.2, network_demand=0.5, runtime=4.0)
    dag.add_node(3, cpu_demand=0.8, memory_demand=1.0, io_demand=2.0, network_demand=1.2, runtime=6.0)
    dag.add_edges_from([(0, 1), (1, 2), (1, 3), (2, 3)])
    
    try:
        from src.preprocessing.feature_extraction import WorkflowFeatureExtractor
        
        # 创建特征提取器
        extractor = WorkflowFeatureExtractor(feature_dim=128)
        
        # 提取特征
        task_features, metadata = extractor.extract_task_features(dag, 'test')
        
        print('✅ 特征提取成功')
        print(f'   任务特征形状: {task_features.shape}')
        print(f'   特征维度: {task_features.shape[1]}')
        print(f'   包含图着色结果: {"coloring_result" in metadata}')
        print(f'   包含图特征: {"graph_features" in metadata}')
        
        # 创建特征分析图
        plt.figure(figsize=(15, 10))
        
        # 子图1: 特征热力图
        plt.subplot(2, 3, 1)
        plt.imshow(task_features.numpy(), cmap='viridis', aspect='auto')
        plt.title('任务特征热力图 (128维)')
        plt.xlabel('特征维度')
        plt.ylabel('任务ID')
        plt.colorbar()
        
        # 子图2: 特征分布
        plt.subplot(2, 3, 2)
        feature_means = task_features.mean(dim=0).numpy()
        plt.plot(feature_means)
        plt.title('特征均值分布')
        plt.xlabel('特征维度')
        plt.ylabel('均值')
        plt.grid(True)
        
        # 子图3: DAG结构
        plt.subplot(2, 3, 3)
        pos = nx.spring_layout(dag)
        nx.draw(dag, pos, with_labels=True, node_color='lightblue', 
                node_size=1000, font_size=12, font_weight='bold')
        plt.title('测试DAG结构')
        
        # 子图4: 资源需求分析
        plt.subplot(2, 3, 4)
        resources = ['CPU', 'Memory', 'I/O', 'Network']
        resource_data = []
        for i in range(4):
            resource_key = f'{resources[i].lower()}_demand'
            if resources[i] == 'I/O':
                resource_key = 'io_demand'
            resource_data.append([dag.nodes[j].get(resource_key, 0) for j in range(4)])
        
        x = np.arange(4)
        width = 0.2
        for i, resource in enumerate(resources):
            plt.bar(x + i*width, resource_data[i], width, label=resource)
        plt.xlabel('任务ID')
        plt.ylabel('资源需求')
        plt.title('任务资源需求分析')
        plt.legend()
        plt.xticks(x + width*1.5, [f'Task {i}' for i in range(4)])
        
        # 子图5: 特征组件分析
        plt.subplot(2, 3, 5)
        feature_groups = ['基础特征', '资源特征', '结构特征', '着色特征', '上下文特征', '统计特征']
        group_ranges = [(0, 32), (32, 48), (48, 72), (72, 80), (80, 104), (104, 128)]
        group_means = []
        
        for start, end in group_ranges:
            group_mean = task_features[:, start:end].mean().item()
            group_means.append(group_mean)
        
        plt.bar(range(len(feature_groups)), group_means, color='skyblue')
        plt.xlabel('特征组')
        plt.ylabel('平均值')
        plt.title('特征组件分析')
        plt.xticks(range(len(feature_groups)), [f'组{i+1}' for i in range(len(feature_groups))], rotation=45)
        
        # 子图6: 特征统计
        plt.subplot(2, 3, 6)
        feature_stats = {
            '最小值': task_features.min().item(),
            '最大值': task_features.max().item(),
            '均值': task_features.mean().item(),
            '标准差': task_features.std().item()
        }
        
        plt.bar(feature_stats.keys(), feature_stats.values(), color='lightgreen')
        plt.title('特征统计信息')
        plt.ylabel('值')
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        os.makedirs('outputs', exist_ok=True)
        plt.savefig('outputs/feature_extraction_analysis.png', dpi=300, bbox_inches='tight')
        print('📊 特征分析图已保存: outputs/feature_extraction_analysis.png')
        
        return True
        
    except Exception as e:
        print(f'❌ 特征提取测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_data_loader():
    """测试数据加载器功能"""
    print('\n🔄 测试数据加载器...')
    
    try:
        # 创建模拟数据
        processed_data = []
        for i in range(5):
            workflow = {
                'id': i,
                'type': 'test',
                'task_features': np.random.randn(4, 128),
                'node_features': np.random.randn(3, 128),
                'adjacency_matrix': np.array([
                    [0, 1, 0, 0],
                    [0, 0, 1, 1],
                    [0, 0, 0, 1],
                    [0, 0, 0, 0]
                ]),
                'metadata': {'num_tasks': 4, 'workflow_type': 'test'},
                'original_data': {
                    'dag': {
                        'nodes': [{'runtime': 1.0} for _ in range(4)],
                        'edges': [(0, 1), (1, 2), (1, 3), (2, 3)]
                    },
                    'nodes': [{'cpu_capacity': 1.0} for _ in range(3)]
                }
            }
            processed_data.append(workflow)
        
        from src.data_generation.data_loader import WorkflowDataset, create_data_loaders
        
        # 创建数据集
        dataset = WorkflowDataset(processed_data, augment=True)
        print(f'✅ 数据集创建成功，包含 {len(dataset)} 个工作流')
        
        # 测试单个样本
        sample = dataset[0]
        print(f'   样本特征形状: 任务 {sample["task_features"].shape}, 节点 {sample["node_features"].shape}')
        
        # 创建数据加载器
        train_loader, val_loader, test_loader = create_data_loaders(
            processed_data, batch_size=2, train_ratio=0.6, val_ratio=0.2
        )
        
        print(f'✅ 数据加载器创建成功')
        print(f'   训练集: {len(train_loader.dataset)} 个样本')
        print(f'   验证集: {len(val_loader.dataset)} 个样本')
        print(f'   测试集: {len(test_loader.dataset)} 个样本')
        
        # 测试批处理
        for batch_data in train_loader:
            print(f'   批次数据形状: {batch_data["task_features"].shape}')
            break
        
        # 创建数据加载器分析图
        plt.figure(figsize=(12, 8))
        
        # 子图1: 数据集分布
        plt.subplot(2, 3, 1)
        sizes = [len(train_loader.dataset), len(val_loader.dataset), len(test_loader.dataset)]
        labels = ['训练集', '验证集', '测试集']
        plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
        plt.title('数据集分布')
        
        # 子图2: 特征维度分析
        plt.subplot(2, 3, 2)
        dims = ['任务特征', '节点特征']
        dim_sizes = [sample["task_features"].shape[1], sample["node_features"].shape[1]]
        plt.bar(dims, dim_sizes, color=['skyblue', 'lightcoral'])
        plt.title('特征维度')
        plt.ylabel('维度数')
        
        # 子图3: 工作流规模分析
        plt.subplot(2, 3, 3)
        task_counts = [data['task_features'].shape[0] for data in processed_data]
        node_counts = [data['node_features'].shape[0] for data in processed_data]
        
        x = range(len(processed_data))
        plt.bar([i-0.2 for i in x], task_counts, 0.4, label='任务数', color='lightblue')
        plt.bar([i+0.2 for i in x], node_counts, 0.4, label='节点数', color='lightgreen')
        plt.xlabel('工作流ID')
        plt.ylabel('数量')
        plt.title('工作流规模分析')
        plt.legend()
        
        # 子图4: 邻接矩阵可视化
        plt.subplot(2, 3, 4)
        adj_matrix = processed_data[0]['adjacency_matrix']
        plt.imshow(adj_matrix, cmap='Blues')
        plt.title('示例邻接矩阵')
        plt.xlabel('任务ID')
        plt.ylabel('任务ID')
        plt.colorbar()
        
        # 子图5: 批次大小分析
        plt.subplot(2, 3, 5)
        batch_sizes = []
        for batch_data in train_loader:
            batch_sizes.append(batch_data["task_features"].shape[0])
        
        plt.hist(batch_sizes, bins=5, color='orange', alpha=0.7)
        plt.title('批次大小分布')
        plt.xlabel('批次大小')
        plt.ylabel('频次')
        
        # 子图6: 数据增强效果
        plt.subplot(2, 3, 6)
        original_sample = dataset[0]
        augmented_samples = [dataset[0] for _ in range(3)]
        
        original_mean = original_sample["task_features"].mean().item()
        augmented_means = [sample["task_features"].mean().item() for sample in augmented_samples]
        
        plt.plot([0] + list(range(1, 4)), [original_mean] + augmented_means, 'o-')
        plt.title('数据增强效果')
        plt.xlabel('样本序号')
        plt.ylabel('特征均值')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('outputs/data_loader_analysis.png', dpi=300, bbox_inches='tight')
        print('📊 数据加载器分析图已保存: outputs/data_loader_analysis.png')
        
        return True
        
    except Exception as e:
        print(f'❌ 数据加载器测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = test_feature_extraction()
    success2 = test_data_loader()
    
    if success1 and success2:
        print('\n🎉 所有测试通过！')
    else:
        print('\n⚠️ 部分测试失败')
