{"experiment_info": {"name": "Performance Comparison Experiment", "timestamp": "2025-07-31T10:17:16.149271", "num_workflows": 300, "workflow_types": ["montage", "brain", "sipht", "epigenomics", "cybershake"]}, "algorithm_results": {"HEFT": {"algorithm": "HEFT", "results_by_workflow": {"montage": {"makespan": {"mean": 91.2754197775391, "std": 31.727995894089513, "min": 49.53236440778446, "max": 176.2518499238597}, "resource_utilization": {"mean": 0.40968186515874133, "std": 0.04890782272153469, "min": 0.3455088402178333, "max": 0.5464952378116874}, "load_balance_degree": {"mean": 0.565494709329708, "std": 0.09529328697992695, "min": 0.36032038224177576, "max": 0.7012941310360872}, "energy_consumption": {"mean": 20.801443564513228, "std": 5.480254548395735, "min": 12.496021758913765, "max": 29.457855809232214}}, "brain": {"makespan": {"mean": 144.81258388798852, "std": 42.69483997256104, "min": 66.36132888885541, "max": 242.90919826215242}, "resource_utilization": {"mean": 0.39650092418138516, "std": 0.12614403025364096, "min": 0.22276143713110802, "max": 0.6881226255541033}, "load_balance_degree": {"mean": 1.2269097195516079, "std": 0.5853045855712282, "min": 0.3914852095843909, "max": 2.596185030019286}, "energy_consumption": {"mean": 27.372306268193547, "std": 6.992379717834268, "min": 17.466719474426274, "max": 42.64947058622683}}, "sipht": {"makespan": {"mean": 184.8678923731806, "std": 55.988663875397656, "min": 92.68971901026885, "max": 315.33432303036074}, "resource_utilization": {"mean": 0.45852491208367496, "std": 0.0741970190253195, "min": 0.35539993146937665, "max": 0.6102134570810247}, "load_balance_degree": {"mean": 0.569389150381454, "std": 0.09500591486035474, "min": 0.3199162719514446, "max": 0.7213324169166944}, "energy_consumption": {"mean": 42.81264917879527, "std": 9.371535356526918, "min": 22.793894250010812, "max": 56.472990784591296}}, "epigenomics": {"makespan": {"mean": 152.9032737952399, "std": 44.33612990736562, "min": 79.9393885988124, "max": 237.7644515615193}, "resource_utilization": {"mean": 0.38894932901448576, "std": 0.07616020227875316, "min": 0.29489886769440093, "max": 0.570148276064515}, "load_balance_degree": {"mean": 0.702592702081032, "std": 0.1459912564284715, "min": 0.4702227320299731, "max": 0.9765831360236352}, "energy_consumption": {"mean": 29.163756233640306, "std": 6.98263321866291, "min": 17.187253426503247, "max": 39.21331774136868}}, "cybershake": {"makespan": {"mean": 257.86702573117816, "std": 93.76089384085265, "min": 130.79398778485603, "max": 491.52468954821285}, "resource_utilization": {"mean": 0.41875945593954655, "std": 0.07935152608651748, "min": 0.28781618663196257, "max": 0.5862656980445519}, "load_balance_degree": {"mean": 0.9218201202428669, "std": 0.2628225187814451, "min": 0.5735825944740395, "max": 1.4766501190288306}, "energy_consumption": {"mean": 54.34524746256817, "std": 11.985386513935618, "min": 31.687338251713427, "max": 72.24193542667736}}}, "aggregated_results": {"makespan": {"mean": 166.34523911302526, "std": 54.77069573646483}, "resource_utilization": {"mean": 0.4144832972755668, "std": 0.02431605393748758}, "load_balance_degree": {"mean": 0.7972412803173338, "std": 0.25088494361926217}, "energy_consumption": {"mean": 34.899080541542105, "std": 12.075327082209972}}}, "CPOP": {"algorithm": "CPOP", "results_by_workflow": {"montage": {"makespan": {"mean": 82.59866322759873, "std": 26.97214482810892, "min": 45.02681364075343, "max": 145.91878542151585}, "resource_utilization": {"mean": 0.45279083841014567, "std": 0.07381610416148922, "min": 0.3222357976272165, "max": 0.654358266881639}, "load_balance_degree": {"mean": 0.6749998408661548, "std": 0.14008991156129685, "min": 0.4605123006095127, "max": 0.9922979731461933}, "energy_consumption": {"mean": 20.79874484151751, "std": 5.481278425110437, "min": 12.576452243242397, "max": 29.428625510491557}}, "brain": {"makespan": {"mean": 143.66639058626896, "std": 42.58594161579444, "min": 66.36132888885541, "max": 242.90919826215242}, "resource_utilization": {"mean": 0.4011641283639614, "std": 0.1312624026194443, "min": 0.22276143713110802, "max": 0.6881226255541033}, "load_balance_degree": {"mean": 1.2737136184485442, "std": 0.5589155271642182, "min": 0.3914852095843909, "max": 2.596185030019286}, "energy_consumption": {"mean": 27.42347077392381, "std": 7.0224980034715, "min": 17.440182355632743, "max": 42.80398064256652}}, "sipht": {"makespan": {"mean": 185.14355340653248, "std": 53.37816193242048, "min": 98.38301200107362, "max": 302.6475663131074}, "resource_utilization": {"mean": 0.45427204257295273, "std": 0.06556487165104657, "min": 0.3603352308588952, "max": 0.5763887203395337}, "load_balance_degree": {"mean": 0.6990474386211007, "std": 0.16465443278786837, "min": 0.36758132976203617, "max": 0.9974792138564831}, "energy_consumption": {"mean": 42.88699055924471, "std": 9.380944886253584, "min": 23.038375380288652, "max": 56.5730407378973}}, "epigenomics": {"makespan": {"mean": 146.89647961117575, "std": 40.83493482791492, "min": 79.9393885988124, "max": 222.67000845694432}, "resource_utilization": {"mean": 0.40323999292457746, "std": 0.07735217821375928, "min": 0.2770352135546135, "max": 0.5925965175483591}, "load_balance_degree": {"mean": 0.8299340899714774, "std": 0.16261862079066167, "min": 0.5859396579120306, "max": 1.1007468869195531}, "energy_consumption": {"mean": 29.074796183285407, "std": 6.976113092715162, "min": 17.149970679752453, "max": 39.269619841785996}}, "cybershake": {"makespan": {"mean": 257.33323864137606, "std": 100.02857087704878, "min": 130.79398778485603, "max": 499.4594575709436}, "resource_utilization": {"mean": 0.42281660985690533, "std": 0.0791701647069459, "min": 0.2849384002282762, "max": 0.5982136305079357}, "load_balance_degree": {"mean": 0.9899852450378452, "std": 0.24179892175285062, "min": 0.576657618449512, "max": 1.4766501190288306}, "energy_consumption": {"mean": 54.39343694116941, "std": 11.939349374174848, "min": 31.687338251713427, "max": 72.24193542667736}}}, "aggregated_results": {"makespan": {"mean": 163.1276650945904, "std": 57.42212492554059}, "resource_utilization": {"mean": 0.4268567224257086, "std": 0.02305801362732727}, "load_balance_degree": {"mean": 0.8935360465890245, "std": 0.22066559983515774}, "energy_consumption": {"mean": 34.91548785982817, "std": 12.103402731654175}}}, "Random": {"algorithm": "Random", "results_by_workflow": {"montage": {"makespan": {"mean": 101.8587691577138, "std": 29.124917711582135, "min": 50.41019991708255, "max": 169.38656178890977}, "resource_utilization": {"mean": 0.37739178127836576, "std": 0.12382136264496, "min": 0.22063946179844232, "max": 0.6898457207672206}, "load_balance_degree": {"mean": 0.43682083006895134, "std": 0.0781779318654913, "min": 0.2899555819670013, "max": 0.6262772380659732}, "energy_consumption": {"mean": 20.88213762244835, "std": 5.44705600666609, "min": 12.726584025992356, "max": 29.222384750293937}}, "brain": {"makespan": {"mean": 298.27403506307655, "std": 100.65451612927242, "min": 122.80701442058148, "max": 480.0441886655958}, "resource_utilization": {"mean": 0.20160484478038257, "std": 0.07821369241746455, "min": 0.0872347086317547, "max": 0.35967603357156264}, "load_balance_degree": {"mean": 0.48852487168083547, "std": 0.08069738849807427, "min": 0.36944037379801375, "max": 0.7311007235745833}, "energy_consumption": {"mean": 27.59334539321074, "std": 6.916682475145952, "min": 17.883218734294257, "max": 40.999711670637836}}, "sipht": {"makespan": {"mean": 270.7626046837107, "std": 86.87599467129972, "min": 166.43466499118546, "max": 524.1874114374533}, "resource_utilization": {"mean": 0.31737181559619876, "std": 0.07839348802816018, "min": 0.17815515650393257, "max": 0.4597623358868512}, "load_balance_degree": {"mean": 0.4346112100205694, "std": 0.06914943390751116, "min": 0.32214928879530325, "max": 0.5811875472060962}, "energy_consumption": {"mean": 43.22766574145393, "std": 9.310926348943829, "min": 23.51988025796941, "max": 55.214189494630425}}, "epigenomics": {"makespan": {"mean": 253.90923223901237, "std": 95.97349970047583, "min": 142.06602743536047, "max": 493.6175771066038}, "resource_utilization": {"mean": 0.24591952401480383, "std": 0.0786449368130856, "min": 0.14235643923153987, "max": 0.4513794477946489}, "load_balance_degree": {"mean": 0.49040222584235743, "std": 0.1138439202054947, "min": 0.3598931340924629, "max": 0.7524056306098058}, "energy_consumption": {"mean": 29.496050440351723, "std": 6.958625198875039, "min": 17.204761379161535, "max": 40.02696784045812}}, "cybershake": {"makespan": {"mean": 591.6318336494585, "std": 157.3395179567077, "min": 344.4045106166551, "max": 967.1672334782221}, "resource_utilization": {"mean": 0.17835121582083469, "std": 0.037937708714073586, "min": 0.12480526886006331, "max": 0.2677574017046046}, "load_balance_degree": {"mean": 0.4301326766319115, "std": 0.07196356120873315, "min": 0.3218661937256399, "max": 0.6283886060473758}, "energy_consumption": {"mean": 54.61368251699723, "std": 11.964265224825013, "min": 30.993897425979657, "max": 72.08676168765193}}}, "aggregated_results": {"makespan": {"mean": 303.2872949585944, "std": 159.52254093347392}, "resource_utilization": {"mean": 0.2641278362981171, "std": 0.07384028478956517}, "load_balance_degree": {"mean": 0.456098362848925, "std": 0.02733412284588852}, "energy_consumption": {"mean": 35.16257634289239, "std": 12.138574063086896}}}, "RoundRobin": {"algorithm": "RoundR<PERSON>in", "results_by_workflow": {"montage": {"makespan": {"mean": 120.75569015281886, "std": 51.84613245828587, "min": 48.62923989419676, "max": 258.3892821257286}, "resource_utilization": {"mean": 0.3257178708139389, "std": 0.0833766424742799, "min": 0.21394280001463328, "max": 0.5650931013022861}, "load_balance_degree": {"mean": 0.1704363123945342, "std": 0.04185305649980685, "min": 0.10146022889135126, "max": 0.2647093667875777}, "energy_consumption": {"mean": 20.927142426544197, "std": 5.585176914309832, "min": 12.393330126649069, "max": 29.674111502534597}}, "brain": {"makespan": {"mean": 273.04221105346886, "std": 92.95760319237102, "min": 122.6757808489239, "max": 440.75960352246943}, "resource_utilization": {"mean": 0.2242612736810538, "std": 0.09844091456343736, "min": 0.09065722503445414, "max": 0.45412068815585127}, "load_balance_degree": {"mean": 0.17813312701022832, "std": 0.05618689911336485, "min": 0.11143444183926224, "max": 0.3100397046847747}, "energy_consumption": {"mean": 27.532306257600442, "std": 6.9109462787433245, "min": 18.168171047755116, "max": 41.36073264130538}}, "sipht": {"makespan": {"mean": 249.942119988662, "std": 57.093136040847114, "min": 133.0312416939978, "max": 342.3560478585012}, "resource_utilization": {"mean": 0.3316380083688317, "std": 0.05355667904700324, "min": 0.25756769444161665, "max": 0.45438564814776317}, "load_balance_degree": {"mean": 0.14003912551000994, "std": 0.03434125574918528, "min": 0.07672129187267296, "max": 0.18974723745905972}, "energy_consumption": {"mean": 43.246828146222605, "std": 9.547461948282487, "min": 22.945495151421195, "max": 56.325051701934}}, "epigenomics": {"makespan": {"mean": 254.91092426759346, "std": 61.360099889169774, "min": 166.9729530372896, "max": 387.7709633513346}, "resource_utilization": {"mean": 0.23033529684058757, "std": 0.04377671677021892, "min": 0.14833107263181802, "max": 0.30898352970474585}, "load_balance_degree": {"mean": 0.1593371315619156, "std": 0.05878981113608753, "min": 0.06387864451056732, "max": 0.3056797448233258}, "energy_consumption": {"mean": 29.476309444765654, "std": 6.971802466953368, "min": 17.468837371519086, "max": 39.6153608043137}}, "cybershake": {"makespan": {"mean": 627.9976431667831, "std": 220.98608671574107, "min": 332.64015449534475, "max": 1122.6969816535457}, "resource_utilization": {"mean": 0.17179285330133043, "std": 0.035694996956163516, "min": 0.12187717661640522, "max": 0.24625658821887594}, "load_balance_degree": {"mean": 0.15331026285216703, "std": 0.037993106997468086, "min": 0.07521981182440779, "max": 0.21824068281360404}, "energy_consumption": {"mean": 54.60716884669959, "std": 11.956774314234439, "min": 31.29870117727792, "max": 72.94861609292585}}}, "aggregated_results": {"makespan": {"mean": 305.32971772586524, "std": 170.1966424856187}, "resource_utilization": {"mean": 0.2567490606011485, "std": 0.062186415966274416}, "load_balance_degree": {"mean": 0.16025119186577103, "std": 0.013274335485263833}, "energy_consumption": {"mean": 35.1579510243665, "std": 12.137952294261623}}}, "GA": {"algorithm": "GA", "results_by_workflow": {"montage": {"makespan": {"mean": 35.058534400898694, "std": 6.656408316947982, "min": 25.044233268345653, "max": 46.55052778085191}, "resource_utilization": {"mean": 0.9173687154905285, "std": 0.12841443214683537, "min": 0.6236400529673642, "max": 1.0}, "load_balance_degree": {"mean": 0.5713678658133177, "std": 0.11593641015888624, "min": 0.33762695339993964, "max": 0.7344532740731817}, "energy_consumption": {"mean": 20.823172891183503, "std": 5.465040272419348, "min": 12.794470344564724, "max": 29.938196030066432}}, "brain": {"makespan": {"mean": 156.21675033638996, "std": 50.1203835414422, "min": 72.1223420906219, "max": 258.4490851559165}, "resource_utilization": {"mean": 0.3816112258023766, "std": 0.14142087646079843, "min": 0.1730894359475316, "max": 0.6032251919108628}, "load_balance_degree": {"mean": 0.7544764072277839, "std": 0.2499921683638532, "min": 0.38407369352029136, "max": 1.4277074901479432}, "energy_consumption": {"mean": 27.271655260458108, "std": 6.829176963769299, "min": 17.864847679188085, "max": 42.153222620337075}}, "sipht": {"makespan": {"mean": 125.96971967313877, "std": 24.035057642652582, "min": 76.1116411299683, "max": 168.06278908049538}, "resource_utilization": {"mean": 0.6499133838830918, "std": 0.09239305578131518, "min": 0.5434340309028771, "max": 0.8183042722887388}, "load_balance_degree": {"mean": 0.4792537695753837, "std": 0.09457769136271817, "min": 0.3053478999778934, "max": 0.6426477173597108}, "energy_consumption": {"mean": 42.79928361286564, "std": 9.36372103009074, "min": 22.562775127896792, "max": 56.57230562144569}}, "epigenomics": {"makespan": {"mean": 100.82680044807644, "std": 22.443371917934034, "min": 62.434043113836836, "max": 134.33328463603203}, "resource_utilization": {"mean": 0.5913550270913428, "std": 0.16196734158323345, "min": 0.32707619083992384, "max": 0.9600424896052415}, "load_balance_degree": {"mean": 0.4932100722064834, "std": 0.1594892740315759, "min": 0.24050903274668303, "max": 0.8891314299773299}, "energy_consumption": {"mean": 29.291249307774734, "std": 6.875142516808297, "min": 17.101927053440654, "max": 39.424804334835635}}, "cybershake": {"makespan": {"mean": 295.72574603309295, "std": 94.37117785175073, "min": 114.35425000713548, "max": 512.859626754901}, "resource_utilization": {"mean": 0.36434512737952374, "std": 0.07744344974734015, "min": 0.2454053102618735, "max": 0.5135928535982996}, "load_balance_degree": {"mean": 0.5789180143628537, "std": 0.188644242197149, "min": 0.2687980023517062, "max": 0.9876215112941737}, "energy_consumption": {"mean": 54.74655924939786, "std": 11.918230370415472, "min": 31.35194297941889, "max": 72.80323996380143}}}, "aggregated_results": {"makespan": {"mean": 142.75951017831935, "std": 86.27805748646341}, "resource_utilization": {"mean": 0.5809186959293727, "std": 0.20233406924168504}, "load_balance_degree": {"mean": 0.5754452258371645, "std": 0.09807779135507987}, "energy_consumption": {"mean": 34.98638406433597, "std": 12.198803825357931}}}, "PSO": {"algorithm": "PSO", "results_by_workflow": {"montage": {"makespan": {"mean": 35.904850400723966, "std": 8.257042847772434, "min": 24.30984083616594, "max": 50.33812049573986}, "resource_utilization": {"mean": 0.8926665183872806, "std": 0.13877992876193296, "min": 0.6438606596508115, "max": 1.0}, "load_balance_degree": {"mean": 0.45424661053077625, "std": 0.1300990267093273, "min": 0.25372920521812237, "max": 0.8476808929624895}, "energy_consumption": {"mean": 20.785547868402713, "std": 5.502202201505318, "min": 12.64049653728574, "max": 29.308034518196514}}, "brain": {"makespan": {"mean": 190.56703119667642, "std": 68.3434734959832, "min": 78.66996664585972, "max": 337.66535357951506}, "resource_utilization": {"mean": 0.320436355429346, "std": 0.13091036304337564, "min": 0.13943266940725121, "max": 0.5817158511946785}, "load_balance_degree": {"mean": 0.6032441254911539, "std": 0.18194196506655094, "min": 0.3436805161557526, "max": 1.1615765334596042}, "energy_consumption": {"mean": 27.34545774400604, "std": 7.0212784453039765, "min": 17.52461631205205, "max": 42.3469592898077}}, "sipht": {"makespan": {"mean": 158.02802819160758, "std": 28.218678741187325, "min": 82.4979268874147, "max": 202.4214536662088}, "resource_utilization": {"mean": 0.5164187880729656, "std": 0.06487662439094215, "min": 0.42957684605593993, "max": 0.6756302079580356}, "load_balance_degree": {"mean": 0.47983682723785187, "std": 0.2032794381049532, "min": 0.19477292364460835, "max": 1.2450228925751896}, "energy_consumption": {"mean": 42.74504634197084, "std": 9.45074058750742, "min": 22.914962017280086, "max": 56.11820054808347}}, "epigenomics": {"makespan": {"mean": 118.50658903548715, "std": 26.941365415375827, "min": 65.17890380869926, "max": 169.1786359188624}, "resource_utilization": {"mean": 0.49611888090256306, "std": 0.10146763156553588, "min": 0.3159300638274092, "max": 0.6855081545545384}, "load_balance_degree": {"mean": 0.4909505393761731, "std": 0.17598605715638171, "min": 0.1717634607656625, "max": 0.8996017126454702}, "energy_consumption": {"mean": 29.476232424533485, "std": 6.928658931316471, "min": 17.645602126483322, "max": 39.76515401389776}}, "cybershake": {"makespan": {"mean": 366.68253357064407, "std": 120.20237139305213, "min": 190.52906388340833, "max": 641.9222291596718}, "resource_utilization": {"mean": 0.2899530483973427, "std": 0.04605116042089859, "min": 0.20429395317172816, "max": 0.3746615610967541}, "load_balance_degree": {"mean": 0.48679384520569896, "std": 0.12238564154586175, "min": 0.2958148098525992, "max": 0.813958489439516}, "energy_consumption": {"mean": 54.401521210173634, "std": 11.928060769985528, "min": 31.71206171058238, "max": 72.5532696627512}}}, "aggregated_results": {"makespan": {"mean": 173.93780647902784, "std": 109.36717863461104}, "resource_utilization": {"mean": 0.5031187182378997, "std": 0.21484218704037167}, "load_balance_degree": {"mean": 0.5030143895683308, "std": 0.05171100109624019}, "energy_consumption": {"mean": 34.95076111781734, "std": 12.062730375404593}}}, "CGWSA": {"algorithm": "CGWSA", "results_by_workflow": {"montage": {"makespan": {"mean": 31.09678591959416, "std": 6.682629958902717, "min": 21.526992100101925, "max": 46.32584395828578}, "resource_utilization": {"mean": 0.9513513624669512, "std": 0.09119977641301234, "min": 0.7034369424823333, "max": 1.0}, "load_balance_degree": {"mean": 0.6056289963032564, "std": 0.13690853263679206, "min": 0.38400447812641736, "max": 0.9055053786138834}, "energy_consumption": {"mean": 20.91584399804749, "std": 5.64629611913249, "min": 12.645967410865786, "max": 29.89178529455943}}, "brain": {"makespan": {"mean": 152.26991183660965, "std": 49.17992552801141, "min": 65.56342532113823, "max": 247.35157427911764}, "resource_utilization": {"mean": 0.38950759187140066, "std": 0.1443587812557533, "min": 0.1951499082397483, "max": 0.6477122733581975}, "load_balance_degree": {"mean": 0.817709298771593, "std": 0.34356645158570404, "min": 0.36950848557174065, "max": 1.9134805023567063}, "energy_consumption": {"mean": 27.432956933689525, "std": 6.818051113050087, "min": 17.638880894648093, "max": 41.30167021920775}}, "sipht": {"makespan": {"mean": 121.47673425942962, "std": 22.184457335924158, "min": 73.85742182048806, "max": 156.21359943144438}, "resource_utilization": {"mean": 0.6698801238126707, "std": 0.06269266537839047, "min": 0.5678006165649606, "max": 0.793808569208158}, "load_balance_degree": {"mean": 0.5090019821054068, "std": 0.16857624519730813, "min": 0.21284203812346844, "max": 1.030027813259129}, "energy_consumption": {"mean": 42.89095162704341, "std": 9.37608493380104, "min": 22.63882611159498, "max": 56.774811662928634}}, "epigenomics": {"makespan": {"mean": 90.05743690193043, "std": 16.25921038656383, "min": 49.80942333868865, "max": 112.39065727388585}, "resource_utilization": {"mean": 0.6497307138645116, "std": 0.16219676353294757, "min": 0.3374012433239021, "max": 1.0}, "load_balance_degree": {"mean": 0.48927070383560717, "std": 0.12787534985391225, "min": 0.21101749230537345, "max": 0.6891122450740561}, "energy_consumption": {"mean": 29.320595871678233, "std": 7.0262659041363404, "min": 17.153062728231063, "max": 39.504457855571076}}, "cybershake": {"makespan": {"mean": 271.51278441437034, "std": 92.09631946930585, "min": 117.24632740759358, "max": 452.35985166449024}, "resource_utilization": {"mean": 0.3982031357788506, "std": 0.07690335395293306, "min": 0.2727183574450093, "max": 0.5287732877693075}, "load_balance_degree": {"mean": 0.6782693275025831, "std": 0.233361025307699, "min": 0.34386945393343743, "max": 1.31234806432223}, "energy_consumption": {"mean": 54.54315607826625, "std": 11.935637178689682, "min": 31.821928310143768, "max": 73.65576938723002}}}, "aggregated_results": {"makespan": {"mean": 133.28273066638684, "std": 79.89722859266206}, "resource_utilization": {"mean": 0.6117345855588769, "std": 0.2074341733230105}, "load_balance_degree": {"mean": 0.6199760617036894, "std": 0.12008456443903684}, "energy_consumption": {"mean": 35.02070090174498, "std": 12.10024087983937}}}, "GCN": {"algorithm": "GCN", "results_by_workflow": {"montage": {"makespan": {"mean": 95.64278820619305, "std": 108.94098542669313, "min": 28.800544963325166, "max": 398.2672686556625}, "resource_utilization": {"mean": 0.6339060085930579, "std": 0.3358818835541154, "min": 0.09390646412946169, "max": 1.0}, "load_balance_degree": {"mean": 2.5705563413619084, "std": 0.2999924094501585, "min": 2.113104918628244, "max": 3.194914952973581}, "energy_consumption": {"mean": 20.586914344070067, "std": 6.223196127530782, "min": 11.795630479937422, "max": 33.15809236347991}}, "brain": {"makespan": {"mean": 524.6965064737199, "std": 309.60595041554114, "min": 144.32805136584977, "max": 1431.1128838972638}, "resource_utilization": {"mean": 0.12973643989824626, "std": 0.0656856495306889, "min": 0.034633274139117674, "max": 0.33387849693940924}, "load_balance_degree": {"mean": 2.325642188367593, "std": 0.24281152532858238, "min": 1.7022569573048998, "max": 2.6580212256963667}, "energy_consumption": {"mean": 27.405620002338157, "std": 7.928532643773915, "min": 17.22393412165862, "max": 44.12785349815057}}, "sipht": {"makespan": {"mean": 625.2258894345624, "std": 287.02590251491256, "min": 236.0897897442987, "max": 1561.7346838557612}, "resource_utilization": {"mean": 0.1445505066955225, "std": 0.035741189823003804, "min": 0.0717598180792157, "max": 0.2025739429545187}, "load_balance_degree": {"mean": 2.623274962233112, "std": 0.24523740625852963, "min": 2.1624012542156725, "max": 3.0754877225165136}, "energy_consumption": {"mean": 41.96203985516324, "std": 9.55712666652252, "min": 22.99510159482888, "max": 57.619843172744375}}, "epigenomics": {"makespan": {"mean": 311.03716589868395, "std": 240.41370034629793, "min": 133.47674951804703, "max": 1272.0691136857135}, "resource_utilization": {"mean": 0.2403575273523594, "std": 0.11442973109332046, "min": 0.055842333540308195, "max": 0.5028697397589869}, "load_balance_degree": {"mean": 2.3742795182379104, "std": 0.3062825805278209, "min": 1.5591641382970767, "max": 2.8386824224433544}, "energy_consumption": {"mean": 30.118121603090867, "std": 8.007000636277086, "min": 16.517431420898376, "max": 43.67731129399372}}, "cybershake": {"makespan": {"mean": 698.7950968782152, "std": 294.05095560567554, "min": 342.35498157251635, "max": 1537.1825706878947}, "resource_utilization": {"mean": 0.1646095292312923, "std": 0.053425292548608526, "min": 0.05028636658835789, "max": 0.2653603696575272}, "load_balance_degree": {"mean": 2.343588530382257, "std": 0.28961015256515493, "min": 1.8072365389882277, "max": 2.9696680301706038}, "energy_consumption": {"mean": 55.388530798186366, "std": 12.162463202122717, "min": 32.356663907057246, "max": 75.95135398650228}}}, "aggregated_results": {"makespan": {"mean": 451.07948937827496, "std": 220.47973011744833}, "resource_utilization": {"mean": 0.2626320023540957, "std": 0.18950114264556026}, "load_balance_degree": {"mean": 2.447468308116556, "std": 0.1241352924828262}, "energy_consumption": {"mean": 35.09224532056974, "std": 12.274885118923988}}}, "GAT": {"algorithm": "GAT", "results_by_workflow": {"montage": {"makespan": {"mean": 270.4865727437806, "std": 160.64109525373289, "min": 41.68198776070153, "max": 600.0783068861454}, "resource_utilization": {"mean": 0.21032996022138545, "std": 0.17290044291142562, "min": 0.08211543074523533, "max": 0.7438557591831608}, "load_balance_degree": {"mean": 2.320640572103374, "std": 0.2927942811717066, "min": 1.8355091411476636, "max": 2.8659790339905067}, "energy_consumption": {"mean": 21.18316676040883, "std": 5.924166669268347, "min": 11.482243810085642, "max": 29.38314750420682}}, "brain": {"makespan": {"mean": 438.118906594726, "std": 179.84183796398264, "min": 187.18380884155266, "max": 768.2571582217764}, "resource_utilization": {"mean": 0.1439430028633219, "std": 0.06134322722358535, "min": 0.053694306268264796, "max": 0.25042433971376693}, "load_balance_degree": {"mean": 2.1561356335148893, "std": 0.20712560082014866, "min": 1.7827640227617214, "max": 2.4905358961802646}, "energy_consumption": {"mean": 27.88719748208414, "std": 7.628561904546957, "min": 17.353186127350522, "max": 44.42950060527863}}, "sipht": {"makespan": {"mean": 624.2817443819687, "std": 384.67429513872156, "min": 151.41157887136205, "max": 1859.486798849955}, "resource_utilization": {"mean": 0.16791731058912576, "std": 0.07823499257171332, "min": 0.04949916811374749, "max": 0.35131331251216613}, "load_balance_degree": {"mean": 2.339761859533671, "std": 0.2937785985390915, "min": 1.78389710011594, "max": 2.739363194744356}, "energy_consumption": {"mean": 43.84631856209924, "std": 9.60617242795455, "min": 21.524770498361555, "max": 60.122615233642044}}, "epigenomics": {"makespan": {"mean": 335.7528098377328, "std": 179.16478116428914, "min": 133.39633450645633, "max": 664.7594909061773}, "resource_utilization": {"mean": 0.2242257280421049, "std": 0.12859568994677945, "min": 0.07820100323875678, "max": 0.5093425810919712}, "load_balance_degree": {"mean": 2.153408686796456, "std": 0.2247385868675249, "min": 1.5767668838197013, "max": 2.551784276003443}, "energy_consumption": {"mean": 29.590403490653074, "std": 7.839497124698353, "min": 17.510202622959383, "max": 43.82578457460778}}, "cybershake": {"makespan": {"mean": 925.9569113353919, "std": 382.46855931437545, "min": 476.3506060666611, "max": 1919.7179052109534}, "resource_utilization": {"mean": 0.12247222815538483, "std": 0.041570687868556236, "min": 0.06804738870284834, "max": 0.23168023012964054}, "load_balance_degree": {"mean": 2.308725199985025, "std": 0.26449236685501487, "min": 1.7888715741204528, "max": 2.800400812851513}, "energy_consumption": {"mean": 55.761559697787405, "std": 13.644116870981426, "min": 30.465100762373996, "max": 78.41682712615396}}}, "aggregated_results": {"makespan": {"mean": 518.91938897872, "std": 236.04166936899063}, "resource_utilization": {"mean": 0.17377764597426457, "std": 0.038568837211140496}, "load_balance_degree": {"mean": 2.255734390386683, "std": 0.08303242244306444}, "energy_consumption": {"mean": 35.65372919860654, "std": 12.473359440702879}}}, "DQN": {"algorithm": "DQN", "results_by_workflow": {"montage": {"makespan": {"mean": 202.66882531246088, "std": 89.14494107307507, "min": 73.78090544630159, "max": 510.46358267342424}, "resource_utilization": {"mean": 0.19913551620161551, "std": 0.06307919055562912, "min": 0.08494582679248172, "max": 0.3256219421310957}, "load_balance_degree": {"mean": 1.8731272621941115, "std": 0.27937622462509587, "min": 1.5161655610009146, "max": 2.6882266981380507}, "energy_consumption": {"mean": 20.79903469191772, "std": 5.43733463533404, "min": 12.741858790962382, "max": 29.66131931967222}}, "brain": {"makespan": {"mean": 268.46335004988293, "std": 91.27140621670692, "min": 130.438285630122, "max": 496.38424060600664}, "resource_utilization": {"mean": 0.21976205314049874, "std": 0.07815766325686624, "min": 0.13113468943069856, "max": 0.36943166358447355}, "load_balance_degree": {"mean": 1.7641872558157217, "std": 0.2720293169244252, "min": 1.4436911666207002, "max": 2.6736028345300356}, "energy_consumption": {"mean": 27.608489109266554, "std": 7.454967793350716, "min": 16.807646969148585, "max": 45.68652729052363}}, "sipht": {"makespan": {"mean": 374.5715685104973, "std": 99.7122170753483, "min": 146.89561769516433, "max": 588.9926414020074}, "resource_utilization": {"mean": 0.2251362054851535, "std": 0.041522415072342875, "min": 0.15660713085634936, "max": 0.3098452393472468}, "load_balance_degree": {"mean": 1.6792866693775477, "std": 0.1569145590001742, "min": 1.4005347032820568, "max": 1.9218381068671597}, "energy_consumption": {"mean": 42.727987904426044, "std": 9.78742614850808, "min": 21.44546486816155, "max": 55.254732886519534}}, "epigenomics": {"makespan": {"mean": 341.8218605737362, "std": 106.91614815740391, "min": 176.79869376901294, "max": 531.7902858636487}, "resource_utilization": {"mean": 0.18678847106052804, "std": 0.07835888285596432, "min": 0.08595351880229411, "max": 0.3643837568589986}, "load_balance_degree": {"mean": 1.7799371674064504, "std": 0.22668327996645862, "min": 1.4503034139619428, "max": 2.211266899610132}, "energy_consumption": {"mean": 28.62924605701239, "std": 6.759384284131098, "min": 17.54303939851409, "max": 39.44005539134227}}, "cybershake": {"makespan": {"mean": 720.1947811936055, "std": 311.34114310968533, "min": 310.9866659818584, "max": 1587.4341201220468}, "resource_utilization": {"mean": 0.15558076747618726, "std": 0.03971768640590528, "min": 0.10330732475798668, "max": 0.2668386886115328}, "load_balance_degree": {"mean": 1.7807178728984614, "std": 0.23437769467406827, "min": 1.3014246690403368, "max": 2.458731220292335}, "energy_consumption": {"mean": 54.14286617481529, "std": 12.765153416331923, "min": 30.70598297084067, "max": 74.15298378562237}}}, "aggregated_results": {"makespan": {"mean": 381.5440771280366, "std": 179.49617058707602}, "resource_utilization": {"mean": 0.19728060267279662, "std": 0.025034983557272295}, "load_balance_degree": {"mean": 1.7754512455384586, "std": 0.061584037316745985}, "energy_consumption": {"mean": 34.7815247874876, "std": 12.023483855162036}}}, "TLF-GNN": {"algorithm": "TLF-GNN", "results_by_workflow": {"montage": {"makespan": {"mean": 113.09359693611097, "std": 52.5755349033532, "min": 50.173356645023915, "max": 255.9127721488175}, "resource_utilization": {"mean": 0.3584492794390801, "std": 0.1105834038355501, "min": 0.14837240136101085, "max": 0.6452443070424831}, "load_balance_degree": {"mean": 0.49428947829343484, "std": 0.18455274672614472, "min": 0.23434373541491307, "max": 0.9619259578943942}, "energy_consumption": {"mean": 20.922465513087367, "std": 5.60984493056213, "min": 12.238897815906576, "max": 29.639019419825726}}, "brain": {"makespan": {"mean": 296.91776811631956, "std": 105.92383767729521, "min": 115.9535405012536, "max": 537.137762626859}, "resource_utilization": {"mean": 0.2027103565051501, "std": 0.07791256294763244, "min": 0.092296542570034, "max": 0.40991197333761376}, "load_balance_degree": {"mean": 0.4982722957662646, "std": 0.1333950577510184, "min": 0.2200833804884587, "max": 0.7111656099051166}, "energy_consumption": {"mean": 27.597682418983133, "std": 7.0554705203908705, "min": 17.884712526636374, "max": 40.8661118539928}}, "sipht": {"makespan": {"mean": 270.05766012899306, "std": 51.87661803262616, "min": 186.87016242506604, "max": 361.2666367955402}, "resource_utilization": {"mean": 0.3091715178834782, "std": 0.07260158388965018, "min": 0.16755862355684106, "max": 0.4575221697860439}, "load_balance_degree": {"mean": 0.40953736034998095, "std": 0.11278462954341817, "min": 0.2341933118235568, "max": 0.7683120416191183}, "energy_consumption": {"mean": 43.21915362939701, "std": 9.624075946724524, "min": 21.969110272478773, "max": 57.136574236106}}, "epigenomics": {"makespan": {"mean": 241.26664919659237, "std": 63.19686061322699, "min": 148.9599426116254, "max": 358.1973993916865}, "resource_utilization": {"mean": 0.25015797523291206, "std": 0.07504538007776111, "min": 0.14600360122987707, "max": 0.44344344710881023}, "load_balance_degree": {"mean": 0.485214831298444, "std": 0.1377742938496348, "min": 0.2304611061146457, "max": 0.7207286099564917}, "energy_consumption": {"mean": 29.582903825311234, "std": 6.844700894048043, "min": 17.191364931636215, "max": 39.97790243004863}}, "cybershake": {"makespan": {"mean": 608.1801060365792, "std": 178.71940737725592, "min": 261.0928362194776, "max": 1057.9538525827709}, "resource_utilization": {"mean": 0.17524187401400834, "std": 0.037682670389215356, "min": 0.11079478932291004, "max": 0.25014128572940775}, "load_balance_degree": {"mean": 0.3974010295025563, "std": 0.09246667828058566, "min": 0.17933695404788244, "max": 0.5344855105244015}, "energy_consumption": {"mean": 54.615452946754225, "std": 11.989262609662575, "min": 31.560677852016315, "max": 73.71540911423327}}}, "aggregated_results": {"makespan": {"mean": 305.90315608291905, "std": 163.7622638895991}, "resource_utilization": {"mean": 0.25914620061492577, "std": 0.0673340367867986}, "load_balance_degree": {"mean": 0.45694299904213614, "std": 0.04403341343972998}, "energy_consumption": {"mean": 35.18753166670659, "std": 12.119908034520261}}}}}