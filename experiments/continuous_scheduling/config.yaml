# 连续时刻任务调度实验配置
data:
  num_workflows: 2000          # 增加工作流数量
  workflow_types: 
    - montage
    - cybershake
    - ligo
    - sipht
  min_tasks: 2000              # 每个工作流的任务数量增加到2000
  max_tasks: 2000
  min_nodes: 100               # 节点数量增加到100
  max_nodes: 100
  heterogeneity_factor: 0.5
  # 连续时刻调度参数
  time_slots: 50               # 时间槽数量
  arrival_rate: 0.8            # 任务到达率
  dynamic_load_factor: 0.3     # 动态负载因子

model:
  hidden_dim: 512              # 增加隐藏层维度
  num_heads: 16                # 增加注意力头数
  num_layers: 8                # 增加层数
  dropout: 0.1
  activation: relu
  constraint_weight: 1.0
  # 连续调度特定参数
  temporal_attention: true      # 启用时间注意力
  memory_size: 1000            # 记忆大小
  prediction_horizon: 10       # 预测时域

training:
  batch_size: 64               # 增加批次大小
  learning_rate: 0.0005        # 降低学习率
  num_epochs: 2000             # 增加训练轮次到2000
  weight_decay: 0.0001
  patience: 20                 # 增加早停耐心值
  # 连续训练参数
  sequence_length: 20          # 序列长度
  warmup_steps: 1000          # 预热步数
  gradient_accumulation: 4     # 梯度累积

evaluation:
  metrics:
    - makespan
    - resource_utilization
    - load_balance
    - energy_consumption
    - temporal_efficiency      # 新增时间效率指标
    - throughput              # 新增吞吐量指标
  convergence_plot: true       # 启用收敛曲线
  comparison_methods:          # 对比算法
    - HEFT
    - CPOP
    - Random
    - RoundRobin
    - BasicGCN
    - BasicGAT
    - GNN-WS
    - TemporalGNN             # 新增时间GNN
    - LSTM_Scheduler          # 新增LSTM调度器
    - Transformer_Scheduler   # 新增Transformer调度器

output_dir: "./experiments/continuous_scheduling/outputs"
log_level: "INFO" 