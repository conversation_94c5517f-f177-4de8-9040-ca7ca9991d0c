# 连续时刻任务调度实验配置
data:
  num_workflows: 100           # 临时减少工作流数量
  workflow_types: 
    - montage
    - cybershake
    - ligo
    - sipht
  min_tasks: 100              # 临时减少任务数量
  max_tasks: 100
  min_nodes: 20               # 临时减少节点数量
  max_nodes: 20
  heterogeneity_factor: 0.5
  # 连续时刻调度参数
  time_slots: 20              # 临时减少时间槽
  arrival_rate: 0.8            # 任务到达率
  dynamic_load_factor: 0.3     # 动态负载因子

model:
  hidden_dim: 128              # 临时减少隐藏层维度
  num_heads: 8                 # 临时减少注意力头数
  num_layers: 4                # 临时减少层数
  dropout: 0.1
  activation: relu
  constraint_weight: 1.0
  # 连续调度特定参数
  temporal_attention: true      # 启用时间注意力
  memory_size: 1000            # 记忆大小
  prediction_horizon: 10       # 预测时域

training:
  batch_size: 16               # 临时减少批次大小
  learning_rate: 0.001         # 临时增加学习率
  num_epochs: 50               # 临时减少训练轮次
  weight_decay: 0.0001
  patience: 10                 # 临时减少早停耐心值
  # 连续训练参数
  sequence_length: 10          # 临时减少序列长度
  warmup_steps: 100           # 临时减少预热步数
  gradient_accumulation: 2     # 临时减少梯度累积

evaluation:
  metrics:
    - makespan
    - resource_utilization
    - load_balance
    - energy_consumption
    - temporal_efficiency      # 新增时间效率指标
    - throughput              # 新增吞吐量指标
  convergence_plot: true       # 启用收敛曲线
  comparison_methods:          # 对比算法
    - HEFT
    - CPOP
    - Random
    - RoundRobin
    - BasicGCN
    - BasicGAT
    - GNN-WS
    - TemporalGNN             # 新增时间GNN
    - LSTM_Scheduler          # 新增LSTM调度器
    - Transformer_Scheduler   # 新增Transformer调度器

output_dir: "./experiments/continuous_scheduling/outputs"
log_level: "INFO" 