# 连续时刻任务调度实验

## 实验概述

本实验旨在研究连续时刻任务调度问题，通过对比多种深度学习算法在动态调度场景下的性能表现。

## 实验特点

### 1. 连续时刻调度
- **时间序列处理**: 支持任务在时间维度上的动态到达和调度
- **动态负载管理**: 实时监控节点负载并进行任务重分配
- **时间依赖建模**: 考虑任务间的时间依赖关系

### 2. 大规模实验设置
- **任务数量**: 2000个任务/工作流
- **节点数量**: 100个计算节点
- **训练轮次**: 2000轮训练
- **时间槽**: 50个时间槽

### 3. 多种算法对比
- **TemporalGNN**: 时间图神经网络
- **LSTM_Scheduler**: LSTM调度器
- **Transformer_Scheduler**: Transformer调度器
- **传统启发式算法**: HEFT、CPOP等

## 文件结构

```
experiments/continuous_scheduling/
├── config.yaml                 # 实验配置文件
├── continuous_scheduler.py     # 连续调度器核心实现
├── run_experiment.py          # 实验运行脚本
├── README.md                  # 说明文档
└── outputs/                   # 输出结果目录
    ├── convergence_curves.png     # 收敛曲线
    ├── algorithm_comparison.png   # 算法对比图
    ├── experiment_summary.png     # 实验总结图
    ├── training_results.json      # 训练结果
    └── experiment_report.md       # 实验报告
```

## 运行方法

### 1. 基本运行
```bash
python experiments/continuous_scheduling/run_experiment.py
```

### 2. 自定义配置
```bash
python experiments/continuous_scheduling/run_experiment.py \
    --config experiments/continuous_scheduling/config.yaml \
    --output experiments/continuous_scheduling/outputs
```

### 3. 生成可视化总结
```bash
python experiments/continuous_scheduling/run_experiment.py --summary
```

## 实验配置

### 数据配置
- `num_workflows`: 2000 (工作流数量)
- `min_tasks`: 2000 (每个工作流的任务数量)
- `min_nodes`: 100 (节点数量)
- `time_slots`: 50 (时间槽数量)
- `arrival_rate`: 0.8 (任务到达率)

### 模型配置
- `hidden_dim`: 512 (隐藏层维度)
- `num_heads`: 16 (注意力头数)
- `num_layers`: 8 (网络层数)
- `temporal_attention`: true (启用时间注意力)

### 训练配置
- `num_epochs`: 2000 (训练轮次)
- `batch_size`: 64 (批次大小)
- `learning_rate`: 0.0005 (学习率)
- `sequence_length`: 20 (序列长度)

## 评估指标

### 1. 传统指标
- **Makespan**: 完工时间
- **Resource Utilization**: 资源利用率
- **Load Balance**: 负载均衡

### 2. 时间相关指标
- **Temporal Efficiency**: 时间效率
- **Throughput**: 吞吐量

## 算法特点

### TemporalGNN
- **优势**: 结合时间信息的图神经网络，适合处理动态任务依赖
- **适用场景**: 复杂工作流，强时间依赖关系

### LSTM_Scheduler
- **优势**: 长短期记忆网络，适合处理序列化调度决策
- **适用场景**: 连续时间序列，历史依赖重要

### Transformer_Scheduler
- **优势**: 基于注意力机制的调度器，适合处理长序列依赖
- **适用场景**: 长序列任务，全局依赖关系

## 实验结果

### 收敛曲线
- 训练损失随轮次的变化趋势
- 各指标在训练过程中的收敛情况
- 不同算法的收敛速度和稳定性对比

### 算法对比
- 最终性能指标的柱状图对比
- 综合评分的雷达图
- 收敛速度和稳定性的量化分析

### 实验报告
- 详细的实验配置和参数
- 各算法的性能数据表格
- 算法分析和结论

## 技术特点

### 1. 时间建模
- 任务到达时间建模
- 执行时间预测
- 时间依赖关系处理

### 2. 动态调度
- 实时负载监控
- 任务重分配机制
- 资源动态调整

### 3. 深度学习
- 多种神经网络架构
- 注意力机制应用
- 序列建模能力

## 扩展方向

### 1. 算法扩展
- 强化学习调度器
- 图注意力网络
- 多任务学习框架

### 2. 场景扩展
- 多目标优化
- 不确定性建模
- 分布式调度

### 3. 评估扩展
- 更多性能指标
- 鲁棒性测试
- 可扩展性分析

## 注意事项

1. **硬件要求**: 建议使用GPU进行训练，CPU训练时间较长
2. **内存需求**: 大规模实验需要足够的内存和显存
3. **时间成本**: 完整实验可能需要数小时到数天
4. **结果解释**: 注意区分随机性和算法性能差异

## 联系信息

如有问题或建议，请联系实验负责人。 