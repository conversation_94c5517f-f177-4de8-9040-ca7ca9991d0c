import os
import numpy as np
import json
import matplotlib.pyplot as plt
from src.models.baseline_models import HEFTScheduler, CPOPScheduler, RandomScheduler, RoundRobinScheduler, GAScheduler, PSOScheduler, DQNScheduler, CGWSAScheduler
from src.models.three_layer_gnn import ThreeLayerGNNScheduler
from src.evaluation.metrics import SchedulingMetrics

# 假设已处理数据格式与main.py一致
def load_processed_workflows(path):
    return np.load(path, allow_pickle=True).tolist()

def run_all_methods_on_workflow(workflow, methods, gnn_config, device):
    results = []
    for name, scheduler in methods:
        if name == 'GNN-WS':
            # 构造batch_data，调用GNN模型
            from src.training.trainer import prepare_batch_data
            batch_data = prepare_batch_data(workflow, device)
            model = ThreeLayerGNNScheduler(gnn_config).to(device)
            model.eval()
            with torch.no_grad():
                assignment_probs, _ = model(batch_data, debug_mode=False)
                assignment = np.argmax(assignment_probs.cpu().numpy()[0], axis=1)
                assignment = {i: int(a) for i, a in enumerate(assignment)}
        else:
            assignment = scheduler.schedule(
                nx.node_link_graph(workflow['original_data']['dag']),
                workflow['original_data']['nodes']
            )
        metrics = SchedulingMetrics().compute_metrics(assignment, workflow)
        results.append({
            'workflow_id': workflow['id'],
            'workflow_type': workflow['type'],
            'method': name,
            **metrics,
            'assignment': assignment
        })
    return results

def main():
    import torch
    import networkx as nx
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--data', type=str, default='outputs/processed_workflows.npy')
    parser.add_argument('--output', type=str, default='outputs/comparison_results.json')
    parser.add_argument('--device', type=str, default='cpu')
    args = parser.parse_args()

    processed_workflows = load_processed_workflows(args.data)
    device = torch.device(args.device)
    gnn_config = {
        'input_dim': 128,
        'hidden_dim': 256,
        'num_heads': 8,
        'num_transformer_layers': 6,
        'num_gat_layers': 3,
        'dropout': 0.1,
        'constraint_weights': {
            'dependency': 1.0,
            'resource': 1.0,
            'temporal': 0.5,
            'communication': 0.5
        }
    }
    methods = [
        ('HEFT', HEFTScheduler()),
        ('CPOP', CPOPScheduler()),
        ('Random', RandomScheduler()),
        ('RoundRobin', RoundRobinScheduler()),
        ('GA-WS', GAScheduler()),
        ('PSO-WS', PSOScheduler()),
        ('DQN-Scheduler', DQNScheduler()),
        ('CGWSA', CGWSAScheduler()),
        ('GNN-WS', None),
    ]
    all_results = []
    for workflow in processed_workflows:
        all_results.extend(run_all_methods_on_workflow(workflow, methods, gnn_config, device))
    # 保存结果
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    with open(args.output, 'w') as f:
        json.dump(all_results, f, indent=2)
    print(f"All comparison results saved to {args.output}")

    # 可视化
    import pandas as pd
    df = pd.DataFrame(all_results)
    for metric in ['makespan', 'resource_utilization', 'load_balance', 'energy_consumption']:
        plt.figure(figsize=(10,6))
        for method in df['method'].unique():
            vals = df[df['method']==method][metric]
            plt.plot(range(len(vals)), vals, marker='o', label=method)
        plt.title(f'Comparison of {metric}')
        plt.xlabel('Workflow Instance')
        plt.ylabel(metric.replace('_',' ').title())
        plt.legend()
        plt.tight_layout()
        plt.savefig(f"outputs/visualizations/comparison_{metric}.png", dpi=300)
    print("All comparison plots saved to outputs/visualizations/")

if __name__ == '__main__':
    main()