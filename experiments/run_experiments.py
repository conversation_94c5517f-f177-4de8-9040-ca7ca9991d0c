import unittest
import torch
import torch.nn as nn
import networkx as nx
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.models.three_layer_gnn import ThreeLayerGNNScheduler
from src.models.baseline_models import HEFTScheduler, CPOPScheduler


class TestThreeLayerGNN(unittest.TestCase):
    """三层GNN模型测试"""

    def setUp(self):
        """设置测试环境"""
        self.config = {
            'input_dim': 128,
            'hidden_dim': 64,  # 减小维度以加速测试
            'num_heads': 4,
            'num_transformer_layers': 2,
            'num_gat_layers': 2,
            'dropout': 0.1,
            'constraint_weights': {
                'dependency': 1.0,
                'resource': 1.0,
                'temporal': 0.5,
                'communication': 0.5
            }
        }

        self.model = ThreeLayerGNNScheduler(self.config)

        # 创建测试数据
        self.batch_size = 2
        self.max_tasks = 5
        self.max_nodes = 3

        self.batch_data = {
            'task_features': torch.randn(self.batch_size, self.max_tasks, 128),
            'node_features': torch.randn(self.max_nodes * self.batch_size, 128),
            'adjacency_matrix': torch.zeros(self.batch_size, self.max_tasks, self.max_tasks),
            'task_edge_index': torch.tensor([[0, 1], [1, 2]]).T.long(),
            'task_batch': torch.zeros(self.max_tasks * self.batch_size, dtype=torch.long),
            'node_batch': torch.zeros(self.max_nodes * self.batch_size, dtype=torch.long),
            'resource_constraints': torch.randn(self.batch_size, self.max_nodes, 4),
            'constraint_data': {
                'dependency': torch.randn(self.batch_size, self.max_tasks, 128),
                'resource': torch.randn(self.batch_size, self.max_nodes, 128),
                'adjacency_matrix': torch.zeros(self.batch_size, self.max_tasks, self.max_tasks),
                'task_demands': torch.randn(self.batch_size, self.max_tasks, 4),
                'node_capacities': torch.randn(self.batch_size, self.max_nodes, 4),
            },
            'ground_truth_assignments': torch.randint(0, self.max_nodes, (self.batch_size, self.max_tasks))
        }

        # 设置批次索引
        for b in range(self.batch_size):
            start_task = b * self.max_tasks
            end_task = (b + 1) * self.max_tasks
            start_node = b * self.max_nodes
            end_node = (b + 1) * self.max_nodes

            self.batch_data['task_batch'][start_task:end_task] = b
            self.batch_data['node_batch'][start_node:end_node] = b

    def test_model_forward_pass(self):
        """测试模型前向传播"""
        self.model.eval()

        with torch.no_grad():
            assignment_probs, debug_info = self.model(self.batch_data, debug_mode=True)

        # 检查输出形状
        expected_shape = (self.batch_size, self.max_tasks, self.max_nodes)
        self.assertEqual(assignment_probs.shape, expected_shape)

        # 检查概率约束
        prob_sums = assignment_probs.sum(dim=-1)
        self.assertTrue(torch.allclose(prob_sums, torch.ones_like(prob_sums), atol=1e-5))

        # 检查调试信息
        self.assertIn('layer_outputs', debug_info)
        self.assertIn('losses', debug_info)

    def test_model_training_step(self):
        """测试模型训练步骤"""
        self.model.train()

        # 前向传播
        assignment_probs, debug_info = self.model(self.batch_data, debug_mode=False)

        # 计算损失
        ground_truth = self.batch_data['ground_truth_assignments']
        constraint_losses = debug_info['losses']['final_constraint_losses']

        losses = self.model.compute_total_loss(assignment_probs, ground_truth, constraint_losses)

        # 检查损失
        self.assertIn('total', losses)
        self.assertIn('assignment', losses)
        self.assertIn('constraint_total', losses)

        total_loss = losses['total']
        self.assertIsInstance(total_loss, torch.Tensor)
        self.assertEqual(total_loss.dim(), 0)  # 标量

        # 测试反向传播
        total_loss.backward()

        # 检查梯度
        has_gradients = False
        for param in self.model.parameters():
            if param.grad is not None:
                has_gradients = True
                break

        self.assertTrue(has_gradients, "模型参数应该有梯度")


class TestBaselineModels(unittest.TestCase):
    """基线模型测试"""

    def setUp(self):
        """设置测试环境"""
        # 创建测试DAG
        self.test_dag = nx.DiGraph()

        tasks_data = [
            {'id': 0, 'runtime': 10},
            {'id': 1, 'runtime': 8},
            {'id': 2, 'runtime': 12},
        ]

        for task_data in tasks_data:
            self.test_dag.add_node(task_data['id'], **task_data)

        self.test_dag.add_edges_from([(0, 1), (0, 2)])

        # 创建测试节点
        self.test_nodes = [
            {'cpu_capacity': 2.0, 'memory_capacity': 8.0},
            {'cpu_capacity': 4.0, 'memory_capacity': 4.0},
        ]

    def test_heft_scheduler(self):
        """测试HEFT调度器"""
        heft = HEFTScheduler()
        assignments = heft.schedule(self.test_dag, self.test_nodes)

        # 检查所有任务都被分配
        self.assertEqual(len(assignments), len(self.test_dag.nodes()))

        # 检查分配的节点ID有效
        for task_id, node_id in assignments.items():
            self.assertIn(task_id, self.test_dag.nodes())
            self.assertGreaterEqual(node_id, 0)
            self.assertLess(node_id, len(self.test_nodes))

    def test_cpop_scheduler(self):
        """测试CPOP调度器"""
        cpop = CPOPScheduler()
        assignments = cpop.schedule(self.test_dag, self.test_nodes)

        # 检查所有任务都被分配
        self.assertEqual(len(assignments), len(self.test_dag.nodes()))

        # 检查分配的节点ID有效
        for task_id, node_id in assignments.items():
            self.assertIn(task_id, self.test_dag.nodes())
            self.assertGreaterEqual(node_id, 0)
            self.assertLess(node_id, len(self.test_nodes))


if __name__ == '__main__':
    unittest.main()