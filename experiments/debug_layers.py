import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from typing import Dict, Any, List
import json


class LayerDebugger:
    """层级调试器"""

    def __init__(self, model: nn.Module):
        self.model = model
        self.activations = {}
        self.gradients = {}
        self.hooks = []

    def register_hooks(self):
        """注册钩子函数"""

        def save_activation(name):
            def hook(module, input, output):
                if isinstance(output, torch.Tensor):
                    self.activations[name] = output.detach().cpu()
                elif isinstance(output, tuple):
                    self.activations[name] = [o.detach().cpu() if isinstance(o, torch.Tensor) else o
                                              for o in output]

            return hook

        def save_gradient(name):
            def hook(module, grad_input, grad_output):
                if grad_output[0] is not None:
                    self.gradients[name] = grad_output[0].detach().cpu()

            return hook

        # 为每一层注册钩子
        for name, module in self.model.named_modules():
            if len(list(module.children())) == 0:  # 叶子节点
                hook_handle = module.register_forward_hook(save_activation(name))
                self.hooks.append(hook_handle)

                hook_handle = module.register_backward_hook(save_gradient(name))
                self.hooks.append(hook_handle)

    def remove_hooks(self):
        """移除钩子函数"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()

    def debug_layer_outputs(self, batch_data: Dict[str, torch.Tensor],
                            save_dir: str) -> Dict[str, Any]:
        """调试层输出"""

        print("🔍 开始层级调试...")

        # 注册钩子
        self.register_hooks()

        try:
            # 前向传播
            self.model.eval()
            with torch.no_grad():
                assignment_probs, debug_info = self.model(batch_data, debug_mode=True)

            # 分析激活值
            activation_stats = self._analyze_activations()

            # 保存调试结果
            self._save_activation_analysis(activation_stats, save_dir)
            self._plot_activation_distributions(save_dir)
            self._save_layer_outputs(debug_info, save_dir)

            return {
                'activation_stats': activation_stats,
                'debug_info': debug_info,
                'assignment_probs': assignment_probs
            }

        finally:
            # 清理钩子
            self.remove_hooks()

    def _analyze_activations(self) -> Dict[str, Dict[str, float]]:
        """分析激活值统计"""

        stats = {}

        for layer_name, activation in self.activations.items():
            if isinstance(activation, torch.Tensor) and activation.numel() > 0:
                layer_stats = {
                    'mean': float(activation.mean()),
                    'std': float(activation.std()),
                    'min': float(activation.min()),
                    'max': float(activation.max()),
                    'zero_fraction': float((activation == 0).float().mean()),
                    'negative_fraction': float((activation < 0).float().mean()),
                    'shape': list(activation.shape),
                    'num_elements': int(activation.numel())
                }

                # 检查异常值
                if torch.isnan(activation).any():
                    layer_stats['has_nan'] = True
                    layer_stats['nan_count'] = int(torch.isnan(activation).sum())

                if torch.isinf(activation).any():
                    layer_stats['has_inf'] = True
                    layer_stats['inf_count'] = int(torch.isinf(activation).sum())

                stats[layer_name] = layer_stats

        return stats

    def _save_activation_analysis(self, stats: Dict[str, Dict[str, float]], save_dir: str):
        """保存激活值分析结果"""

        os.makedirs(save_dir, exist_ok=True)

        # 保存JSON格式的统计信息
        with open(os.path.join(save_dir, 'activation_stats.json'), 'w') as f:
            json.dump(stats, f, indent=2)

        # 创建可读的报告
        report_lines = ["# 层级激活值分析报告\n"]

        for layer_name, layer_stats in stats.items():
            report_lines.append(f"## {layer_name}")
            report_lines.append(f"- 形状: {layer_stats['shape']}")
            report_lines.append(f"- 均值: {layer_stats['mean']:.6f}")
            report_lines.append(f"- 标准差: {layer_stats['std']:.6f}")
            report_lines.append(f"- 范围: [{layer_stats['min']:.6f}, {layer_stats['max']:.6f}]")
            report_lines.append(f"- 零值比例: {layer_stats['zero_fraction']:.3f}")
            report_lines.append(f"- 负值比例: {layer_stats['negative_fraction']:.3f}")

            if layer_stats.get('has_nan', False):
                report_lines.append(f"- ⚠️ 包含NaN值: {layer_stats['nan_count']}")

            if layer_stats.get('has_inf', False):
                report_lines.append(f"- ⚠️ 包含Inf值: {layer_stats['inf_count']}")

            report_lines.append("")

        with open(os.path.join(save_dir, 'activation_report.md'), 'w') as f:
            f.write('\n'.join(report_lines))

    def _plot_activation_distributions(self, save_dir: str):
        """绘制激活值分布图"""

        # 选择要可视化的层（避免太多图）
        selected_layers = list(self.activations.keys())[:12]  # 最多12个

        if not selected_layers:
            return

        # 计算子图布局
        n_layers = len(selected_layers)
        n_cols = min(4, n_layers)
        n_rows = (n_layers + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(4 * n_cols, 3 * n_rows))
        if n_rows == 1:
            axes = [axes] if n_cols == 1 else axes
        elif n_cols == 1:
            axes = [[ax] for ax in axes]

        for i, layer_name in enumerate(selected_layers):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row][col] if n_rows > 1 else axes[col]

            activation = self.activations[layer_name]
            if isinstance(activation, torch.Tensor) and activation.numel() > 0:
                # 展平并采样（避免内存问题）
                flat_values = activation.flatten()
                if len(flat_values) > 10000:
                    indices = torch.randperm(len(flat_values))[:10000]
                    flat_values = flat_values[indices]

                flat_values = flat_values.numpy()

                # 绘制直方图
                ax.hist(flat_values, bins=50, alpha=0.7, density=True)
                ax.set_title(f'{layer_name}\n(mean={flat_values.mean():.3f})', fontsize=8)
                ax.set_xlabel('Activation Value')
                ax.set_ylabel('Density')
                ax.grid(True, alpha=0.3)

        # 隐藏多余的子图
        for i in range(n_layers, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row][col] if n_rows > 1 else axes[col]
            ax.set_visible(False)

        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'activation_distributions.png'),
                    dpi=300, bbox_inches='tight')
        plt.close()

        print(f"   激活值分布图保存到: {os.path.join(save_dir, 'activation_distributions.png')}")

    def _save_layer_outputs(self, debug_info: Dict[str, Any], save_dir: str):
        """保存层输出信息"""

        layer_outputs = debug_info.get('layer_outputs', {})

        # 保存每层的输出统计
        layer_output_stats = {}

        for layer_name, output in layer_outputs.items():
            if isinstance(output, torch.Tensor):
                stats = {
                    'shape': list(output.shape),
                    'mean': float(output.mean()),
                    'std': float(output.std()),
                    'min': float(output.min()),
                    'max': float(output.max()),
                }
                layer_output_stats[layer_name] = stats

        with open(os.path.join(save_dir, 'layer_outputs.json'), 'w') as f:
            json.dump(layer_output_stats, f, indent=2)

    def debug_all_layers(self, batch_data: Dict[str, torch.Tensor], save_dir: str):
        """调试所有层"""

        print("🔧 开始全面层级调试...")

        # 第一层：DAG Transformer
        print("🔍 调试第一层：DAG Transformer")
        transformer_output = self._debug_dag_transformer(batch_data, save_dir)

        # 第二层：PINN约束层
        print("🔍 调试第二层：PINN约束层")
        constraint_output = self._debug_pinn_constraints(batch_data, save_dir)

        # 第三层：GAT调度器
        print("🔍 调试第三层：GAT调度器")
        gat_output = self._debug_gat_scheduler(batch_data, save_dir)

        # 整体调试
        print("🔍 调试整体模型")
        overall_debug = self.debug_layer_outputs(batch_data, save_dir)

        print("✅ 全面层级调试完成")

        return {
            'transformer': transformer_output,
            'constraints': constraint_output,
            'gat': gat_output,
            'overall': overall_debug
        }

    def _debug_dag_transformer(self, batch_data: Dict[str, torch.Tensor], save_dir: str):
        """调试DAG Transformer层"""

        transformer_dir = os.path.join(save_dir, 'dag_transformer')
        os.makedirs(transformer_dir, exist_ok=True)

        try:
            # 单独测试transformer层
            task_features = batch_data['task_features']
            adjacency_matrix = batch_data['adjacency_matrix']

            transformer_output = self.model.dag_transformer(task_features, adjacency_matrix)

            # 分析输出
            stats = {
                'input_shape': list(task_features.shape),
                'output_shape': list(transformer_output.shape),
                'output_mean': float(transformer_output.mean()),
                'output_std': float(transformer_output.std()),
                'attention_patterns': 'analyzed'  # 可以添加注意力模式分析
            }

            with open(os.path.join(transformer_dir, 'transformer_debug.json'), 'w') as f:
                json.dump(stats, f, indent=2)

            print(f"   Transformer调试结果保存到: {transformer_dir}")
            return stats

        except Exception as e:
            print(f"   ⚠️ Transformer调试失败: {e}")
            return {'error': str(e)}

    def _debug_pinn_constraints(self, batch_data: Dict[str, torch.Tensor], save_dir: str):
        """调试PINN约束层"""

        constraint_dir = os.path.join(save_dir, 'pinn_constraints')
        os.makedirs(constraint_dir, exist_ok=True)

        try:
            # 测试约束层
            task_features = batch_data['task_features']
            constraint_data = batch_data['constraint_data']

            # 模拟分配概率
            batch_size, num_tasks, _ = task_features.shape
            num_nodes = batch_data['node_features'].shape[0]
            mock_assignment = torch.softmax(torch.randn(batch_size, num_tasks, num_nodes), dim=-1)

            enhanced_features, constraint_losses = self.model.pinn_constraint_layer(
                task_features, constraint_data, mock_assignment
            )

            # 分析约束损失
            stats = {
                'constraint_losses': {k: float(v) if isinstance(v, torch.Tensor) else v
                                      for k, v in constraint_losses.items()},
                'input_shape': list(task_features.shape),
                'output_shape': list(enhanced_features.shape),
                'enhancement_effect': float((enhanced_features - task_features).abs().mean())
            }

            with open(os.path.join(constraint_dir, 'constraint_debug.json'), 'w') as f:
                json.dump(stats, f, indent=2)

            print(f"   约束层调试结果保存到: {constraint_dir}")
            return stats

        except Exception as e:
            print(f"   ⚠️ 约束层调试失败: {e}")
            return {'error': str(e)}

    def _debug_gat_scheduler(self, batch_data: Dict[str, torch.Tensor], save_dir: str):
        """调试GAT调度器"""

        gat_dir = os.path.join(save_dir, 'gat_scheduler')
        os.makedirs(gat_dir, exist_ok=True)

        try:
            # 测试GAT调度器
            node_features = batch_data['node_features']
            task_edge_index = batch_data['task_edge_index']
            task_batch = batch_data['task_batch']
            node_batch = batch_data['node_batch']
            resource_constraints = batch_data['resource_constraints']

            # 模拟任务特征
            num_tasks = len(task_batch)
            mock_task_features = torch.randn(num_tasks, self.model.hidden_dim)

            assignment_probs, gat_debug_info = self.model.gat_scheduler(
                mock_task_features, node_features, task_edge_index,
                task_batch, node_batch, resource_constraints
            )

            stats = {
                'assignment_shape': list(assignment_probs.shape),
                'assignment_sum': float(assignment_probs.sum(dim=-1).mean()),
                'assignment_entropy': float(
                    -(assignment_probs * torch.log(assignment_probs + 1e-8)).sum(dim=-1).mean()),
                'debug_info_keys': list(gat_debug_info.keys())
            }

            with open(os.path.join(gat_dir, 'gat_debug.json'), 'w') as f:
                json.dump(stats, f, indent=2)

            print(f"   GAT调试结果保存到: {gat_dir}")
            return stats

        except Exception as e:
            print(f"   ⚠️ GAT调试失败: {e}")
            return {'error': str(e)}