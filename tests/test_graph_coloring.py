import unittest
import networkx as nx
import torch
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.preprocessing.graph_coloring import ImprovedGraphColoring, ResourceType


class TestGraphColoring(unittest.TestCase):
    """图着色算法测试"""

    def setUp(self):
        """设置测试环境"""
        self.graph_coloring = ImprovedGraphColoring()

        # 创建测试DAG
        self.test_dag = nx.DiGraph()

        # 添加任务节点
        tasks_data = [
            {'id': 0, 'cpu_demand': 2.0, 'memory_demand': 1.0, 'io_demand': 0.5, 'network_demand': 0.3, 'runtime': 10},
            {'id': 1, 'cpu_demand': 1.0, 'memory_demand': 3.0, 'io_demand': 0.2, 'network_demand': 0.1, 'runtime': 8},
            {'id': 2, 'cpu_demand': 0.5, 'memory_demand': 0.8, 'io_demand': 2.5, 'network_demand': 0.4, 'runtime': 12},
            {'id': 3, 'cpu_demand': 0.8, 'memory_demand': 0.5, 'io_demand': 0.3, 'network_demand': 2.0, 'runtime': 6},
            {'id': 4, 'cpu_demand': 1.5, 'memory_demand': 1.5, 'io_demand': 1.5, 'network_demand': 1.5, 'runtime': 15},
        ]

        for task_data in tasks_data:
            self.test_dag.add_node(task_data['id'], **task_data)

        # 添加依赖边
        edges = [(0, 1), (0, 2), (1, 3), (2, 3), (3, 4)]
        self.test_dag.add_edges_from(edges)

    def test_resource_dominance_analysis(self):
        """测试资源主导性分析"""
        # 测试CPU密集型任务
        task_0_type = self.graph_coloring.analyze_task_resource_dominance(
            self.test_dag.nodes[0]
        )
        self.assertEqual(task_0_type, ResourceType.CPU_INTENSIVE)

        # 测试内存密集型任务
        task_1_type = self.graph_coloring.analyze_task_resource_dominance(
            self.test_dag.nodes[1]
        )
        self.assertEqual(task_1_type, ResourceType.MEMORY_INTENSIVE)

        # 测试I/O密集型任务
        task_2_type = self.graph_coloring.analyze_task_resource_dominance(
            self.test_dag.nodes[2]
        )
        self.assertEqual(task_2_type, ResourceType.IO_INTENSIVE)

        # 测试网络密集型任务
        task_3_type = self.graph_coloring.analyze_task_resource_dominance(
            self.test_dag.nodes[3]
        )
        self.assertEqual(task_3_type, ResourceType.NETWORK_INTENSIVE)

        # 测试混合型任务
        task_4_type = self.graph_coloring.analyze_task_resource_dominance(
            self.test_dag.nodes[4]
        )
        self.assertEqual(task_4_type, ResourceType.MIXED)

    def test_conflict_graph_construction(self):
        """测试冲突图构建"""
        conflict_graph = self.graph_coloring.construct_conflict_graph(self.test_dag)

        # 检查冲突图是否为无向图
        self.assertIsInstance(conflict_graph, nx.Graph)

        # 检查节点数量
        self.assertEqual(len(conflict_graph.nodes()), len(self.test_dag.nodes()))

        # 检查依赖关系是否被正确识别为冲突
        self.assertTrue(conflict_graph.has_edge(0, 1))  # 直接依赖
        self.assertTrue(conflict_graph.has_edge(0, 2))  # 直接依赖

    def test_improved_graph_coloring(self):
        """测试改进的图着色算法"""
        coloring_result = self.graph_coloring.improved_graph_coloring(self.test_dag)

        # 检查结果类型
        self.assertIsNotNone(coloring_result.task_colors)
        self.assertIsNotNone(coloring_result.task_types)
        self.assertIsNotNone(coloring_result.color_features)

        # 检查所有任务都被着色
        self.assertEqual(len(coloring_result.task_colors), len(self.test_dag.nodes()))

        # 检查颜色特征维度
        for task_id, features in coloring_result.color_features.items():
            self.assertEqual(len(features), 8)  # 8维特征向量
            self.assertIsInstance(features, np.ndarray)

        # 检查着色质量
        quality = coloring_result.coloring_quality
        self.assertIn('num_colors', quality)
        self.assertIn('conflict_rate', quality)
        self.assertIn('load_balance', quality)
        self.assertIn('type_consistency', quality)

        # 冲突率应该为0
        self.assertEqual(quality['conflict_rate'], 0.0)

    def test_parallelizable_groups_identification(self):
        """测试并行组识别"""
        coloring_result = self.graph_coloring.improved_graph_coloring(self.test_dag)

        # 检查并行组
        parallelizable_groups = coloring_result.parallelizable_groups

        # 验证同一组内的任务确实可以并行
        for group in parallelizable_groups:
            group_list = list(group)
            for i in range(len(group_list)):
                for j in range(i + 1, len(group_list)):
                    task1, task2 = group_list[i], group_list[j]
                    # 检查是否存在依赖关系
                    self.assertFalse(nx.has_path(self.test_dag, task1, task2))
                    self.assertFalse(nx.has_path(self.test_dag, task2, task1))

    def test_critical_path_analysis(self):
        """测试关键路径分析"""
        coloring_result = self.graph_coloring.improved_graph_coloring(self.test_dag)

        critical_path_colors = coloring_result.critical_path_colors

        # 检查关键路径颜色序列
        self.assertIsInstance(critical_path_colors, list)

        # 如果存在关键路径，检查其长度
        if critical_path_colors:
            self.assertGreater(len(critical_path_colors), 0)


class TestFeatureExtraction(unittest.TestCase):
    """特征提取测试"""

    def setUp(self):
        """设置测试环境"""
        from src.preprocessing.feature_extraction import WorkflowFeatureExtractor
        self.feature_extractor = WorkflowFeatureExtractor()

        # 创建测试工作流
        self.test_dag = nx.DiGraph()
        tasks_data = [
            {'id': 0, 'cpu_demand': 2.0, 'memory_demand': 1.0, 'io_demand': 0.5, 'network_demand': 0.3, 'runtime': 10},
            {'id': 1, 'cpu_demand': 1.0, 'memory_demand': 3.0, 'io_demand': 0.2, 'network_demand': 0.1, 'runtime': 8},
        ]

        for task_data in tasks_data:
            self.test_dag.add_node(task_data['id'], **task_data)

        self.test_dag.add_edge(0, 1)

    def test_task_feature_extraction(self):
        """测试任务特征提取"""
        task_features, metadata = self.feature_extractor.extract_task_features(
            self.test_dag, 'montage'
        )

        # 检查特征张量形状
        self.assertEqual(task_features.shape[0], len(self.test_dag.nodes()))
        self.assertEqual(task_features.shape[1], 128)  # 特征维度

        # 检查元数据
        self.assertIn('coloring_result', metadata)
        self.assertIn('workflow_type', metadata)
        self.assertEqual(metadata['workflow_type'], 'montage')

    def test_node_feature_extraction(self):
        """测试节点特征提取"""
        nodes_data = [
            {'cpu_capacity': 8.0, 'memory_capacity': 16.0, 'io_capacity': 1000, 'network_capacity': 5000},
            {'cpu_capacity': 4.0, 'memory_capacity': 32.0, 'io_capacity': 2000, 'network_capacity': 2500},
        ]

        node_features = self.feature_extractor.extract_node_features(nodes_data)

        # 检查特征张量形状
        self.assertEqual(node_features.shape[0], len(nodes_data))
        self.assertEqual(node_features.shape[1], 32)  # 节点特征维度


if __name__ == '__main__':
    unittest.main()