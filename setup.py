from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="gnn-workflow-scheduler",
    version="1.0.0",
    author="Research Team",
    author_email="<EMAIL>",
    description="基于图神经网络的工作流任务调度系统",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/gnn-workflow-scheduler",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
    python_requires=">=3.8",
    install_requires=[
        "torch>=2.0.0",
        "torch-geometric>=2.3.0",
        "numpy>=1.21.0",
        "scipy>=1.7.0",
        "networkx>=2.8.0",
        "matplotlib>=3.5.0",
        "seaborn>=0.11.0",
        "pandas>=1.3.0",
        "scikit-learn>=1.0.0",
        "pyyaml>=6.0",
        "tqdm>=4.62.0",
        "tensorboard>=2.8.0",
        "plotly>=5.0.0",
        "colorama>=0.4.4",
        "psutil>=5.8.0",
        "GPUtil>=1.4.0",
    ],
    extras_require={
        "dev": [
            "pytest>=6.0",
            "black>=21.0",
            "flake8>=3.9",
            "mypy>=0.910",
        ],
    },
    entry_points={
        "console_scripts": [
            "gnn-scheduler=main:main",
        ],
    },
)