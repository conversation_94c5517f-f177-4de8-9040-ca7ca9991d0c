import torch
import numpy as np
from torch.utils.data import Dataset, DataLoader
from typing import List, Dict, Any, Tuple
import networkx as nx
import random
from pathlib import Path
import json


class WorkflowDataset(Dataset):
    """工作流数据集类"""
    
    def __init__(self, processed_data: List[Dict], augment: bool = False):
        """
        初始化数据集
        
        Args:
            processed_data: 预处理后的工作流数据列表
            augment: 是否启用数据增强
        """
        self.data = processed_data
        self.augment = augment
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        """获取单个数据样本"""
        workflow = self.data[idx]
        
        # 基础数据
        task_features = torch.tensor(workflow['task_features'], dtype=torch.float32)
        node_features = torch.tensor(workflow['node_features'], dtype=torch.float32)
        adjacency_matrix = torch.tensor(workflow['adjacency_matrix'], dtype=torch.float32)
        
        # 数据增强
        if self.augment:
            task_features, node_features = self._apply_augmentation(task_features, node_features)
        
        # 构建边索引
        edge_index = self._adjacency_to_edge_index(adjacency_matrix)
        
        # 生成真实分配（这里使用随机分配作为示例，实际应该有真实标签）
        num_tasks, num_nodes = task_features.shape[0], node_features.shape[0]
        ground_truth = self._generate_ground_truth(num_tasks, num_nodes, adjacency_matrix)
        
        # 构建约束数据
        constraint_data = self._build_constraint_data(task_features, node_features, adjacency_matrix)
        
        return {
            'task_features': task_features,
            'node_features': node_features,
            'adjacency_matrix': adjacency_matrix,
            'edge_index': edge_index,
            'ground_truth_assignments': ground_truth,
            'constraint_data': constraint_data,
            'metadata': workflow.get('metadata', {}),
            'workflow_id': workflow.get('id', idx),
            'workflow_type': workflow.get('type', 'unknown')
        }
    
    def _apply_augmentation(self, task_features: torch.Tensor, 
                          node_features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """应用数据增强"""
        # 添加噪声
        if random.random() < 0.3:
            noise_scale = 0.05
            task_features += torch.randn_like(task_features) * noise_scale
            node_features += torch.randn_like(node_features) * noise_scale
        
        # 特征缩放
        if random.random() < 0.2:
            scale_factor = random.uniform(0.9, 1.1)
            task_features *= scale_factor
            node_features *= scale_factor
        
        return task_features, node_features
    
    def _adjacency_to_edge_index(self, adjacency_matrix: torch.Tensor) -> torch.Tensor:
        """将邻接矩阵转换为边索引"""
        edge_indices = torch.nonzero(adjacency_matrix, as_tuple=False)
        if edge_indices.numel() == 0:
            return torch.empty((2, 0), dtype=torch.long)
        return edge_indices.t().contiguous()
    
    def _generate_ground_truth(self, num_tasks: int, num_nodes: int, 
                             adjacency_matrix: torch.Tensor) -> torch.Tensor:
        """生成真实分配标签（示例实现）"""
        # 这里使用简单的负载均衡策略作为真实标签
        assignments = torch.zeros(num_tasks, dtype=torch.long)
        
        # 拓扑排序
        adj_np = adjacency_matrix.numpy()
        dag = nx.from_numpy_array(adj_np, create_using=nx.DiGraph)
        
        try:
            topo_order = list(nx.topological_sort(dag))
        except:
            topo_order = list(range(num_tasks))
        
        # 简单的轮询分配
        for i, task_id in enumerate(topo_order):
            assignments[task_id] = i % num_nodes
        
        return assignments
    
    def _build_constraint_data(self, task_features: torch.Tensor, 
                             node_features: torch.Tensor,
                             adjacency_matrix: torch.Tensor) -> Dict[str, torch.Tensor]:
        """构建约束数据"""
        num_tasks, num_nodes = task_features.shape[0], node_features.shape[0]
        
        # 任务需求（前4个特征作为资源需求）
        task_demands = task_features[:, :4]  # CPU, Memory, IO, Network
        
        # 节点容量（前4个特征作为资源容量）
        node_capacities = node_features[:, :4]  # CPU, Memory, IO, Network
        
        # 依赖约束特征
        dependency_features = torch.zeros(num_tasks, 256)
        for i in range(num_tasks):
            # 使用任务特征和图结构信息构建依赖特征
            dependency_features[i, :task_features.shape[1]] = task_features[i]
        
        # 资源约束特征
        resource_features = torch.zeros(num_nodes, 256)
        for i in range(num_nodes):
            resource_features[i, :node_features.shape[1]] = node_features[i]
        
        return {
            'dependency': dependency_features,
            'resource': resource_features,
            'adjacency_matrix': adjacency_matrix,
            'task_demands': task_demands,
            'node_capacities': node_capacities,
        }


def collate_workflow_batch(batch: List[Dict]) -> Dict[str, torch.Tensor]:
    """批处理整理函数"""
    batch_size = len(batch)
    
    # 获取最大尺寸
    max_tasks = max(item['task_features'].shape[0] for item in batch)
    max_nodes = max(item['node_features'].shape[0] for item in batch)
    
    # 初始化批处理张量
    task_features_batch = torch.zeros(batch_size, max_tasks, batch[0]['task_features'].shape[1])
    node_features_batch = torch.zeros(batch_size, max_nodes, batch[0]['node_features'].shape[1])
    adjacency_batch = torch.zeros(batch_size, max_tasks, max_tasks)
    ground_truth_batch = torch.zeros(batch_size, max_tasks, dtype=torch.long)
    
    # 批处理索引
    task_batch_indices = []
    node_batch_indices = []
    
    # 约束数据
    dependency_batch = torch.zeros(batch_size, max_tasks, 256)
    resource_batch = torch.zeros(batch_size, max_nodes, 256)
    task_demands_batch = torch.zeros(batch_size, max_tasks, 4)
    node_capacities_batch = torch.zeros(batch_size, max_nodes, 4)
    
    # 填充数据
    for i, item in enumerate(batch):
        num_tasks = item['task_features'].shape[0]
        num_nodes = item['node_features'].shape[0]
        
        # 任务特征
        task_features_batch[i, :num_tasks] = item['task_features']
        task_batch_indices.extend([i] * num_tasks)
        
        # 节点特征
        node_features_batch[i, :num_nodes] = item['node_features']
        node_batch_indices.extend([i] * num_nodes)
        
        # 邻接矩阵
        adjacency_batch[i, :num_tasks, :num_tasks] = item['adjacency_matrix']
        
        # 真实分配
        ground_truth_batch[i, :num_tasks] = item['ground_truth_assignments']
        
        # 约束数据
        constraint_data = item['constraint_data']
        dependency_batch[i, :num_tasks] = constraint_data['dependency']
        resource_batch[i, :num_nodes] = constraint_data['resource']
        task_demands_batch[i, :num_tasks] = constraint_data['task_demands']
        node_capacities_batch[i, :num_nodes] = constraint_data['node_capacities']
    
    # 构建边索引（合并所有图的边）
    edge_index_list = []
    edge_offset = 0
    for i, item in enumerate(batch):
        edge_index = item['edge_index']
        if edge_index.numel() > 0:
            edge_index_shifted = edge_index + edge_offset
            edge_index_list.append(edge_index_shifted)
        edge_offset += item['task_features'].shape[0]
    
    if edge_index_list:
        task_edge_index = torch.cat(edge_index_list, dim=1)
    else:
        task_edge_index = torch.empty((2, 0), dtype=torch.long)
    
    return {
        'task_features': task_features_batch,
        'node_features': node_features_batch,
        'adjacency_matrix': adjacency_batch,
        'task_edge_index': task_edge_index,
        'task_batch': torch.tensor(task_batch_indices, dtype=torch.long),
        'node_batch': torch.tensor(node_batch_indices, dtype=torch.long),
        'ground_truth_assignments': ground_truth_batch,
        'resource_constraints': torch.randn(batch_size, max_nodes, 4),  # 临时生成
        'constraint_data': {
            'dependency': dependency_batch,
            'resource': resource_batch,
            'adjacency_matrix': adjacency_batch,
            'task_demands': task_demands_batch,
            'node_capacities': node_capacities_batch,
        }
    }


def create_data_loaders(processed_data: List[Dict], 
                       train_ratio: float = 0.8,
                       val_ratio: float = 0.1,
                       batch_size: int = 32,
                       num_workers: int = 0,
                       augment_train: bool = True) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """创建训练、验证和测试数据加载器"""
    
    # 数据分割
    total_size = len(processed_data)
    train_size = int(total_size * train_ratio)
    val_size = int(total_size * val_ratio)
    test_size = total_size - train_size - val_size
    
    # 随机打乱数据
    indices = list(range(total_size))
    random.shuffle(indices)
    
    train_indices = indices[:train_size]
    val_indices = indices[train_size:train_size + val_size]
    test_indices = indices[train_size + val_size:]
    
    # 创建数据集
    train_data = [processed_data[i] for i in train_indices]
    val_data = [processed_data[i] for i in val_indices]
    test_data = [processed_data[i] for i in test_indices]
    
    train_dataset = WorkflowDataset(train_data, augment=augment_train)
    val_dataset = WorkflowDataset(val_data, augment=False)
    test_dataset = WorkflowDataset(test_data, augment=False)
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        collate_fn=collate_workflow_batch,
        pin_memory=torch.cuda.is_available()
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        collate_fn=collate_workflow_batch,
        pin_memory=torch.cuda.is_available()
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        collate_fn=collate_workflow_batch,
        pin_memory=torch.cuda.is_available()
    )
    
    return train_loader, val_loader, test_loader


def load_processed_data(data_path: str) -> List[Dict]:
    """加载预处理数据"""
    data_path = Path(data_path)
    
    if data_path.suffix == '.npy':
        return np.load(data_path, allow_pickle=True).tolist()
    elif data_path.suffix == '.json':
        with open(data_path, 'r') as f:
            return json.load(f)
    else:
        raise ValueError(f"不支持的文件格式: {data_path.suffix}")
