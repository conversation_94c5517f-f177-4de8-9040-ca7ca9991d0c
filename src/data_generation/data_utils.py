import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
import json
import os
from typing import Dict, List, Tuple, Any
import networkx as nx
from ..preprocessing.feature_extraction import WorkflowFeatureExtractor


class WorkflowDataset(Dataset):
    """工作流数据集类"""

    def __init__(self, data_path: str, transform=None, max_samples: int = None):
        self.data_path = data_path
        self.transform = transform
        self.max_samples = max_samples

        # 加载数据
        if data_path.endswith('.npy'):
            self.data = np.load(data_path, allow_pickle=True).tolist()
        elif data_path.endswith('.json'):
            with open(data_path, 'r') as f:
                self.data = json.load(f)
        else:
            raise ValueError(f"不支持的数据格式: {data_path}")

        if max_samples is not None:
            self.data = self.data[:max_samples]

        print(f"📚 加载了 {len(self.data)} 个工作流样本")

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        sample = self.data[idx]

        if self.transform:
            sample = self.transform(sample)

        return sample


class WorkflowCollator:
    """工作流数据批处理器"""

    def __init__(self, max_tasks: int = 100, max_nodes: int = 16):
        self.max_tasks = max_tasks
        self.max_nodes = max_nodes

    def __call__(self, batch: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """将一个批次的数据整理成模型输入格式"""

        batch_size = len(batch)

        # 初始化批次张量
        task_features = torch.zeros(batch_size, self.max_tasks, 128)
        node_features_list = []
        adjacency_matrices = torch.zeros(batch_size, self.max_tasks, self.max_tasks)

        # 图数据准备
        all_task_features = []
        all_node_features = []
        task_edge_index_list = []
        task_batch_list = []
        node_batch_list = []

        # 约束数据
        resource_constraints = torch.zeros(batch_size, self.max_nodes, 4)

        task_offset = 0
        node_offset = 0

        for b, sample in enumerate(batch):
            # 任务特征
            sample_task_features = torch.tensor(sample['task_features'], dtype=torch.float32)
            num_tasks = min(sample_task_features.shape[0], self.max_tasks)
            task_features[b, :num_tasks] = sample_task_features[:num_tasks]

            # 节点特征
            sample_node_features = torch.tensor(sample['node_features'], dtype=torch.float32)
            num_nodes = min(sample_node_features.shape[0], self.max_nodes)

            # 邻接矩阵
            sample_adj = torch.tensor(sample['adjacency_matrix'], dtype=torch.float32)
            adj_size = min(sample_adj.shape[0], self.max_tasks)
            adjacency_matrices[b, :adj_size, :adj_size] = sample_adj[:adj_size, :adj_size]

            # 图数据格式
            all_task_features.append(sample_task_features[:num_tasks])
            all_node_features.append(sample_node_features[:num_nodes])

            # 构建边索引
            edge_index = self._adjacency_to_edge_index(sample_adj[:adj_size, :adj_size])
            task_edge_index_list.append(edge_index + task_offset)

            # 批次索引
            task_batch_list.append(torch.full((num_tasks,), b, dtype=torch.long))
            node_batch_list.append(torch.full((num_nodes,), b, dtype=torch.long))

            # 资源约束（随机生成，实际应该从数据中获取）
            resource_constraints[b, :num_nodes] = torch.randn(num_nodes, 4).abs()

            task_offset += num_tasks
            node_offset += num_nodes

        # 合并图数据
        all_task_features_tensor = torch.cat(all_task_features, dim=0)
        all_node_features_tensor = torch.cat(all_node_features, dim=0)
        task_edge_index = torch.cat(task_edge_index_list, dim=1) if task_edge_index_list else torch.empty((2, 0),
                                                                                                          dtype=torch.long)
        task_batch = torch.cat(task_batch_list, dim=0)
        node_batch = torch.cat(node_batch_list, dim=0)

        # 约束数据
        constraint_data = {
            'dependency': torch.randn(batch_size, self.max_tasks, 128),
            'resource': torch.randn(batch_size, self.max_nodes, 128),
            'adjacency_matrix': adjacency_matrices,
            'task_demands': torch.randn(batch_size, self.max_tasks, 4),
            'node_capacities': resource_constraints,
        }

        # 生成模拟的真实标签（随机分配）
        ground_truth_assignments = torch.randint(0, self.max_nodes, (batch_size, self.max_tasks))

        return {
            'task_features': task_features,
            'node_features': all_node_features_tensor,
            'adjacency_matrix': adjacency_matrices,
            'task_edge_index': task_edge_index,
            'task_batch': task_batch,
            'node_batch': node_batch,
            'resource_constraints': resource_constraints,
            'constraint_data': constraint_data,
            'ground_truth_assignments': ground_truth_assignments,
            'batch_metadata': {
                'workflow_types': [sample.get('type', 'unknown') for sample in batch],
                'workflow_ids': [sample.get('id', i) for i, sample in enumerate(batch)]
            }
        }

    def _adjacency_to_edge_index(self, adjacency_matrix: torch.Tensor) -> torch.Tensor:
        """将邻接矩阵转换为边索引"""
        edge_indices = torch.nonzero(adjacency_matrix, as_tuple=False).T
        return edge_indices


def create_data_loaders(data_dir: str, batch_size: int = 32,
                        train_ratio: float = 0.8, val_ratio: float = 0.1) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """创建训练、验证和测试数据加载器"""

    # 加载数据集
    data_path = os.path.join(data_dir, 'processed_workflows.npy')
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"找不到预处理数据文件: {data_path}")

    dataset = WorkflowDataset(data_path)

    # 划分数据集
    total_size = len(dataset)
    train_size = int(train_ratio * total_size)
    val_size = int(val_ratio * total_size)
    test_size = total_size - train_size - val_size

    train_dataset, val_dataset, test_dataset = torch.utils.data.random_split(
        dataset, [train_size, val_size, test_size]
    )

    # 创建数据加载器
    collator = WorkflowCollator()

    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=collator,
        num_workers=0  # 设为0避免多进程问题
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=collator,
        num_workers=0
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=collator,
        num_workers=0
    )

    print(f"📊 数据集划分完成:")
    print(f"   训练集: {len(train_dataset)} 样本")
    print(f"   验证集: {len(val_dataset)} 样本")
    print(f"   测试集: {len(test_dataset)} 样本")

    return train_loader, val_loader, test_loader