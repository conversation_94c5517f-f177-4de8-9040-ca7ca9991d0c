import random
import networkx as nx
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import json
import os

from config.base_config import DataConfig


@dataclass
class Task:
    """任务定义"""
    id: int
    name: str
    runtime: float  # 执行时间
    input_size: float  # 输入数据大小 (MB)
    output_size: float  # 输出数据大小 (MB)
    cpu_demand: float  # CPU需求 (GFLOPS)
    memory_demand: float  # 内存需求 (GB)
    io_demand: float  # I/O需求 (MB/s)
    network_demand: float  # 网络需求 (Mbps)


@dataclass
class ComputeNode:
    """计算节点定义"""
    id: int
    name: str
    cpu_capacity: float  # CPU容量 (GFLOPS)
    memory_capacity: float  # 内存容量 (GB)
    io_capacity: float  # I/O容量 (MB/s)
    network_capacity: float  # 网络容量 (Mbps)
    energy_efficiency: float  # 能效比
    cost_per_hour: float  # 每小时成本


class WorkflowSimGenerator:
    """基于WorkflowSim的数据生成器"""

    def __init__(self, config: DataConfig):
        self.config = config
        self.workflow_templates = self._initialize_workflow_templates()

    def _initialize_workflow_templates(self) -> Dict:
        """初始化工作流模板"""
        return {
            'montage': {
                'stages': ['mProject', 'mDiffFit', 'mConcatFit', 'mBgModel', 'mBackground', 'mImgtbl', 'mAdd'],
                'stage_patterns': 'fan_out_fan_in',
                'data_intensity': 'high',
                'compute_intensity': 'medium'
            },
            'cybershake': {
                'stages': ['ExtractSGT', 'SeismoogramSynthesis', 'ZipSeis', 'PeakValCalc', 'ZipPSA'],
                'stage_patterns': 'pipeline_parallel',
                'data_intensity': 'medium',
                'compute_intensity': 'high'
            },
            'ligo': {
                'stages': ['Inspiral', 'TmpltBank', 'Inca', 'ThincaSelect', 'Sire'],
                'stage_patterns': 'complex_dag',
                'data_intensity': 'high',
                'compute_intensity': 'very_high'
            },
            'sipht': {
                'stages': ['Blast', 'CandidateSelect', 'Folding', 'RaSearch'],
                'stage_patterns': 'bag_of_tasks',
                'data_intensity': 'low',
                'compute_intensity': 'high'
            }
        }

    def generate_workflow_dag(self, workflow_type: str, num_tasks: int) -> nx.DiGraph:
        """生成工作流DAG"""
        template = self.workflow_templates[workflow_type]
        G = nx.DiGraph()

        # 根据不同工作流类型生成不同的DAG结构
        if template['stage_patterns'] == 'fan_out_fan_in':
            G = self._generate_fan_out_fan_in_dag(num_tasks)
        elif template['stage_patterns'] == 'pipeline_parallel':
            G = self._generate_pipeline_parallel_dag(num_tasks)
        elif template['stage_patterns'] == 'complex_dag':
            G = self._generate_complex_dag(num_tasks)
        elif template['stage_patterns'] == 'bag_of_tasks':
            G = self._generate_bag_of_tasks_dag(num_tasks)

        # 为每个任务添加特征
        for node_id in G.nodes():
            task = self._generate_task_features(node_id, workflow_type, template)
            G.nodes[node_id].update(task.__dict__)

        # 为边添加通信成本
        for edge in G.edges():
            comm_cost = self._generate_communication_cost(G.nodes[edge[0]], G.nodes[edge[1]])
            G.edges[edge]['communication_cost'] = comm_cost

        return G

    def _generate_fan_out_fan_in_dag(self, num_tasks: int) -> nx.DiGraph:
        """生成扇出-扇入DAG结构（如Montage）"""
        G = nx.DiGraph()

        # 入口任务
        G.add_node(0)

        # 中间并行任务
        middle_tasks = int(num_tasks * 0.7)
        for i in range(1, middle_tasks + 1):
            G.add_node(i)
            G.add_edge(0, i)  # 从入口扇出

        # 汇聚任务
        converge_tasks = num_tasks - middle_tasks - 2
        for i in range(middle_tasks + 1, middle_tasks + 1 + converge_tasks):
            G.add_node(i)
            # 随机选择一些中间任务作为前驱
            predecessors = random.sample(range(1, middle_tasks + 1),
                                         min(3, middle_tasks))
            for pred in predecessors:
                G.add_edge(pred, i)

        # 出口任务
        exit_task = num_tasks - 1
        G.add_node(exit_task)
        for i in range(middle_tasks + 1, exit_task):
            G.add_edge(i, exit_task)

        return G

    def _generate_pipeline_parallel_dag(self, num_tasks: int) -> nx.DiGraph:
        """生成流水线并行DAG结构（如CyberShake）"""
        G = nx.DiGraph()

        num_stages = 5
        tasks_per_stage = num_tasks // num_stages

        for stage in range(num_stages):
            stage_start = stage * tasks_per_stage
            stage_end = min((stage + 1) * tasks_per_stage, num_tasks)

            # 当前阶段的任务
            for i in range(stage_start, stage_end):
                G.add_node(i)

                # 与上一阶段建立依赖关系
                if stage > 0:
                    prev_stage_start = (stage - 1) * tasks_per_stage
                    prev_stage_end = stage_start

                    # 随机选择上一阶段的一些任务作为前驱
                    num_deps = random.randint(1, min(3, prev_stage_end - prev_stage_start))
                    predecessors = random.sample(range(prev_stage_start, prev_stage_end), num_deps)

                    for pred in predecessors:
                        G.add_edge(pred, i)

        return G

    def _generate_complex_dag(self, num_tasks: int) -> nx.DiGraph:
        """生成复杂DAG结构（如LIGO）"""
        G = nx.DiGraph()

        # 使用随机DAG生成算法
        for i in range(num_tasks):
            G.add_node(i)

        # 确保DAG结构的复杂性
        for i in range(num_tasks):
            # 每个任务可能依赖于前面的多个任务
            max_predecessors = min(i, 4)
            if max_predecessors > 0:
                num_predecessors = random.randint(0, max_predecessors)
                predecessors = random.sample(range(i), num_predecessors)
                for pred in predecessors:
                    G.add_edge(pred, i)

        # 确保连通性
        if not nx.is_weakly_connected(G):
            components = list(nx.weakly_connected_components(G))
            for i in range(len(components) - 1):
                # 连接不同的连通分量
                source = random.choice(list(components[i]))
                target = random.choice(list(components[i + 1]))
                if source < target:  # 保持DAG性质
                    G.add_edge(source, target)

        return G

    def _generate_bag_of_tasks_dag(self, num_tasks: int) -> nx.DiGraph:
        """生成任务袋DAG结构（如SIPHT）"""
        G = nx.DiGraph()

        # 大部分任务是独立的
        independent_ratio = 0.8
        num_independent = int(num_tasks * independent_ratio)

        # 独立任务
        for i in range(num_independent):
            G.add_node(i)

        # 少量有依赖关系的任务
        for i in range(num_independent, num_tasks):
            G.add_node(i)
            # 随机选择前面的任务作为依赖
            if i > 0:
                num_deps = random.randint(1, min(2, i))
                predecessors = random.sample(range(i), num_deps)
                for pred in predecessors:
                    G.add_edge(pred, i)

        return G

    def _generate_task_features(self, task_id: int, workflow_type: str, template: Dict) -> Task:
        """生成任务特征"""
        # 根据工作流类型调整特征分布
        intensity_factors = {
            'low': (0.5, 1.5),
            'medium': (1.0, 3.0),
            'high': (2.0, 5.0),
            'very_high': (4.0, 8.0)
        }

        data_factor = intensity_factors[template['data_intensity']]
        compute_factor = intensity_factors[template['compute_intensity']]

        # 基础参数
        base_runtime = random.uniform(10, 100)  # 秒
        base_data_size = random.uniform(1, 50)  # MB

        # 根据强度因子调整
        runtime = base_runtime * random.uniform(*compute_factor)
        input_size = base_data_size * random.uniform(*data_factor)
        output_size = input_size * random.uniform(0.5, 2.0)

        # 资源需求
        cpu_demand = runtime * random.uniform(0.1, 2.0)  # GFLOPS
        memory_demand = max(input_size / 1000, random.uniform(0.5, 4.0))  # GB
        io_demand = (input_size + output_size) / runtime  # MB/s
        network_demand = output_size * 8 / runtime  # Mbps (假设8:1的压缩比)

        return Task(
            id=task_id,
            name=f"{workflow_type}_task_{task_id}",
            runtime=runtime,
            input_size=input_size,
            output_size=output_size,
            cpu_demand=cpu_demand,
            memory_demand=memory_demand,
            io_demand=io_demand,
            network_demand=network_demand
        )

    def _generate_communication_cost(self, source_task: Dict, target_task: Dict) -> float:
        """生成通信成本"""
        data_transfer = source_task['output_size']
        network_speed = min(source_task['network_demand'], target_task['network_demand'])

        # 通信时间 = 数据大小 / 网络速度
        if network_speed > 0:
            comm_time = (data_transfer * 8) / network_speed  # 转换为秒
        else:
            comm_time = data_transfer / 100  # 默认网络速度

        return comm_time

    def generate_heterogeneous_nodes(self, num_nodes: int) -> List[ComputeNode]:
        """生成异构计算节点"""
        nodes = []

        # 定义不同类型的节点模板
        node_templates = {
            'cpu_intensive': {
                'cpu_range': (8.0, 16.0),
                'memory_range': (16.0, 64.0),
                'io_range': (500, 1000),
                'network_range': (1000, 10000),
                'cost_range': (0.1, 0.3)
            },
            'memory_intensive': {
                'cpu_range': (4.0, 8.0),
                'memory_range': (64.0, 256.0),
                'io_range': (1000, 2000),
                'network_range': (1000, 5000),
                'cost_range': (0.15, 0.4)
            },
            'io_intensive': {
                'cpu_range': (2.0, 6.0),
                'memory_range': (8.0, 32.0),
                'io_range': (2000, 5000),
                'network_range': (10000, 25000),
                'cost_range': (0.08, 0.25)
            },
            'balanced': {
                'cpu_range': (4.0, 12.0),
                'memory_range': (16.0, 128.0),
                'io_range': (800, 1500),
                'network_range': (5000, 15000),
                'cost_range': (0.12, 0.35)
            }
        }

        # 随机分配节点类型
        node_types = list(node_templates.keys())

        for i in range(num_nodes):
            node_type = random.choice(node_types)
            template = node_templates[node_type]

            # 添加异构性因子
            heterogeneity = self.config.heterogeneity_factor

            node = ComputeNode(
                id=i,
                name=f"{node_type}_node_{i}",
                cpu_capacity=random.uniform(*template['cpu_range']) * (
                            1 + random.uniform(-heterogeneity, heterogeneity)),
                memory_capacity=random.uniform(*template['memory_range']) * (
                            1 + random.uniform(-heterogeneity, heterogeneity)),
                io_capacity=random.uniform(*template['io_range']) * (1 + random.uniform(-heterogeneity, heterogeneity)),
                network_capacity=random.uniform(*template['network_range']) * (
                            1 + random.uniform(-heterogeneity, heterogeneity)),
                energy_efficiency=random.uniform(0.5, 2.0),
                cost_per_hour=random.uniform(*template['cost_range'])
            )

            nodes.append(node)

        return nodes

    def generate_dataset(self, output_dir: str) -> None:
        """生成完整数据集"""
        os.makedirs(output_dir, exist_ok=True)

        workflows = []
        nodes_list = []

        print(f"生成 {self.config.num_workflows} 个工作流...")

        for i in range(self.config.num_workflows):
            # 随机选择工作流类型
            workflow_type = random.choice(self.config.workflow_types)
            num_tasks = random.randint(self.config.min_tasks, self.config.max_tasks)
            num_nodes = random.randint(self.config.min_nodes, self.config.max_nodes)

            # 生成工作流DAG
            workflow_dag = self.generate_workflow_dag(workflow_type, num_tasks)

            # 生成计算节点
            compute_nodes = self.generate_heterogeneous_nodes(num_nodes)

            # 保存工作流数据
            workflow_data = {
                'id': i,
                'type': workflow_type,
                'num_tasks': num_tasks,
                'num_nodes': num_nodes,
                'dag': nx.node_link_data(workflow_dag),
                'nodes': [node.__dict__ for node in compute_nodes]
            }

            workflows.append(workflow_data)

            if (i + 1) % 100 == 0:
                print(f"已生成 {i + 1} 个工作流")

        # 保存数据集
        with open(os.path.join(output_dir, 'workflows.json'), 'w') as f:
            json.dump(workflows, f, indent=2)

        print(f"数据集已保存到 {output_dir}")

        # 生成统计信息
        self._generate_dataset_statistics(workflows, output_dir)

    def _generate_dataset_statistics(self, workflows: List[Dict], output_dir: str) -> None:
        """生成数据集统计信息"""
        stats = {
            'total_workflows': len(workflows),
            'workflow_types': {},
            'task_statistics': {
                'min_tasks': float('inf'),
                'max_tasks': 0,
                'avg_tasks': 0
            },
            'node_statistics': {
                'min_nodes': float('inf'),
                'max_nodes': 0,
                'avg_nodes': 0
            }
        }

        total_tasks = 0
        total_nodes = 0

        for workflow in workflows:
            # 工作流类型统计
            wf_type = workflow['type']
            if wf_type not in stats['workflow_types']:
                stats['workflow_types'][wf_type] = 0
            stats['workflow_types'][wf_type] += 1

            # 任务统计
            num_tasks = workflow['num_tasks']
            stats['task_statistics']['min_tasks'] = min(stats['task_statistics']['min_tasks'], num_tasks)
            stats['task_statistics']['max_tasks'] = max(stats['task_statistics']['max_tasks'], num_tasks)
            total_tasks += num_tasks

            # 节点统计
            num_nodes = workflow['num_nodes']
            stats['node_statistics']['min_nodes'] = min(stats['node_statistics']['min_nodes'], num_nodes)
            stats['node_statistics']['max_nodes'] = max(stats['node_statistics']['max_nodes'], num_nodes)
            total_nodes += num_nodes

        stats['task_statistics']['avg_tasks'] = total_tasks / len(workflows)
        stats['node_statistics']['avg_nodes'] = total_nodes / len(workflows)

        # 保存统计信息
        with open(os.path.join(output_dir, 'dataset_statistics.json'), 'w') as f:
            json.dump(stats, f, indent=2)

        print("数据集统计信息:")
        print(f"  总工作流数: {stats['total_workflows']}")
        print(f"  工作流类型分布: {stats['workflow_types']}")
        print(f"  任务数范围: {stats['task_statistics']['min_tasks']}-{stats['task_statistics']['max_tasks']}")
        print(f"  平均任务数: {stats['task_statistics']['avg_tasks']:.2f}")
        print(f"  节点数范围: {stats['node_statistics']['min_nodes']}-{stats['node_statistics']['max_nodes']}")
        print(f"  平均节点数: {stats['node_statistics']['avg_nodes']:.2f}")
