import torch
import torch.nn as nn
import numpy as np
import networkx as nx
from typing import Dict, List, Tuple, Any
import random


class HEFTScheduler:
    """异构最早完成时间算法 (HEFT)"""

    def __init__(self):
        self.name = "HEFT"

    def schedule(self, workflow_dag: nx.DiGraph, nodes: List[Dict]) -> Dict[int, int]:
        """
        HEFT调度算法

        Returns:
            task_assignments: {task_id: node_id}
        """
        # 计算任务优先级（向上排序）
        task_priorities = self._compute_upward_rank(workflow_dag, nodes)

        # 按优先级排序任务
        sorted_tasks = sorted(task_priorities.items(), key=lambda x: x[1], reverse=True)

        # 节点可用时间
        node_available_time = [0.0] * len(nodes)
        task_assignments = {}
        task_finish_times = {}

        for task_id, priority in sorted_tasks:
            best_node = None
            best_finish_time = float('inf')

            for node_id in range(len(nodes)):
                # 计算最早开始时间
                earliest_start_time = node_available_time[node_id]

                # 考虑依赖任务的完成时间
                for pred in workflow_dag.predecessors(task_id):
                    if pred in task_assignments:
                        pred_node = task_assignments[pred]
                        pred_finish_time = task_finish_times[pred]

                        if pred_node != node_id:
                            # 添加通信时间
                            comm_time = self._get_communication_time(
                                workflow_dag, pred, task_id, pred_node, node_id
                            )
                            earliest_start_time = max(earliest_start_time, pred_finish_time + comm_time)
                        else:
                            earliest_start_time = max(earliest_start_time, pred_finish_time)

                # 计算执行时间
                execution_time = self._get_execution_time(workflow_dag, task_id, nodes[node_id])
                finish_time = earliest_start_time + execution_time

                if finish_time < best_finish_time:
                    best_finish_time = finish_time
                    best_node = node_id

            # 分配任务
            task_assignments[task_id] = best_node
            task_finish_times[task_id] = best_finish_time
            node_available_time[best_node] = best_finish_time

        return task_assignments

    def _compute_upward_rank(self, dag: nx.DiGraph, nodes: List[Dict]) -> Dict[int, float]:
        """计算向上排序值"""
        upward_rank = {}

        # 拓扑排序（逆序）
        topo_order = list(nx.topological_sort(dag))

        for task_id in reversed(topo_order):
            # 计算平均执行时间
            avg_execution_time = np.mean([
                self._get_execution_time(dag, task_id, node) for node in nodes
            ])

            # 计算到出口节点的最长路径
            max_successor_rank = 0.0
            for succ in dag.successors(task_id):
                if succ in upward_rank:
                    avg_comm_time = np.mean([
                        self._get_communication_time(dag, task_id, succ, i, j)
                        for i in range(len(nodes)) for j in range(len(nodes)) if i != j
                    ])
                    max_successor_rank = max(max_successor_rank,
                                             avg_comm_time + upward_rank[succ])

            upward_rank[task_id] = avg_execution_time + max_successor_rank

        return upward_rank

    def _get_execution_time(self, dag: nx.DiGraph, task_id: int, node: Dict) -> float:
        """获取任务在节点上的执行时间"""
        task_data = dag.nodes[task_id]
        runtime = task_data.get('runtime', 1.0)
        cpu_capacity = node.get('cpu_capacity', 1.0)

        # 简化：执行时间与CPU容量成反比
        return runtime / max(cpu_capacity, 0.1)

    def _get_communication_time(self, dag: nx.DiGraph, task1: int, task2: int,
                                node1_id: int, node2_id: int) -> float:
        """获取通信时间"""
        if node1_id == node2_id:
            return 0.0

        # 简化：固定通信时间
        edge_data = dag.get_edge_data(task1, task2)
        if edge_data:
            return edge_data.get('communication_cost', 1.0)
        return 0.0


class CPOPScheduler:
    """关键路径优先算法 (CPOP)"""

    def __init__(self):
        self.name = "CPOP"

    def schedule(self, workflow_dag: nx.DiGraph, nodes: List[Dict]) -> Dict[int, int]:
        """CPOP调度算法"""
        # 计算关键路径
        critical_path = self._find_critical_path(workflow_dag, nodes)

        # 为关键路径选择最佳节点
        critical_node = self._select_critical_node(workflow_dag, nodes, critical_path)

        task_assignments = {}

        # 首先调度关键路径上的任务
        for task_id in critical_path:
            task_assignments[task_id] = critical_node

        # 调度其余任务
        remaining_tasks = [t for t in workflow_dag.nodes() if t not in critical_path]

        # 使用HEFT策略调度剩余任务
        heft = HEFTScheduler()
        remaining_dag = workflow_dag.subgraph(remaining_tasks + critical_path)
        remaining_assignments = heft.schedule(remaining_dag, nodes)

        # 合并分配结果（不覆盖关键路径分配）
        for task_id, node_id in remaining_assignments.items():
            if task_id not in task_assignments:
                task_assignments[task_id] = node_id

        return task_assignments

    def _find_critical_path(self, dag: nx.DiGraph, nodes: List[Dict]) -> List[int]:
        """找到关键路径"""
        try:
            # 计算最长路径
            path = nx.dag_longest_path(dag, weight=lambda u, v, d:
            np.mean([self._get_execution_time(dag, u, node) for node in nodes]))
            return path
        except:
            # 如果失败，返回拓扑排序的一个子集
            topo_order = list(nx.topological_sort(dag))
            return topo_order[:min(len(topo_order), 5)]

    def _select_critical_node(self, dag: nx.DiGraph, nodes: List[Dict],
                              critical_path: List[int]) -> int:
        """为关键路径选择最佳节点"""
        best_node = 0
        best_execution_time = float('inf')

        for node_id, node in enumerate(nodes):
            total_execution_time = sum([
                self._get_execution_time(dag, task_id, node)
                for task_id in critical_path
            ])

            if total_execution_time < best_execution_time:
                best_execution_time = total_execution_time
                best_node = node_id

        return best_node

    def _get_execution_time(self, dag: nx.DiGraph, task_id: int, node: Dict) -> float:
        """获取执行时间（与HEFT相同）"""
        task_data = dag.nodes[task_id]
        runtime = task_data.get('runtime', 1.0)
        cpu_capacity = node.get('cpu_capacity', 1.0)
        return runtime / max(cpu_capacity, 0.1)


class RandomScheduler:
    """随机调度算法（基线）"""

    def __init__(self, seed: int = 42):
        self.name = "Random"
        self.seed = seed

    def schedule(self, workflow_dag: nx.DiGraph, nodes: List[Dict]) -> Dict[int, int]:
        """随机分配任务到节点"""
        random.seed(self.seed)

        task_assignments = {}
        num_nodes = len(nodes)

        for task_id in workflow_dag.nodes():
            assigned_node = random.randint(0, num_nodes - 1)
            task_assignments[task_id] = assigned_node

        return task_assignments


class RoundRobinScheduler:
    """轮询调度算法"""

    def __init__(self):
        self.name = "RoundRobin"

    def schedule(self, workflow_dag: nx.DiGraph, nodes: List[Dict]) -> Dict[int, int]:
        """轮询分配任务到节点"""
        task_assignments = {}
        num_nodes = len(nodes)

        for i, task_id in enumerate(workflow_dag.nodes()):
            assigned_node = i % num_nodes
            task_assignments[task_id] = assigned_node

        return task_assignments


class GAScheduler:
    """Genetic Algorithm Scheduler (placeholder)"""
    def __init__(self, seed: int = 42):
        self.name = "GA-WS"
        self.seed = seed
    def schedule(self, workflow_dag: nx.DiGraph, nodes: List[Dict]) -> Dict[int, int]:
        # 伪实现：随机分配，后续可替换为GA主循环
        random.seed(self.seed)
        return {task_id: random.randint(0, len(nodes)-1) for task_id in workflow_dag.nodes()}

class PSOScheduler:
    """Particle Swarm Optimization Scheduler (placeholder)"""
    def __init__(self, seed: int = 42):
        self.name = "PSO-WS"
        self.seed = seed
    def schedule(self, workflow_dag: nx.DiGraph, nodes: List[Dict]) -> Dict[int, int]:
        # 伪实现：随机分配，后续可替换为PSO主循环
        random.seed(self.seed+1)
        return {task_id: random.randint(0, len(nodes)-1) for task_id in workflow_dag.nodes()}

class DQNScheduler:
    """Deep Q-Network Scheduler (placeholder)"""
    def __init__(self, seed: int = 42):
        self.name = "DQN-Scheduler"
        self.seed = seed
    def schedule(self, workflow_dag: nx.DiGraph, nodes: List[Dict]) -> Dict[int, int]:
        # 伪实现：随机分配，后续可替换为DQN推理
        random.seed(self.seed+2)
        return {task_id: random.randint(0, len(nodes)-1) for task_id in workflow_dag.nodes()}

class CGWSAScheduler:
    """Chaotic Grey Wolf Scheduler Algorithm (placeholder)"""
    def __init__(self, seed: int = 42):
        self.name = "CGWSA"
        self.seed = seed
    def schedule(self, workflow_dag: nx.DiGraph, nodes: List[Dict]) -> Dict[int, int]:
        # 伪实现：随机分配，后续可替换为CGWSA主循环
        random.seed(self.seed+3)
        return {task_id: random.randint(0, len(nodes)-1) for task_id in workflow_dag.nodes()}