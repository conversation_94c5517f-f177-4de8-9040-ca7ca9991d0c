import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional
import numpy as np


class ConstraintEmbedding(nn.Module):
    """约束条件嵌入层"""

    def __init__(self, constraint_dim: int, hidden_dim: int):
        super().__init__()
        self.constraint_dim = constraint_dim
        self.hidden_dim = hidden_dim

        # 不同类型约束的编码器
        self.dependency_encoder = nn.Sequential(
            nn.Linear(constraint_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )

        self.resource_encoder = nn.Sequential(
            nn.Linear(constraint_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )

        self.temporal_encoder = nn.Sequential(
            nn.Linear(constraint_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )

        self.communication_encoder = nn.Sequential(
            nn.Linear(constraint_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )

        # 约束融合层
        self.constraint_fusion = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=8,
            dropout=0.1
        )

    def forward(self, task_features: torch.Tensor,
                constraint_data: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Args:
            task_features: [batch_size, num_tasks, feature_dim]
            constraint_data: 包含各种约束信息的字典
        """
        batch_size, num_tasks, _ = task_features.size()
        constraint_embeddings = []

        # 编码不同类型的约束
        if 'dependency' in constraint_data:
            dep_emb = self.dependency_encoder(constraint_data['dependency'])
            # 确保依赖约束的形状与任务特征一致
            if dep_emb.size(1) != num_tasks:
                # 如果维度不匹配，进行插值或填充
                dep_emb = F.interpolate(dep_emb.transpose(1, 2), size=num_tasks, mode='linear').transpose(1, 2)
            constraint_embeddings.append(dep_emb)

        if 'resource' in constraint_data:
            res_emb = self.resource_encoder(constraint_data['resource'])
            # 资源约束通常是针对节点的，需要转换为任务级别的约束
            # 这里我们简单地复制到任务级别
            if res_emb.size(1) != num_tasks:
                # 如果维度不匹配，进行插值或填充
                res_emb = F.interpolate(res_emb.transpose(1, 2), size=num_tasks, mode='linear').transpose(1, 2)
            constraint_embeddings.append(res_emb)

        if 'temporal' in constraint_data:
            temp_emb = self.temporal_encoder(constraint_data['temporal'])
            if temp_emb.size(1) != num_tasks:
                temp_emb = F.interpolate(temp_emb.transpose(1, 2), size=num_tasks, mode='linear').transpose(1, 2)
            constraint_embeddings.append(temp_emb)

        if 'communication' in constraint_data:
            comm_emb = self.communication_encoder(constraint_data['communication'])
            if comm_emb.size(1) != num_tasks:
                comm_emb = F.interpolate(comm_emb.transpose(1, 2), size=num_tasks, mode='linear').transpose(1, 2)
            constraint_embeddings.append(comm_emb)

        if not constraint_embeddings:
            return task_features

        # 堆叠约束嵌入并平均池化到任务级别
        stacked_constraints = torch.stack(constraint_embeddings, dim=1)  # [batch_size, num_constraints, num_tasks, hidden_dim]
        # 平均池化约束维度
        avg_constraints = torch.mean(stacked_constraints, dim=1)  # [batch_size, num_tasks, hidden_dim]

        # 使用注意力机制融合约束
        task_features = task_features.transpose(0, 1)  # [num_tasks, batch_size, feature_dim]
        avg_constraints = avg_constraints.transpose(0, 1)  # [num_tasks, batch_size, hidden_dim]

        constraint_aware_features, _ = self.constraint_fusion(
            task_features, avg_constraints, avg_constraints
        )

        return constraint_aware_features.transpose(0, 1)  # [batch_size, num_tasks, hidden_dim]


class PhysicsInformedLoss(nn.Module):
    """物理约束感知损失函数"""

    def __init__(self, constraint_weights: Dict[str, float]):
        super().__init__()
        self.constraint_weights = constraint_weights

    def dependency_constraint_loss(self, assignment_probs: torch.Tensor,
                                   adjacency_matrix: torch.Tensor,
                                   node_capacities: torch.Tensor) -> torch.Tensor:
        """依赖关系约束损失"""
        batch_size, num_tasks, num_nodes = assignment_probs.size()

        # 计算每个任务的预期完成时间
        expected_completion_times = self._compute_expected_completion_times(
            assignment_probs, node_capacities
        )

        # 检查依赖关系是否被违反
        violation_loss = 0.0

        for b in range(batch_size):
            adj = adjacency_matrix[b]  # [num_tasks, num_tasks]
            completion_times = expected_completion_times[b]  # [num_tasks]

            # 对于每条依赖边，确保前驱任务早于后继任务完成
            for i in range(num_tasks):
                for j in range(num_tasks):
                    if adj[i, j] > 0:  # 存在依赖关系 i -> j
                        # 前驱任务i应该早于后继任务j完成
                        time_violation = F.relu(completion_times[i] - completion_times[j] + 1e-3)
                        violation_loss += time_violation

        return violation_loss / batch_size

    def resource_constraint_loss(self, assignment_probs: torch.Tensor,
                                 task_demands: torch.Tensor,
                                 node_capacities: torch.Tensor) -> torch.Tensor:
        """资源容量约束损失"""
        batch_size, num_tasks, num_nodes = assignment_probs.size()

        # 计算每个节点的预期资源使用量
        expected_resource_usage = torch.zeros(batch_size, num_nodes, task_demands.size(-1),
                                              device=assignment_probs.device)

        for n in range(num_nodes):
            # 节点n的任务分配概率：[batch_size, num_tasks]
            node_assignment_probs = assignment_probs[:, :, n]

            # 预期资源使用量：[batch_size, resource_dim]
            expected_resource_usage[:, n, :] = torch.sum(
                node_assignment_probs.unsqueeze(-1) * task_demands, dim=1
            )

        # 计算资源容量违反程度
        # node_capacities的形状应该是[batch_size, num_nodes, resource_dim]
        capacity_violations = F.relu(expected_resource_usage - node_capacities)

        # 归一化违反损失
        violation_loss = torch.sum(capacity_violations) / (batch_size * num_nodes)

        return violation_loss

    def temporal_constraint_loss(self, assignment_probs: torch.Tensor,
                                 task_deadlines: torch.Tensor,
                                 task_execution_times: torch.Tensor,
                                 node_speeds: torch.Tensor) -> torch.Tensor:
        """时间约束损失（截止时间）"""
        batch_size, num_tasks, num_nodes = assignment_probs.size()

        # 计算每个任务在每个节点上的预期执行时间
        # [batch_size, num_tasks, num_nodes]
        expected_exec_times = task_execution_times.unsqueeze(-1) / node_speeds.unsqueeze(0).unsqueeze(0)

        # 计算加权预期执行时间
        weighted_exec_times = assignment_probs * expected_exec_times
        actual_exec_times = torch.sum(weighted_exec_times, dim=-1)  # [batch_size, num_tasks]

        # 计算截止时间违反
        deadline_violations = F.relu(actual_exec_times - task_deadlines)

        return torch.mean(deadline_violations)

    def communication_constraint_loss(self, assignment_probs: torch.Tensor,
                                      adjacency_matrix: torch.Tensor,
                                      communication_costs: torch.Tensor,
                                      network_bandwidth: torch.Tensor) -> torch.Tensor:
        """通信约束损失"""
        batch_size, num_tasks, num_nodes = assignment_probs.size()

        total_comm_loss = 0.0

        for b in range(batch_size):
            adj = adjacency_matrix[b]  # [num_tasks, num_tasks]
            comm_costs = communication_costs[b]  # [num_tasks, num_tasks]

            for i in range(num_tasks):
                for j in range(num_tasks):
                    if adj[i, j] > 0:  # 存在数据依赖
                        # 计算任务i和j分配到不同节点的概率和通信成本
                        for ni in range(num_nodes):
                            for nj in range(num_nodes):
                                if ni != nj:  # 不同节点间的通信
                                    prob_diff_nodes = assignment_probs[b, i, ni] * assignment_probs[b, j, nj]
                                    comm_time = comm_costs[i, j] / network_bandwidth[ni, nj]
                                    total_comm_loss += prob_diff_nodes * comm_time

        return total_comm_loss / batch_size

    def _compute_expected_completion_times(self, assignment_probs: torch.Tensor,
                                           node_speeds: torch.Tensor) -> torch.Tensor:
        """计算预期完成时间（简化版本）"""
        batch_size, num_tasks, num_nodes = assignment_probs.size()

        # 假设所有任务的基础执行时间为1（可以从task_features中获取）
        base_exec_time = 1.0

        # 计算每个任务的预期执行时间
        expected_times = torch.zeros(batch_size, num_tasks, device=assignment_probs.device)

        for t in range(num_tasks):
            for n in range(num_nodes):
                prob = assignment_probs[:, t, n]  # [batch_size]
                
                # 处理node_speeds的多维形状
                if node_speeds.dim() > 1:
                    # 如果node_speeds是[batch_size, num_nodes, resource_dim]，取第一个资源维度作为速度
                    node_speed = node_speeds[:, n, 0] if node_speeds.size(0) == batch_size else node_speeds[n, 0]
                else:
                    # 如果node_speeds是[num_nodes]，直接索引
                    node_speed = node_speeds[n]
                
                # 使用torch.where来处理张量的条件判断
                exec_time = torch.where(node_speed > 0, base_exec_time / node_speed, torch.tensor(base_exec_time, device=assignment_probs.device))
                expected_times[:, t] += prob * exec_time

        return expected_times

    def forward(self, assignment_probs: torch.Tensor,
                constraint_data: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        计算所有约束损失

        Args:
            assignment_probs: [batch_size, num_tasks, num_nodes] 任务分配概率
            constraint_data: 包含各种约束信息的字典
        """
        losses = {}
        total_loss = 0.0

        # 依赖关系约束
        if 'adjacency_matrix' in constraint_data and 'node_capacities' in constraint_data:
            dep_loss = self.dependency_constraint_loss(
                assignment_probs,
                constraint_data['adjacency_matrix'],
                constraint_data['node_capacities']
            )
            losses['dependency'] = dep_loss
            total_loss += self.constraint_weights.get('dependency', 1.0) * dep_loss

        # 资源约束
        if 'task_demands' in constraint_data and 'node_capacities' in constraint_data:
            res_loss = self.resource_constraint_loss(
                assignment_probs,
                constraint_data['task_demands'],
                constraint_data['node_capacities']
            )
            losses['resource'] = res_loss
            total_loss += self.constraint_weights.get('resource', 1.0) * res_loss

        # 时间约束
        if all(k in constraint_data for k in ['task_deadlines', 'task_execution_times', 'node_speeds']):
            temp_loss = self.temporal_constraint_loss(
                assignment_probs,
                constraint_data['task_deadlines'],
                constraint_data['task_execution_times'],
                constraint_data['node_speeds']
            )
            losses['temporal'] = temp_loss
            total_loss += self.constraint_weights.get('temporal', 1.0) * temp_loss

        # 通信约束
        if all(k in constraint_data for k in ['adjacency_matrix', 'communication_costs', 'network_bandwidth']):
            comm_loss = self.communication_constraint_loss(
                assignment_probs,
                constraint_data['adjacency_matrix'],
                constraint_data['communication_costs'],
                constraint_data['network_bandwidth']
            )
            losses['communication'] = comm_loss
            total_loss += self.constraint_weights.get('communication', 1.0) * comm_loss

        losses['total'] = total_loss
        return losses


class PINNConstraintLayer(nn.Module):
    """PINN约束增强层"""

    def __init__(self, input_dim: int, hidden_dim: int, constraint_weights: Dict[str, float]):
        super().__init__()

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim

        # 约束嵌入
        self.constraint_embedding = ConstraintEmbedding(
            constraint_dim=input_dim,
            hidden_dim=hidden_dim
        )

        # 物理约束损失
        self.physics_loss = PhysicsInformedLoss(constraint_weights)

        # 约束感知的特征变换
        self.constraint_aware_transform = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim)
        )

        # 约束验证网络
        self.constraint_validator = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )

    def forward(self, task_features: torch.Tensor,
                constraint_data: Dict[str, torch.Tensor],
                assignment_probs: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Args:
            task_features: [batch_size, num_tasks, input_dim]
            constraint_data: 约束数据字典
            assignment_probs: [batch_size, num_tasks, num_nodes] 当前分配概率（用于损失计算）

        Returns:
            约束增强的特征和约束损失
        """
        # 约束嵌入
        constraint_aware_features = self.constraint_embedding(task_features, constraint_data)

        # 约束感知变换
        enhanced_features = self.constraint_aware_transform(constraint_aware_features)

        # 计算约束验证分数
        constraint_scores = self.constraint_validator(enhanced_features)

        # 应用约束分数到特征
        constrained_features = enhanced_features * constraint_scores

        # 计算物理约束损失（如果提供了分配概率）
        constraint_losses = {}
        if assignment_probs is not None:
            constraint_losses = self.physics_loss(assignment_probs, constraint_data)

        return constrained_features, constraint_losses