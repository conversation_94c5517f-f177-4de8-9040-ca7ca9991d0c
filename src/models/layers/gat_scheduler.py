import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional, Dict

from torch_geometric.nn import GATConv, global_mean_pool
import math


class HeterogeneousGATLayer(nn.Module):
    """异构图注意力层"""

    def __init__(self, in_dim: int, out_dim: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()

        self.in_dim = in_dim
        self.out_dim = out_dim
        self.num_heads = num_heads
        self.dropout = dropout

        # 任务-任务注意力（DAG依赖关系）
        self.task_task_attention = GATConv(
            in_channels=in_dim,
            out_channels=out_dim // num_heads,
            heads=num_heads,
            dropout=dropout,
            add_self_loops=False
        )

        # 任务-节点注意力（兼容性评估）
        self.task_node_attention = nn.MultiheadAttention(
            embed_dim=in_dim,
            num_heads=num_heads,
            dropout=dropout
        )

        # 节点特征处理
        self.node_feature_transform = nn.Sequential(
            nn.Linear(in_dim, out_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.LayerNorm(out_dim)
        )

        # 兼容性计算
        self.compatibility_scorer = nn.Sequential(
            nn.Linear(out_dim * 2, out_dim),
            nn.ReLU(),
            nn.Linear(out_dim, 1)
        )

    def forward(self, task_features: torch.Tensor, node_features: torch.Tensor,
                task_edge_index: torch.Tensor, task_batch: torch.Tensor,
                node_batch: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            task_features: [total_tasks, in_dim] 所有批次任务特征
            node_features: [total_nodes, in_dim] 所有批次节点特征  
            task_edge_index: [2, num_edges] 任务间边连接
            task_batch: [total_tasks] 任务所属批次索引
            node_batch: [total_nodes] 节点所属批次索引
        """
        # 任务间依赖关系建模
        task_features_updated = self.task_task_attention(task_features, task_edge_index)

        # 节点特征变换
        node_features_updated = self.node_feature_transform(node_features)

        # 计算任务-节点兼容性
        compatibility_scores = self._compute_compatibility(
            task_features_updated, node_features_updated, task_batch, node_batch
        )

        return task_features_updated, compatibility_scores

    def _compute_compatibility(self, task_features: torch.Tensor, node_features: torch.Tensor,
                               task_batch: torch.Tensor, node_batch: torch.Tensor) -> torch.Tensor:
        """计算任务-节点兼容性分数"""
        batch_size = task_batch.max().item() + 1
        compatibility_matrices = []

        for b in range(batch_size):
            # 获取当前批次的任务和节点
            task_mask = (task_batch == b)
            node_mask = (node_batch == b)

            batch_task_features = task_features[task_mask]  # [num_tasks_b, out_dim]
            batch_node_features = node_features[node_mask]  # [num_nodes_b, out_dim]

            # 计算兼容性矩阵
            num_tasks_b = batch_task_features.size(0)
            num_nodes_b = batch_node_features.size(0)

            compatibility_matrix = torch.zeros(num_tasks_b, num_nodes_b, device=task_features.device)

            for i in range(num_tasks_b):
                for j in range(num_nodes_b):
                    # 连接任务和节点特征
                    combined_features = torch.cat([
                        batch_task_features[i],
                        batch_node_features[j]
                    ], dim=0)

                    # 计算兼容性分数
                    compatibility_matrix[i, j] = self.compatibility_scorer(combined_features).squeeze()

            compatibility_matrices.append(compatibility_matrix)

        return compatibility_matrices


class ResourceAwareAttention(nn.Module):
    """资源感知注意力机制"""

    def __init__(self, feature_dim: int, num_heads: int = 8):
        super().__init__()

        self.feature_dim = feature_dim
        self.num_heads = num_heads
        self.head_dim = feature_dim // num_heads

        assert feature_dim % num_heads == 0

        # 查询、键、值变换
        self.w_q = nn.Linear(feature_dim, feature_dim)
        self.w_k = nn.Linear(feature_dim, feature_dim)
        self.w_v = nn.Linear(feature_dim, feature_dim)

        # 资源权重网络 - 接受resource_dim=4的输入
        self.resource_weight_network = nn.Sequential(
            nn.Linear(4, feature_dim // 4),  # 从4维资源约束映射到feature_dim//4
            nn.ReLU(),
            nn.Linear(feature_dim // 4, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, num_heads),
            nn.Softmax(dim=-1)
        )

        self.output_linear = nn.Linear(feature_dim, feature_dim)
        self.dropout = nn.Dropout(0.1)

    def forward(self, task_features: torch.Tensor, node_features: torch.Tensor,
                resource_constraints: torch.Tensor) -> torch.Tensor:
        """
        Args:
            task_features: [batch_size, num_tasks, feature_dim]
            node_features: [batch_size, num_nodes, feature_dim]
            resource_constraints: [batch_size, num_nodes, resource_dim]
        """
        batch_size, num_tasks, _ = task_features.size()
        _, num_nodes, _ = node_features.size()

        # 计算Q, K, V
        Q = self.w_q(task_features)  # [batch_size, num_tasks, feature_dim]
        K = self.w_k(node_features)  # [batch_size, num_nodes, feature_dim]
        V = self.w_v(node_features)  # [batch_size, num_nodes, feature_dim]

        # 计算资源权重
        resource_weights = self.resource_weight_network(resource_constraints)  # [batch_size, num_nodes, num_heads]

        # 计算注意力分数 - 使用更简单的方法
        # Q: [batch_size, num_tasks, feature_dim]
        # K: [batch_size, num_nodes, feature_dim]
        # 计算 Q @ K^T
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.feature_dim)  # [batch_size, num_tasks, num_nodes]

        # 应用资源权重 - 将资源权重扩展到任务维度
        # resource_weights: [batch_size, num_nodes, num_heads] -> [batch_size, num_nodes]
        resource_weights_avg = resource_weights.mean(dim=-1)  # [batch_size, num_nodes]
        scores = scores * resource_weights_avg.unsqueeze(1)  # [batch_size, num_tasks, num_nodes]

        # 注意力权重
        attention_weights = F.softmax(scores, dim=-1)  # [batch_size, num_tasks, num_nodes]
        attention_weights = self.dropout(attention_weights)

        # 应用注意力
        attended_values = torch.matmul(attention_weights, V)  # [batch_size, num_tasks, feature_dim]

        # 输出变换
        output = self.output_linear(attended_values)

        return output


class TaskNodeMatcher(nn.Module):
    """任务-节点匹配决策网络"""

    def __init__(self, feature_dim: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()

        self.feature_dim = feature_dim

        # 资源感知注意力
        self.resource_attention = ResourceAwareAttention(feature_dim, num_heads)

        # 匹配分数计算
        self.matching_network = nn.Sequential(
            nn.Linear(feature_dim * 2, feature_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, 1)
        )

        # 决策网络
        self.decision_network = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(feature_dim, 1),
            nn.Sigmoid()
        )

    def forward(self, task_features: torch.Tensor, node_features: torch.Tensor,
                resource_constraints: torch.Tensor,
                load_balance_weight: float = 0.1) -> torch.Tensor:
        """
        Args:
            task_features: [batch_size, num_tasks, feature_dim]
            node_features: [batch_size, num_nodes, feature_dim]  
            resource_constraints: [batch_size, num_nodes, resource_dim]
            load_balance_weight: 负载均衡权重
        """
        batch_size, num_tasks, _ = task_features.size()
        _, num_nodes, _ = node_features.size()

        # 资源感知注意力
        attended_task_features = self.resource_attention(
            task_features, node_features, resource_constraints
        )

        # 计算所有任务-节点对的匹配分数
        assignment_logits = torch.zeros(batch_size, num_tasks, num_nodes, device=task_features.device)

        for i in range(num_tasks):
            for j in range(num_nodes):
                # 任务i和节点j的特征组合
                task_feat = attended_task_features[:, i, :]  # [batch_size, feature_dim]
                node_feat = node_features[:, j, :]  # [batch_size, feature_dim]

                combined_feat = torch.cat([task_feat, node_feat], dim=-1)  # [batch_size, feature_dim * 2]

                # 计算匹配分数
                match_score = self.matching_network(combined_feat).squeeze(-1)  # [batch_size]
                assignment_logits[:, i, j] = match_score

        # 添加负载均衡约束
        if load_balance_weight > 0:
            # 计算当前负载分布
            current_loads = torch.sum(F.softmax(assignment_logits, dim=-1), dim=1)  # [batch_size, num_nodes]
            target_load = num_tasks / num_nodes

            # 负载均衡惩罚
            load_penalty = torch.abs(current_loads - target_load).sum(dim=-1, keepdim=True).unsqueeze(-1)
            assignment_logits = assignment_logits - load_balance_weight * load_penalty

        # 转换为概率分布
        assignment_probs = F.softmax(assignment_logits, dim=-1)

        return assignment_probs


class GATScheduler(nn.Module):
    """GAT决策输出层"""

    def __init__(self, input_dim: int, hidden_dim: int, num_heads: int = 8,
                 num_layers: int = 3, dropout: float = 0.1):
        super().__init__()

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        # 输入投影 - 为task_features和node_features使用不同的投影层
        self.task_input_projection = nn.Linear(input_dim, hidden_dim)
        self.node_input_projection = nn.Linear(128, hidden_dim)  # node_features通常是128维

        # 异构GAT层
        self.gat_layers = nn.ModuleList([
            HeterogeneousGATLayer(
                in_dim=hidden_dim if i > 0 else hidden_dim,
                out_dim=hidden_dim,
                num_heads=num_heads,
                dropout=dropout
            )
            for i in range(num_layers)
        ])

        # 任务-节点匹配器
        self.task_node_matcher = TaskNodeMatcher(
            feature_dim=hidden_dim,
            num_heads=num_heads,
            dropout=dropout
        )

        # 最终决策层
        self.final_decision = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()
        )

    def forward(self, task_features: torch.Tensor, node_features: torch.Tensor,
                task_edge_index: torch.Tensor, task_batch: torch.Tensor,
                node_batch: torch.Tensor, resource_constraints: torch.Tensor) -> Tuple[
        torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Args:
            task_features: [total_tasks, input_dim]
            node_features: [total_nodes, input_dim]
            task_edge_index: [2, num_edges]
            task_batch: [total_tasks]
            node_batch: [total_nodes]
            resource_constraints: [batch_size, max_nodes, resource_dim]

        Returns:
            assignment_probs: [batch_size, max_tasks, max_nodes]
            debug_info: 调试信息字典
        """
        debug_info = {}

        # 输入投影
        task_features = self.task_input_projection(task_features)
        node_features = self.node_input_projection(node_features)

        # 多层GAT处理
        for i, gat_layer in enumerate(self.gat_layers):
            task_features, compatibility_scores = gat_layer(
                task_features, node_features, task_edge_index, task_batch, node_batch
            )
            debug_info[f'layer_{i}_compatibility'] = compatibility_scores

        # 重塑为批次格式
        batch_size = task_batch.max().item() + 1
        max_tasks = max([(task_batch == b).sum().item() for b in range(batch_size)])
        max_nodes = max([(node_batch == b).sum().item() for b in range(batch_size)])

        batched_task_features = torch.zeros(batch_size, max_tasks, self.hidden_dim, device=task_features.device)
        batched_node_features = torch.zeros(batch_size, max_nodes, self.hidden_dim, device=node_features.device)

        for b in range(batch_size):
            task_mask = (task_batch == b)
            node_mask = (node_batch == b)

            batch_task_features = task_features[task_mask]
            batch_node_features = node_features[node_mask]

            num_tasks_b = batch_task_features.size(0)
            num_nodes_b = batch_node_features.size(0)

            batched_task_features[b, :num_tasks_b, :] = batch_task_features
            batched_node_features[b, :num_nodes_b, :] = batch_node_features

        # 任务-节点匹配决策
        assignment_probs = self.task_node_matcher(
            batched_task_features, batched_node_features, resource_constraints
        )

        debug_info['final_assignment_probs'] = assignment_probs
        debug_info['task_features'] = batched_task_features
        debug_info['node_features'] = batched_node_features

        return assignment_probs, debug_info