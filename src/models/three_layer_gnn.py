import torch
import torch.nn as nn
from typing import Dict, Tu<PERSON>, Optional, Any
from .layers.dag_transformer import DAGTransformer
from .layers.pinn_constraints import PINNConstraintLayer
from .layers.gat_scheduler import GATScheduler


class ThreeLayerGNNScheduler(nn.Module):
    """三层融合GNN调度器"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__()

        # 配置参数
        self.input_dim = config.get('input_dim', 128)
        self.hidden_dim = config.get('hidden_dim', 256)
        self.num_heads = config.get('num_heads', 8)
        self.num_transformer_layers = config.get('num_transformer_layers', 6)
        self.num_gat_layers = config.get('num_gat_layers', 3)
        self.dropout = config.get('dropout', 0.1)

        # 约束权重
        self.constraint_weights = config.get('constraint_weights', {
            'dependency': 1.0,
            'resource': 1.0,
            'temporal': 0.5,
            'communication': 0.5
        })

        # 第一层：Graph Transformer基础层
        self.dag_transformer = DAGTransformer(
            input_dim=self.input_dim,
            d_model=self.hidden_dim,
            num_heads=self.num_heads,
            num_layers=self.num_transformer_layers,
            d_ff=self.hidden_dim * 4,
            dropout=self.dropout
        )

        # 第二层：PINN约束增强层
        self.pinn_constraint_layer = PINNConstraintLayer(
            input_dim=self.hidden_dim,
            hidden_dim=self.hidden_dim,
            constraint_weights=self.constraint_weights
        )

        # 第三层：GAT决策输出层
        self.gat_scheduler = GATScheduler(
            input_dim=self.hidden_dim,  # 使用hidden_dim=256，匹配fused_features的维度
            hidden_dim=self.hidden_dim,
            num_heads=self.num_heads,
            num_layers=self.num_gat_layers,
            dropout=self.dropout
        )

        # 层间连接
        self.layer_fusion = nn.ModuleList([
            nn.Sequential(
                nn.Linear(self.hidden_dim * 2, self.hidden_dim),
                nn.ReLU(),
                nn.Dropout(self.dropout),
                nn.LayerNorm(self.hidden_dim)
            )
            for _ in range(2)  # transformer->pinn, pinn->gat
        ])

    def forward(self, batch_data: Dict[str, torch.Tensor],
                debug_mode: bool = False) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """
        Args:
            batch_data: 包含所有输入数据的字典
            debug_mode: 是否返回调试信息

        Returns:
            assignment_probs: [batch_size, max_tasks, max_nodes]
            debug_info: 调试信息字典
        """
        debug_info = {'layer_outputs': {}, 'losses': {}, 'attention_weights': {}}

        # 提取输入数据
        task_features = batch_data['task_features']  # [batch_size, max_tasks, input_dim]
        adjacency_matrix = batch_data['adjacency_matrix']  # [batch_size, max_tasks, max_tasks]
        node_features = batch_data['node_features']  # [total_nodes, input_dim]
        task_edge_index = batch_data['task_edge_index']  # [2, num_edges]
        task_batch = batch_data['task_batch']  # [total_tasks]
        node_batch = batch_data['node_batch']  # [total_nodes]
        resource_constraints = batch_data['resource_constraints']  # [batch_size, max_nodes, resource_dim]
        constraint_data = batch_data['constraint_data']  # 各种约束信息

        # 第一层：DAG Transformer
        print("🔄 第一层：DAG Transformer处理中...")
        transformer_output = self.dag_transformer(
            task_features, adjacency_matrix
        )

        if debug_mode:
            debug_info['layer_outputs']['transformer'] = transformer_output.detach().clone()
            print(f"   Transformer输出形状: {transformer_output.shape}")
            print(f"   Transformer输出均值: {transformer_output.mean().item():.4f}")

        # 第二层：PINN约束增强
        print("⚖️  第二层：PINN约束增强处理中...")

        # 创建当前的分配概率（用于约束损失计算）
        batch_size, max_tasks, _ = transformer_output.shape
        max_nodes = resource_constraints.shape[1]
        current_assignment_probs = torch.softmax(
            torch.randn(batch_size, max_tasks, max_nodes, device=transformer_output.device),
            dim=-1
        )

        constraint_enhanced_features, constraint_losses = self.pinn_constraint_layer(
            transformer_output, constraint_data, current_assignment_probs
        )

        # 层间融合
        fused_features_1 = self.layer_fusion[0](
            torch.cat([transformer_output, constraint_enhanced_features], dim=-1)
        )

        if debug_mode:
            debug_info['layer_outputs']['constraint_enhanced'] = constraint_enhanced_features.detach().clone()
            debug_info['layer_outputs']['fused_1'] = fused_features_1.detach().clone()
            debug_info['losses']['constraint_losses'] = constraint_losses
            print(f"   约束增强输出形状: {constraint_enhanced_features.shape}")
            print(f"   约束损失: {constraint_losses}")

        # 第三层：GAT决策输出
        print("🎯 第三层：GAT决策输出处理中...")

        # 将任务特征转换为图格式
        fused_task_features_flat = self._batch_to_graph_format(fused_features_1, task_batch)

        assignment_probs, gat_debug_info = self.gat_scheduler(
            fused_task_features_flat, node_features, task_edge_index,
            task_batch, node_batch, resource_constraints
        )

        if debug_mode:
            debug_info['layer_outputs']['gat_output'] = assignment_probs.detach().clone()
            debug_info['gat_debug'] = gat_debug_info
            print(f"   GAT输出形状: {assignment_probs.shape}")
            print(f"   分配概率和: {assignment_probs.sum(dim=-1).mean().item():.4f}")

        # 计算最终的约束损失（基于最终分配结果）
        final_constraint_enhanced_features, final_constraint_losses = self.pinn_constraint_layer(
            fused_features_1, constraint_data, assignment_probs
        )

        debug_info['losses']['final_constraint_losses'] = final_constraint_losses

        return assignment_probs, debug_info

    def _batch_to_graph_format(self, batched_features: torch.Tensor,
                               batch_indices: torch.Tensor) -> torch.Tensor:
        """将批次格式转换为图格式"""
        batch_size, max_tasks, feature_dim = batched_features.shape

        # 创建扁平化的特征张量
        flat_features = []

        for b in range(batch_size):
            # 获取当前批次的实际任务数
            actual_tasks = (batch_indices == b).sum().item()
            batch_features = batched_features[b, :actual_tasks, :]
            flat_features.append(batch_features)

        return torch.cat(flat_features, dim=0)

    def get_layer_outputs(self, batch_data: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """获取各层输出（用于调试和分析）"""
        with torch.no_grad():
            _, debug_info = self.forward(batch_data, debug_mode=True)
            return debug_info['layer_outputs']

    def compute_total_loss(self, assignment_probs: torch.Tensor,
                           ground_truth: torch.Tensor,
                           constraint_losses: Dict[str, torch.Tensor],
                           task_loss_weight: float = 1.0) -> Dict[str, torch.Tensor]:
        """计算总损失"""
        losses = {}

        # 任务分配损失（交叉熵）
        assignment_loss = nn.CrossEntropyLoss()(
            assignment_probs.view(-1, assignment_probs.size(-1)),
            ground_truth.view(-1)
        )
        losses['assignment'] = assignment_loss

        # 约束损失
        total_constraint_loss = 0.0
        for constraint_type, loss_value in constraint_losses.items():
            if constraint_type != 'total':
                losses[f'constraint_{constraint_type}'] = loss_value
                total_constraint_loss += loss_value

        losses['constraint_total'] = total_constraint_loss

        # 总损失
        total_loss = task_loss_weight * assignment_loss + total_constraint_loss
        losses['total'] = total_loss

        return losses


# ================================
# src/preprocessing/feature_extraction.py
# ================================
import numpy as np
import torch
from typing import Dict, List, Tuple, Any
import networkx as nx
from src.preprocessing.graph_coloring import ImprovedGraphColoring, ColoringResult


class WorkflowFeatureExtractor:
    """工作流特征提取器"""

    def __init__(self, feature_dim: int = 128):
        self.feature_dim = feature_dim
        self.graph_coloring = ImprovedGraphColoring()

        # 特征组件维度
        self.task_basic_dim = 32  # 基础任务特征
        self.resource_demand_dim = 16  # 资源需求特征
        self.dag_structure_dim = 24  # DAG结构特征
        self.coloring_feature_dim = 8  # 图着色特征
        self.workflow_context_dim = 24  # 工作流上下文特征
        self.statistical_dim = 24  # 统计特征

        assert (self.task_basic_dim + self.resource_demand_dim + self.dag_structure_dim +
                self.coloring_feature_dim + self.workflow_context_dim + self.statistical_dim) == feature_dim

    def extract_task_features(self, workflow_dag: nx.DiGraph,
                              workflow_type: str) -> Tuple[torch.Tensor, Dict[str, Any]]:
        """提取任务特征"""

        # 1. 执行图着色
        print("🎨 执行改进图着色算法...")
        coloring_result = self.graph_coloring.improved_graph_coloring(workflow_dag)

        # 2. 提取各类特征
        num_tasks = len(workflow_dag.nodes())
        task_features = torch.zeros(num_tasks, self.feature_dim)

        for i, task_id in enumerate(workflow_dag.nodes()):
            task_data = workflow_dag.nodes[task_id]

            # 基础任务特征
            basic_features = self._extract_basic_task_features(task_data)

            # 资源需求特征  
            resource_features = self._extract_resource_demand_features(task_data)

            # DAG结构特征
            structure_features = self._extract_dag_structure_features(workflow_dag, task_id)

            # 图着色特征
            coloring_features = coloring_result.color_features[task_id]

            # 工作流上下文特征
            context_features = self._extract_workflow_context_features(
                workflow_dag, task_id, workflow_type, coloring_result
            )

            # 统计特征
            statistical_features = self._extract_statistical_features(workflow_dag, task_id)

            # 组合所有特征
            task_features[i] = torch.cat([
                torch.tensor(basic_features, dtype=torch.float32),
                torch.tensor(resource_features, dtype=torch.float32),
                torch.tensor(structure_features, dtype=torch.float32),
                torch.tensor(coloring_features, dtype=torch.float32),
                torch.tensor(context_features, dtype=torch.float32),
                torch.tensor(statistical_features, dtype=torch.float32)
            ])

        # 归一化特征
        task_features = self._normalize_features(task_features)

        # 准备元数据
        metadata = {
            'coloring_result': coloring_result,
            'workflow_type': workflow_type,
            'num_tasks': num_tasks,
            'feature_dims': {
                'basic': self.task_basic_dim,
                'resource': self.resource_demand_dim,
                'structure': self.dag_structure_dim,
                'coloring': self.coloring_feature_dim,
                'context': self.workflow_context_dim,
                'statistical': self.statistical_dim
            }
        }

        return task_features, metadata

    def _extract_basic_task_features(self, task_data: Dict) -> np.ndarray:
        """提取基础任务特征"""
        features = np.zeros(self.task_basic_dim)

        # 执行时间相关特征
        runtime = task_data.get('runtime', 0)
        features[0] = np.log1p(runtime)  # log变换减少偏态
        features[1] = runtime / 100.0  # 归一化运行时间

        # 数据大小特征
        input_size = task_data.get('input_size', 0)
        output_size = task_data.get('output_size', 0)
        total_data = input_size + output_size

        features[2] = np.log1p(input_size)
        features[3] = np.log1p(output_size)
        features[4] = np.log1p(total_data)
        features[5] = output_size / max(input_size, 1e-6)  # 输出/输入比率

        # 计算强度特征
        features[6] = runtime / max(total_data, 1e-6)  # 计算密集度
        features[7] = total_data / max(runtime, 1e-6)  # 数据密集度

        # 其他基础特征
        features[8:32] = np.random.normal(0, 0.1, 24)  # 预留空间，可以添加更多特征

        return features

    def _extract_resource_demand_features(self, task_data: Dict) -> np.ndarray:
        """提取资源需求特征"""
        features = np.zeros(self.resource_demand_dim)

        # 原始资源需求
        cpu_demand = task_data.get('cpu_demand', 0)
        memory_demand = task_data.get('memory_demand', 0)
        io_demand = task_data.get('io_demand', 0)
        network_demand = task_data.get('network_demand', 0)

        # 归一化资源需求
        total_demand = cpu_demand + memory_demand + io_demand + network_demand
        if total_demand > 0:
            features[0] = cpu_demand / total_demand
            features[1] = memory_demand / total_demand
            features[2] = io_demand / total_demand
            features[3] = network_demand / total_demand

        # 绝对资源需求（log变换）
        features[4] = np.log1p(cpu_demand)
        features[5] = np.log1p(memory_demand)
        features[6] = np.log1p(io_demand)
        features[7] = np.log1p(network_demand)

        # 资源需求比率
        features[8] = cpu_demand / max(memory_demand, 1e-6)
        features[9] = io_demand / max(network_demand, 1e-6)
        features[10] = (cpu_demand + memory_demand) / max(io_demand + network_demand, 1e-6)

        # 资源主导性指标
        max_demand = max(cpu_demand, memory_demand, io_demand, network_demand)
        features[11] = 1.0 if cpu_demand == max_demand else 0.0
        features[12] = 1.0 if memory_demand == max_demand else 0.0
        features[13] = 1.0 if io_demand == max_demand else 0.0
        features[14] = 1.0 if network_demand == max_demand else 0.0

        # 资源平衡度
        demands = [cpu_demand, memory_demand, io_demand, network_demand]
        features[15] = np.std(demands) / max(np.mean(demands), 1e-6)

        return features

    def _extract_dag_structure_features(self, dag: nx.DiGraph, task_id: int) -> np.ndarray:
        """提取DAG结构特征"""
        features = np.zeros(self.dag_structure_dim)

        # 度数特征
        features[0] = dag.in_degree(task_id)  # 入度
        features[1] = dag.out_degree(task_id)  # 出度
        features[2] = dag.degree(task_id)  # 总度数

        # 路径特征
        try:
            # 到源节点的最短路径
            source_nodes = [n for n in dag.nodes() if dag.in_degree(n) == 0]
            if source_nodes:
                min_source_dist = min([nx.shortest_path_length(dag, source, task_id)
                                       for source in source_nodes if nx.has_path(dag, source, task_id)])
                features[3] = min_source_dist

            # 到汇节点的最短路径
            sink_nodes = [n for n in dag.nodes() if dag.out_degree(n) == 0]
            if sink_nodes:
                min_sink_dist = min([nx.shortest_path_length(dag, task_id, sink)
                                     for sink in sink_nodes if nx.has_path(dag, task_id, sink)])
                features[4] = min_sink_dist
        except:
            features[3] = 0
            features[4] = 0

        # 关键路径特征
        try:
            critical_path = nx.dag_longest_path(dag, weight='runtime')
            features[5] = 1.0 if task_id in critical_path else 0.0
            if task_id in critical_path:
                features[6] = critical_path.index(task_id) / len(critical_path)  # 在关键路径中的位置
        except:
            features[5] = 0.0
            features[6] = 0.0

        # 子图特征
        predecessors = set(dag.predecessors(task_id))
        successors = set(dag.successors(task_id))

        features[7] = len(predecessors)  # 直接前驱数量
        features[8] = len(successors)  # 直接后继数量

        # 传递闭包特征
        try:
            # 所有祖先节点
            ancestors = nx.ancestors(dag, task_id)
            descendants = nx.descendants(dag, task_id)

            features[9] = len(ancestors)  # 祖先节点数量
            features[10] = len(descendants)  # 后代节点数量
            features[11] = len(ancestors) + len(descendants)  # 相关节点总数
        except:
            features[9] = features[10] = features[11] = 0

        # 局部聚类系数
        try:
            # 转换为无向图计算聚类系数
            undirected_dag = dag.to_undirected()
            features[12] = nx.clustering(undirected_dag, task_id)
        except:
            features[12] = 0.0

        # 中心性指标
        try:
            betweenness = nx.betweenness_centrality(dag)
            closeness = nx.closeness_centrality(dag)
            features[13] = betweenness.get(task_id, 0.0)
            features[14] = closeness.get(task_id, 0.0)
        except:
            features[13] = features[14] = 0.0

        # 拓扑特征
        try:
            topo_order = list(nx.topological_sort(dag))
            features[15] = topo_order.index(task_id) / len(topo_order)  # 拓扑位置
        except:
            features[15] = 0.0

        # 并行度特征
        try:
            # 计算可以与当前任务并行执行的任务数量
            parallel_tasks = []
            for other_task in dag.nodes():
                if (other_task != task_id and
                        not nx.has_path(dag, task_id, other_task) and
                        not nx.has_path(dag, other_task, task_id)):
                    parallel_tasks.append(other_task)
            features[16] = len(parallel_tasks)
        except:
            features[16] = 0.0

        # 其他结构特征
        features[17:24] = np.random.normal(0, 0.1, 7)  # 预留空间

        return features

    def _extract_workflow_context_features(self, dag: nx.DiGraph, task_id: int,
                                           workflow_type: str, coloring_result: ColoringResult) -> np.ndarray:
        """提取工作流上下文特征"""
        features = np.zeros(self.workflow_context_dim)

        # 工作流类型独热编码
        workflow_types = ['montage', 'cybershake', 'ligo', 'sipht']
        if workflow_type in workflow_types:
            features[workflow_types.index(workflow_type)] = 1.0
        else:
            features[4] = 1.0  # 其他类型

        # 着色相关特征
        task_color = coloring_result.task_colors.get(task_id, 0)
        task_type = coloring_result.task_types.get(task_id)

        features[5] = task_color / max(max(coloring_result.task_colors.values()), 1)  # 归一化颜色

        # 资源类型独热编码
        resource_types = ['cpu_intensive', 'memory_intensive', 'io_intensive', 'network_intensive', 'mixed']
        if task_type and task_type.value in resource_types:
            features[6 + resource_types.index(task_type.value)] = 1.0

        # 并行组信息
        in_parallel_group = any(task_id in group for group in coloring_result.parallelizable_groups)
        features[11] = 1.0 if in_parallel_group else 0.0

        if in_parallel_group:
            # 找到所在的并行组
            for group in coloring_result.parallelizable_groups:
                if task_id in group:
                    features[12] = len(group)  # 并行组大小
                    break

        # 关键路径颜色特征
        if coloring_result.critical_path_colors:
            features[13] = 1.0 if task_color in coloring_result.critical_path_colors else 0.0
            features[14] = coloring_result.critical_path_colors.count(task_color) / len(
                coloring_result.critical_path_colors)

        # 着色质量相关特征
        quality_metrics = coloring_result.coloring_quality
        features[15] = quality_metrics.get('num_colors', 0) / len(dag.nodes())
        features[16] = quality_metrics.get('conflict_rate', 0)
        features[17] = quality_metrics.get('load_balance', 0)
        features[18] = quality_metrics.get('type_consistency', 0)

        # 工作流规模特征
        features[19] = len(dag.nodes()) / 100.0  # 归一化任务数量
        features[20] = len(dag.edges()) / len(dag.nodes()) if len(dag.nodes()) > 0 else 0  # 边密度

        # 其他上下文特征
        features[21:24] = np.random.normal(0, 0.1, 3)  # 预留空间

        return features

    def _extract_statistical_features(self, dag: nx.DiGraph, task_id: int) -> np.ndarray:
        """提取统计特征"""
        features = np.zeros(self.statistical_dim)

        # 收集所有任务的运行时间
        runtimes = [dag.nodes[node].get('runtime', 0) for node in dag.nodes()]
        current_runtime = dag.nodes[task_id].get('runtime', 0)

        # 运行时间统计特征
        if runtimes:
            features[0] = np.mean(runtimes)
            features[1] = np.std(runtimes)
            features[2] = np.min(runtimes)
            features[3] = np.max(runtimes)
            features[4] = np.median(runtimes)

            # 当前任务相对于统计值的位置
            features[5] = (current_runtime - np.mean(runtimes)) / max(np.std(runtimes), 1e-6)  # Z分数
            features[6] = (current_runtime - np.min(runtimes)) / max(np.max(runtimes) - np.min(runtimes), 1e-6)  # 归一化位置

        # 资源需求统计
        cpu_demands = [dag.nodes[node].get('cpu_demand', 0) for node in dag.nodes()]
        memory_demands = [dag.nodes[node].get('memory_demand', 0) for node in dag.nodes()]

        current_cpu = dag.nodes[task_id].get('cpu_demand', 0)
        current_memory = dag.nodes[task_id].get('memory_demand', 0)

        if cpu_demands:
            features[7] = (current_cpu - np.mean(cpu_demands)) / max(np.std(cpu_demands), 1e-6)
        if memory_demands:
            features[8] = (current_memory - np.mean(memory_demands)) / max(np.std(memory_demands), 1e-6)

        # 度数统计
        degrees = [dag.degree(node) for node in dag.nodes()]
        current_degree = dag.degree(task_id)

        if degrees:
            features[9] = np.mean(degrees)
            features[10] = (current_degree - np.mean(degrees)) / max(np.std(degrees), 1e-6)

        # 邻居特征统计
        neighbors = list(dag.predecessors(task_id)) + list(dag.successors(task_id))
        if neighbors:
            neighbor_runtimes = [dag.nodes[neighbor].get('runtime', 0) for neighbor in neighbors]
            features[11] = np.mean(neighbor_runtimes)
            features[12] = np.std(neighbor_runtimes)

            neighbor_cpu_demands = [dag.nodes[neighbor].get('cpu_demand', 0) for neighbor in neighbors]
            features[13] = np.mean(neighbor_cpu_demands)

        # 局部网络统计
        subgraph_nodes = [task_id] + neighbors
        subgraph = dag.subgraph(subgraph_nodes)
        features[14] = len(subgraph.edges()) / max(len(subgraph.nodes()), 1)  # 局部密度

        # 其他统计特征
        features[15:24] = np.random.normal(0, 0.1, 9)  # 预留空间

        return features

    def _normalize_features(self, features: torch.Tensor) -> torch.Tensor:
        """归一化特征"""
        # 对每个特征维度进行Z-score归一化
        mean = features.mean(dim=0, keepdim=True)
        std = features.std(dim=0, keepdim=True)

        # 避免除零
        std = torch.where(std == 0, torch.ones_like(std), std)

        normalized_features = (features - mean) / std

        # 用tanh限制范围
        normalized_features = torch.tanh(normalized_features)

        return normalized_features

    def extract_node_features(self, nodes: List[Dict]) -> torch.Tensor:
        """提取计算节点特征"""
        num_nodes = len(nodes)
        node_feature_dim = 32  # 节点特征维度

        node_features = torch.zeros(num_nodes, node_feature_dim)

        for i, node_data in enumerate(nodes):
            # 基础容量特征
            node_features[i, 0] = np.log1p(node_data.get('cpu_capacity', 0))
            node_features[i, 1] = np.log1p(node_data.get('memory_capacity', 0))
            node_features[i, 2] = np.log1p(node_data.get('io_capacity', 0))
            node_features[i, 3] = np.log1p(node_data.get('network_capacity', 0))

            # 归一化容量（相对于所有节点）
            all_cpu = [n.get('cpu_capacity', 0) for n in nodes]
            all_memory = [n.get('memory_capacity', 0) for n in nodes]
            all_io = [n.get('io_capacity', 0) for n in nodes]
            all_network = [n.get('network_capacity', 0) for n in nodes]

            node_features[i, 4] = node_data.get('cpu_capacity', 0) / max(max(all_cpu), 1e-6)
            node_features[i, 5] = node_data.get('memory_capacity', 0) / max(max(all_memory), 1e-6)
            node_features[i, 6] = node_data.get('io_capacity', 0) / max(max(all_io), 1e-6)
            node_features[i, 7] = node_data.get('network_capacity', 0) / max(max(all_network), 1e-6)

            # 效率和成本特征
            node_features[i, 8] = node_data.get('energy_efficiency', 1.0)
            node_features[i, 9] = node_data.get('cost_per_hour', 0)

            # 节点类型特征（基于容量比例）
            total_capacity = (node_data.get('cpu_capacity', 0) +
                              node_data.get('memory_capacity', 0) +
                              node_data.get('io_capacity', 0) +
                              node_data.get('network_capacity', 0))

            if total_capacity > 0:
                node_features[i, 10] = node_data.get('cpu_capacity', 0) / total_capacity
                node_features[i, 11] = node_data.get('memory_capacity', 0) / total_capacity
                node_features[i, 12] = node_data.get('io_capacity', 0) / total_capacity
                node_features[i, 13] = node_data.get('network_capacity', 0) / total_capacity

            # 其他特征
            node_features[i, 14:32] = torch.randn(18) * 0.1  # 预留空间

        return node_features