import torch
import numpy as np
from typing import Dict, List, Tuple, Any
import networkx as nx


class SchedulingMetrics:
    """调度性能指标计算器"""

    def __init__(self):
        self.metric_names = [
            'makespan', 'resource_utilization', 'load_balance_degree',
            'energy_consumption', 'cost', 'throughput', 'response_time'
        ]

    def compute_makespan(self, assignment: torch.Tensor, batch_data: Dict[str, torch.Tensor]) -> float:
        """计算完工时间（Makespan）"""
        batch_size = assignment.shape[0]
        total_makespan = 0.0

        for b in range(batch_size):
            batch_assignment = assignment[b]  # [num_tasks, num_nodes]

            # 获取任务执行时间和依赖关系
            task_runtimes = batch_data.get('task_runtimes', torch.ones_like(batch_assignment[:, 0]))
            adjacency_matrix = batch_data.get('adjacency_matrix',
                                              torch.zeros(batch_assignment.shape[0], batch_assignment.shape[0]))

            if len(task_runtimes.shape) > 1:
                task_runtimes = task_runtimes[b]
            if len(adjacency_matrix.shape) > 2:
                adjacency_matrix = adjacency_matrix[b]

            # 计算每个节点的完成时间
            num_tasks, num_nodes = batch_assignment.shape
            node_completion_times = torch.zeros(num_nodes)
            task_completion_times = torch.zeros(num_tasks)

            # 拓扑排序处理依赖关系（简化实现）
            for task_id in range(num_tasks):
                # 找到分配的节点
                assigned_node = torch.argmax(batch_assignment[task_id]).item()

                # 计算开始时间（考虑依赖）
                start_time = node_completion_times[assigned_node]

                # 检查依赖任务的完成时间
                for pred_task in range(num_tasks):
                    if adjacency_matrix[pred_task, task_id] > 0:  # 存在依赖
                        start_time = max(start_time, task_completion_times[pred_task])

                # 计算完成时间
                execution_time = task_runtimes[task_id].item() if task_id < len(task_runtimes) else 1.0
                finish_time = start_time + execution_time

                task_completion_times[task_id] = finish_time
                node_completion_times[assigned_node] = finish_time

            # 计算makespan
            makespan = torch.max(task_completion_times).item()
            total_makespan += makespan

        return total_makespan / batch_size

    def compute_resource_utilization(self, assignment: torch.Tensor,
                                     batch_data: Dict[str, torch.Tensor]) -> float:
        """计算资源利用率"""
        batch_size = assignment.shape[0]
        total_utilization = 0.0

        for b in range(batch_size):
            batch_assignment = assignment[b]

            # 获取任务资源需求和节点容量
            task_demands = batch_data.get('task_demands', torch.ones(batch_assignment.shape[0], 4))
            node_capacities = batch_data.get('node_capacities', torch.ones(batch_assignment.shape[1], 4))

            if len(task_demands.shape) > 2:
                task_demands = task_demands[b]
            if len(node_capacities.shape) > 2:
                node_capacities = node_capacities[b]

            # 计算每个节点的资源使用量
            num_nodes = batch_assignment.shape[1]
            node_utilizations = []

            for node_id in range(num_nodes):
                # 分配到此节点的任务
                assigned_tasks = torch.nonzero(batch_assignment[:, node_id] == 1).squeeze(-1)

                if len(assigned_tasks) > 0:
                    # 计算总资源需求
                    total_demand = torch.sum(task_demands[assigned_tasks], dim=0)
                    # 计算利用率
                    utilization = total_demand / (node_capacities[node_id] + 1e-6)
                    node_utilizations.append(torch.mean(utilization).item())
                else:
                    node_utilizations.append(0.0)

            batch_utilization = np.mean(node_utilizations)
            total_utilization += batch_utilization

        return total_utilization / batch_size

    def compute_load_balance_degree(self, assignment: torch.Tensor,
                                    batch_data: Dict[str, torch.Tensor]) -> float:
        """计算负载均衡度"""
        batch_size = assignment.shape[0]
        total_lbd = 0.0

        for b in range(batch_size):
            batch_assignment = assignment[b]

            # 计算每个节点的负载
            node_loads = torch.sum(batch_assignment, dim=0)  # 每个节点分配到的任务数

            # 计算负载标准差（越小越均衡）
            if len(node_loads) > 1:
                load_std = torch.std(node_loads.float()).item()
                load_mean = torch.mean(node_loads.float()).item()

                # 负载均衡度（标准差与均值的比值，越小越好）
                lbd = load_std / max(load_mean, 1e-6)
            else:
                lbd = 0.0

            total_lbd += lbd

        return total_lbd / batch_size

    def compute_energy_consumption(self, assignment: torch.Tensor,
                                   batch_data: Dict[str, torch.Tensor]) -> float:
        """计算能耗"""
        batch_size = assignment.shape[0]
        total_energy = 0.0

        for b in range(batch_size):
            batch_assignment = assignment[b]

            # 获取节点能效信息
            node_energy_efficiency = batch_data.get('node_energy_efficiency',
                                                    torch.ones(batch_assignment.shape[1]))
            task_runtimes = batch_data.get('task_runtimes',
                                           torch.ones(batch_assignment.shape[0]))

            if len(node_energy_efficiency.shape) > 1:
                node_energy_efficiency = node_energy_efficiency[b]
            if len(task_runtimes.shape) > 1:
                task_runtimes = task_runtimes[b]

            batch_energy = 0.0

            for task_id in range(batch_assignment.shape[0]):
                assigned_node = torch.argmax(batch_assignment[task_id]).item()

                # 能耗 = 执行时间 / 能效比
                task_runtime = task_runtimes[task_id].item() if task_id < len(task_runtimes) else 1.0
                energy_eff = node_energy_efficiency[assigned_node].item()

                task_energy = task_runtime / max(energy_eff, 1e-6)
                batch_energy += task_energy

            total_energy += batch_energy

        return total_energy / batch_size

    def compute_cost(self, assignment: torch.Tensor,
                     batch_data: Dict[str, torch.Tensor]) -> float:
        """计算执行成本"""
        batch_size = assignment.shape[0]
        total_cost = 0.0

        for b in range(batch_size):
            batch_assignment = assignment[b]

            # 获取节点成本信息
            node_costs = batch_data.get('node_costs', torch.ones(batch_assignment.shape[1]))
            task_runtimes = batch_data.get('task_runtimes', torch.ones(batch_assignment.shape[0]))

            if len(node_costs.shape) > 1:
                node_costs = node_costs[b]
            if len(task_runtimes.shape) > 1:
                task_runtimes = task_runtimes[b]

            batch_cost = 0.0

            for task_id in range(batch_assignment.shape[0]):
                assigned_node = torch.argmax(batch_assignment[task_id]).item()

                # 成本 = 执行时间 × 节点小时成本
                task_runtime = task_runtimes[task_id].item() if task_id < len(task_runtimes) else 1.0
                node_cost_per_hour = node_costs[assigned_node].item()

                task_cost = (task_runtime / 3600.0) * node_cost_per_hour  # 转换为小时
                batch_cost += task_cost

            total_cost += batch_cost

        return total_cost / batch_size

    def compute_throughput(self, assignment: torch.Tensor,
                           batch_data: Dict[str, torch.Tensor]) -> float:
        """计算吞吐量（任务/秒）"""
        makespan = self.compute_makespan(assignment, batch_data)
        num_tasks = assignment.shape[1]  # 假设所有批次任务数相同

        if makespan > 0:
            return num_tasks / makespan
        else:
            return 0.0

    def compute_response_time(self, assignment: torch.Tensor,
                              batch_data: Dict[str, torch.Tensor]) -> float:
        """计算平均响应时间"""
        batch_size = assignment.shape[0]
        total_response_time = 0.0

        for b in range(batch_size):
            batch_assignment = assignment[b]
            task_runtimes = batch_data.get('task_runtimes', torch.ones(batch_assignment.shape[0]))

            if len(task_runtimes.shape) > 1:
                task_runtimes = task_runtimes[b]

            # 简化计算：假设响应时间等于平均执行时间
            avg_runtime = torch.mean(task_runtimes).item()
            total_response_time += avg_runtime

        return total_response_time / batch_size

    def compute_batch_metrics(self, assignment: torch.Tensor,
                              batch_data: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """计算一个批次的所有指标"""
        metrics = {}

        metrics['makespan'] = self.compute_makespan(assignment, batch_data)
        metrics['resource_utilization'] = self.compute_resource_utilization(assignment, batch_data)
        metrics['load_balance_degree'] = self.compute_load_balance_degree(assignment, batch_data)
        metrics['energy_consumption'] = self.compute_energy_consumption(assignment, batch_data)
        metrics['cost'] = self.compute_cost(assignment, batch_data)
        metrics['throughput'] = self.compute_throughput(assignment, batch_data)
        metrics['response_time'] = self.compute_response_time(assignment, batch_data)

        return metrics

    def aggregate_metrics(self, batch_metrics_list: List[Dict[str, float]]) -> Dict[str, float]:
        """聚合多个批次的指标"""
        if not batch_metrics_list:
            return {}

        aggregated = {}

        for metric_name in self.metric_names:
            values = [metrics.get(metric_name, 0.0) for metrics in batch_metrics_list]

            aggregated[metric_name] = np.mean(values)
            aggregated[f'{metric_name}_std'] = np.std(values)
            aggregated[f'{metric_name}_min'] = np.min(values)
            aggregated[f'{metric_name}_max'] = np.max(values)

        return aggregated