import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import torch
import networkx as nx
from typing import Dict, Any, List, Optional
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import os
from pathlib import Path


class EnhancedWorkflowVisualizer:
    """增强的工作流可视化器，支持交互式图表和实时监控"""
    
    def __init__(self, output_dir: str):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # 颜色配置
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'accent': '#F18F01',
            'success': '#C73E1D',
            'tasks': '#4CAF50',
            'nodes': '#2196F3',
            'edges': '#757575',
            'critical': '#F44336'
        }
    
    def create_training_dashboard(self, train_history: Dict[str, List]) -> str:
        """创建训练监控仪表板"""
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('训练损失', '验证指标', '学习率变化', '性能指标'),
            specs=[[{"secondary_y": True}, {"secondary_y": True}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        epochs = list(range(len(train_history['losses'])))
        
        # 训练损失
        if 'losses' in train_history:
            losses = [loss.get('total', 0) for loss in train_history['losses']]
            fig.add_trace(
                go.Scatter(x=epochs, y=losses, name='训练损失', line=dict(color=self.colors['primary'])),
                row=1, col=1
            )
        
        # 验证指标
        if 'val_losses' in train_history:
            val_losses = [loss.get('total', 0) for loss in train_history['val_losses']]
            fig.add_trace(
                go.Scatter(x=epochs, y=val_losses, name='验证损失', line=dict(color=self.colors['secondary'])),
                row=1, col=2
            )
        
        # 学习率
        if 'lr' in train_history:
            fig.add_trace(
                go.Scatter(x=epochs, y=train_history['lr'], name='学习率', line=dict(color=self.colors['accent'])),
                row=2, col=1
            )
        
        # 性能指标
        if 'metrics' in train_history and train_history['metrics']:
            makespan_values = [m.get('makespan', 0) for m in train_history['metrics']]
            fig.add_trace(
                go.Scatter(x=epochs, y=makespan_values, name='Makespan', line=dict(color=self.colors['success'])),
                row=2, col=2
            )
        
        fig.update_layout(
            title='GNN工作流调度器训练监控',
            height=800,
            showlegend=True
        )
        
        output_path = self.output_dir / 'training_dashboard.html'
        fig.write_html(str(output_path))
        return str(output_path)
    
    def create_interactive_dag_visualization(self, workflow_data: Dict, 
                                           assignment_probs: Optional[torch.Tensor] = None) -> str:
        """创建交互式DAG可视化"""
        dag_data = workflow_data['dag']
        dag = nx.node_link_graph(dag_data)
        
        # 计算布局
        pos = nx.spring_layout(dag, k=3, iterations=50)
        
        # 提取节点和边信息
        node_x = [pos[node][0] for node in dag.nodes()]
        node_y = [pos[node][1] for node in dag.nodes()]
        node_text = [f"任务 {node}<br>运行时间: {dag.nodes[node].get('runtime', 'N/A')}" 
                     for node in dag.nodes()]
        
        # 边信息
        edge_x = []
        edge_y = []
        for edge in dag.edges():
            x0, y0 = pos[edge[0]]
            x1, y1 = pos[edge[1]]
            edge_x.extend([x0, x1, None])
            edge_y.extend([y0, y1, None])
        
        # 创建图形
        fig = go.Figure()
        
        # 添加边
        fig.add_trace(go.Scatter(
            x=edge_x, y=edge_y,
            line=dict(width=2, color=self.colors['edges']),
            hoverinfo='none',
            mode='lines',
            name='依赖关系'
        ))
        
        # 节点颜色
        node_colors = [self.colors['tasks']] * len(dag.nodes())
        if assignment_probs is not None:
            # 根据分配概率着色
            max_probs = assignment_probs.max(dim=-1)[0].squeeze().cpu().numpy()
            node_colors = px.colors.sample_colorscale("Viridis", max_probs)
        
        # 添加节点
        fig.add_trace(go.Scatter(
            x=node_x, y=node_y,
            mode='markers+text',
            marker=dict(size=20, color=node_colors, line=dict(width=2, color='white')),
            text=[str(node) for node in dag.nodes()],
            textposition="middle center",
            hovertext=node_text,
            hoverinfo='text',
            name='任务节点'
        ))
        
        fig.update_layout(
            title='工作流DAG结构（交互式）',
            showlegend=True,
            hovermode='closest',
            margin=dict(b=20,l=5,r=5,t=40),
            annotations=[ dict(
                text="拖拽节点可以调整布局",
                showarrow=False,
                xref="paper", yref="paper",
                x=0.005, y=-0.002,
                xanchor='left', yanchor='bottom',
                font=dict(color="gray", size=12)
            )],
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False)
        )
        
        output_path = self.output_dir / 'interactive_dag.html'
        fig.write_html(str(output_path))
        return str(output_path)
    
    def create_performance_comparison_chart(self, results: Dict[str, Dict]) -> str:
        """创建性能对比图表"""
        algorithms = list(results.keys())
        metrics = ['makespan', 'load_balance', 'resource_utilization', 'energy_consumption']
        metric_names = ['Makespan (s)', '负载均衡度', '资源利用率', '能耗 (kWh)']
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=metric_names,
            specs=[[{"type": "bar"}, {"type": "bar"}],
                   [{"type": "bar"}, {"type": "bar"}]]
        )
        
        colors = px.colors.qualitative.Set3[:len(algorithms)]
        
        for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
            row = i // 2 + 1
            col = i % 2 + 1
            
            values = [results[alg].get(metric, 0) for alg in algorithms]
            
            fig.add_trace(
                go.Bar(
                    x=algorithms,
                    y=values,
                    name=metric_name,
                    marker_color=colors,
                    showlegend=False
                ),
                row=row, col=col
            )
        
        fig.update_layout(
            title='算法性能对比',
            height=600,
            showlegend=False
        )
        
        output_path = self.output_dir / 'performance_comparison.html'
        fig.write_html(str(output_path))
        return str(output_path)
    
    def create_resource_utilization_heatmap(self, node_features: torch.Tensor, 
                                          assignment_probs: torch.Tensor) -> str:
        """创建资源利用率热力图"""
        node_features_np = node_features.detach().cpu().numpy()
        assignment_np = assignment_probs.detach().cpu().numpy()
        
        # 计算资源利用率
        if len(assignment_np.shape) == 3:
            assignment_np = assignment_np[0]  # 取第一个批次
        
        # 资源类型
        resource_types = ['CPU', 'Memory', 'I/O', 'Network']
        
        # 计算每个节点的资源利用率
        utilization = np.zeros((len(node_features_np), 4))
        for i in range(len(node_features_np)):
            for j in range(4):
                if j < node_features_np.shape[1]:
                    capacity = node_features_np[i, j]
                    # 计算分配到该节点的任务总需求
                    assigned_tasks = assignment_np[:, i] > 0.5
                    if capacity > 0:
                        utilization[i, j] = min(1.0, np.sum(assigned_tasks) * 0.2)  # 简化计算
        
        fig = go.Figure(data=go.Heatmap(
            z=utilization.T,
            x=[f'节点 {i}' for i in range(len(node_features_np))],
            y=resource_types,
            colorscale='RdYlBu_r',
            colorbar=dict(title="利用率")
        ))
        
        fig.update_layout(
            title='节点资源利用率热力图',
            xaxis_title='计算节点',
            yaxis_title='资源类型'
        )
        
        output_path = self.output_dir / 'resource_utilization_heatmap.html'
        fig.write_html(str(output_path))
        return str(output_path)
    
    def create_layer_analysis_dashboard(self, debug_info: Dict[str, Any]) -> str:
        """创建层分析仪表板"""
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Transformer层激活', 'PINN约束层激活', '约束损失分布', 'GAT层注意力权重'),
            specs=[[{"type": "heatmap"}, {"type": "heatmap"}],
                   [{"type": "histogram"}, {"type": "heatmap"}]]
        )
        
        # Transformer层激活
        if 'transformer' in debug_info.get('layer_outputs', {}):
            transformer_output = debug_info['layer_outputs']['transformer'].detach().cpu().numpy()
            if len(transformer_output.shape) == 3:
                transformer_output = transformer_output[0]  # 取第一个批次
            
            fig.add_trace(
                go.Heatmap(
                    z=transformer_output,
                    colorscale='Viridis',
                    showscale=False
                ),
                row=1, col=1
            )
        
        # PINN约束层激活
        if 'constraint_enhanced' in debug_info.get('layer_outputs', {}):
            constraint_output = debug_info['layer_outputs']['constraint_enhanced'].detach().cpu().numpy()
            if len(constraint_output.shape) == 3:
                constraint_output = constraint_output[0]
            
            fig.add_trace(
                go.Heatmap(
                    z=constraint_output,
                    colorscale='Plasma',
                    showscale=False
                ),
                row=1, col=2
            )
        
        # 约束损失分布
        if 'losses' in debug_info and 'final_constraint_losses' in debug_info['losses']:
            constraint_losses = debug_info['losses']['final_constraint_losses']
            if isinstance(constraint_losses, torch.Tensor):
                losses_np = constraint_losses.detach().cpu().numpy().flatten()
                fig.add_trace(
                    go.Histogram(
                        x=losses_np,
                        nbinsx=20,
                        marker_color=self.colors['accent']
                    ),
                    row=2, col=1
                )
        
        fig.update_layout(
            title='GNN层分析仪表板',
            height=800,
            showlegend=False
        )
        
        output_path = self.output_dir / 'layer_analysis_dashboard.html'
        fig.write_html(str(output_path))
        return str(output_path)
    
    def generate_comprehensive_report(self, workflow_data: Dict, debug_info: Dict, 
                                    assignment_probs: torch.Tensor, train_history: Dict) -> str:
        """生成综合分析报告"""
        report_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>GNN工作流调度器分析报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: {self.colors['primary']}; color: white; padding: 20px; }}
                .section {{ margin: 20px 0; padding: 15px; border-left: 4px solid {self.colors['accent']}; }}
                .metric {{ display: inline-block; margin: 10px; padding: 10px; background-color: #f5f5f5; }}
                iframe {{ width: 100%; height: 600px; border: none; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>GNN工作流调度器分析报告</h1>
                <p>生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <h2>工作流概览</h2>
                <div class="metric">工作流类型: {workflow_data.get('type', 'Unknown')}</div>
                <div class="metric">任务数量: {len(workflow_data.get('dag', {}).get('nodes', []))}</div>
                <div class="metric">节点数量: {len(workflow_data.get('nodes', []))}</div>
            </div>
            
            <div class="section">
                <h2>交互式DAG可视化</h2>
                <iframe src="interactive_dag.html"></iframe>
            </div>
            
            <div class="section">
                <h2>训练监控</h2>
                <iframe src="training_dashboard.html"></iframe>
            </div>
            
            <div class="section">
                <h2>资源利用率分析</h2>
                <iframe src="resource_utilization_heatmap.html"></iframe>
            </div>
            
            <div class="section">
                <h2>层分析</h2>
                <iframe src="layer_analysis_dashboard.html"></iframe>
            </div>
        </body>
        </html>
        """
        
        # 生成各个组件
        self.create_interactive_dag_visualization(workflow_data, assignment_probs)
        self.create_training_dashboard(train_history)
        self.create_resource_utilization_heatmap(
            torch.tensor(workflow_data['node_features']), assignment_probs
        )
        self.create_layer_analysis_dashboard(debug_info)
        
        # 保存报告
        report_path = self.output_dir / 'comprehensive_report.html'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_html)
        
        return str(report_path)
