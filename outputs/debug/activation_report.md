# �㼶����ֵ��������

## dag_transformer.input_projection
- ��״: [1, 13, 256]
- ��ֵ: -0.341383
- ��׼��: 11.594954
- ��Χ: [-43.282917, 45.423946]
- ��ֵ����: 0.000
- ��ֵ����: 0.517

## dag_transformer.positional_encoding.topo_embedding
- ��״: [1, 13, 64]
- ��ֵ: -0.041986
- ��׼��: 1.008309
- ��Χ: [-2.968311, 2.980642]
- ��ֵ����: 0.000
- ��ֵ����: 0.499

## dag_transformer.positional_encoding.depth_embedding
- ��״: [1, 13, 64]
- ��ֵ: -0.014262
- ��׼��: 0.932583
- ��Χ: [-2.678739, 3.177636]
- ��ֵ����: 0.000
- ��ֵ����: 0.472

## dag_transformer.positional_encoding.critical_path_embedding
- ��״: [1, 13, 64]
- ��ֵ: -0.000634
- ��׼��: 1.037335
- ��Χ: [-2.956416, 4.349566]
- ��ֵ����: 0.000
- ��ֵ����: 0.494

## dag_transformer.positional_encoding.dependency_embedding
- ��״: [1, 13, 64]
- ��ֵ: 0.053512
- ��׼��: 0.902269
- ��Χ: [-2.702425, 2.770936]
- ��ֵ����: 0.000
- ��ֵ����: 0.456

## dag_transformer.dropout
- ��״: [1, 13, 256]
- ��ֵ: 0.123305
- ��׼��: 11.602142
- ��Χ: [-42.059597, 44.465439]
- ��ֵ����: 0.000
- ��ֵ����: 0.496

## dag_transformer.transformer_layers.0.self_attention.w_q
- ��״: [1, 13, 256]
- ��ֵ: -0.258870
- ��׼��: 6.487793
- ��Χ: [-31.512663, 34.313549]
- ��ֵ����: 0.000
- ��ֵ����: 0.509

## dag_transformer.transformer_layers.0.self_attention.w_k
- ��״: [1, 13, 256]
- ��ֵ: 0.392681
- ��׼��: 6.381444
- ��Χ: [-25.399130, 31.552027]
- ��ֵ����: 0.000
- ��ֵ����: 0.485

## dag_transformer.transformer_layers.0.self_attention.w_v
- ��״: [1, 13, 256]
- ��ֵ: 0.043147
- ��׼��: 6.779699
- ��Χ: [-31.917803, 31.317741]
- ��ֵ����: 0.000
- ��ֵ����: 0.492

## dag_transformer.transformer_layers.0.self_attention.dropout
- ��״: [1, 8, 13, 13]
- ��ֵ: 0.076923
- ��׼��: 0.253430
- ��Χ: [0.000000, 1.000000]
- ��ֵ����: 0.048
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.0.self_attention.w_o
- ��״: [1, 13, 256]
- ��ֵ: -0.287135
- ��׼��: 4.662826
- ��Χ: [-15.484603, 16.173475]
- ��ֵ����: 0.000
- ��ֵ����: 0.514

## dag_transformer.transformer_layers.0.dropout
- ��״: [1, 13, 256]
- ��ֵ: 0.005127
- ��׼��: 0.237425
- ��Χ: [-0.724627, 0.765820]
- ��ֵ����: 0.000
- ��ֵ����: 0.505

## dag_transformer.transformer_layers.0.norm1
- ��״: [1, 13, 256]
- ��ֵ: -0.000000
- ��׼��: 1.000150
- ��Χ: [-2.883662, 2.992051]
- ��ֵ����: 0.000
- ��ֵ����: 0.496

## dag_transformer.transformer_layers.0.feed_forward.0
- ��״: [1, 13, 1024]
- ��ֵ: 0.018911
- ��׼��: 0.591133
- ��Χ: [-2.009857, 2.139704]
- ��ֵ����: 0.000
- ��ֵ����: 0.494

## dag_transformer.transformer_layers.0.feed_forward.1
- ��״: [1, 13, 1024]
- ��ֵ: 0.245804
- ��׼��: 0.360542
- ��Χ: [0.000000, 2.139704]
- ��ֵ����: 0.494
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.0.feed_forward.2
- ��״: [1, 13, 1024]
- ��ֵ: 0.245804
- ��׼��: 0.360542
- ��Χ: [0.000000, 2.139704]
- ��ֵ����: 0.494
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.0.feed_forward.3
- ��״: [1, 13, 256]
- ��ֵ: 0.005127
- ��׼��: 0.237425
- ��Χ: [-0.724627, 0.765820]
- ��ֵ����: 0.000
- ��ֵ����: 0.505

## dag_transformer.transformer_layers.0.norm2
- ��״: [1, 13, 256]
- ��ֵ: 0.000000
- ��׼��: 1.000145
- ��Χ: [-2.976180, 2.809822]
- ��ֵ����: 0.000
- ��ֵ����: 0.497

## dag_transformer.transformer_layers.1.self_attention.w_q
- ��״: [1, 13, 256]
- ��ֵ: 0.019620
- ��׼��: 0.539026
- ��Χ: [-1.680581, 1.654974]
- ��ֵ����: 0.000
- ��ֵ����: 0.487

## dag_transformer.transformer_layers.1.self_attention.w_k
- ��״: [1, 13, 256]
- ��ֵ: 0.044668
- ��׼��: 0.588190
- ��Χ: [-1.826590, 2.193218]
- ��ֵ����: 0.000
- ��ֵ����: 0.471

## dag_transformer.transformer_layers.1.self_attention.w_v
- ��״: [1, 13, 256]
- ��ֵ: 0.037137
- ��׼��: 0.578167
- ��Χ: [-1.797884, 1.774609]
- ��ֵ����: 0.000
- ��ֵ����: 0.476

## dag_transformer.transformer_layers.1.self_attention.dropout
- ��״: [1, 8, 13, 13]
- ��ֵ: 0.076923
- ��׼��: 0.206535
- ��Χ: [0.000000, 0.999979]
- ��ֵ����: 0.000
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.1.self_attention.w_o
- ��״: [1, 13, 256]
- ��ֵ: -0.011438
- ��׼��: 0.316889
- ��Χ: [-0.934536, 1.487611]
- ��ֵ����: 0.000
- ��ֵ����: 0.516

## dag_transformer.transformer_layers.1.dropout
- ��״: [1, 13, 256]
- ��ֵ: -0.007949
- ��׼��: 0.246852
- ��Χ: [-1.023875, 0.817626]
- ��ֵ����: 0.000
- ��ֵ����: 0.493

## dag_transformer.transformer_layers.1.norm1
- ��״: [1, 13, 256]
- ��ֵ: 0.000000
- ��׼��: 1.000146
- ��Χ: [-2.870378, 3.538647]
- ��ֵ����: 0.000
- ��ֵ����: 0.504

## dag_transformer.transformer_layers.1.feed_forward.0
- ��״: [1, 13, 1024]
- ��ֵ: -0.002039
- ��׼��: 0.576448
- ��Χ: [-2.140737, 1.952311]
- ��ֵ����: 0.000
- ��ֵ����: 0.509

## dag_transformer.transformer_layers.1.feed_forward.1
- ��״: [1, 13, 1024]
- ��ֵ: 0.229654
- ��׼��: 0.338027
- ��Χ: [0.000000, 1.952311]
- ��ֵ����: 0.509
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.1.feed_forward.2
- ��״: [1, 13, 1024]
- ��ֵ: 0.229654
- ��׼��: 0.338027
- ��Χ: [0.000000, 1.952311]
- ��ֵ����: 0.509
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.1.feed_forward.3
- ��״: [1, 13, 256]
- ��ֵ: -0.007949
- ��׼��: 0.246852
- ��Χ: [-1.023875, 0.817626]
- ��ֵ����: 0.000
- ��ֵ����: 0.493

## dag_transformer.transformer_layers.1.norm2
- ��״: [1, 13, 256]
- ��ֵ: 0.000000
- ��׼��: 1.000146
- ��Χ: [-3.130075, 3.610958]
- ��ֵ����: 0.000
- ��ֵ����: 0.502

## dag_transformer.transformer_layers.2.self_attention.w_q
- ��״: [1, 13, 256]
- ��ֵ: -0.007570
- ��׼��: 0.562260
- ��Χ: [-1.939100, 1.880152]
- ��ֵ����: 0.000
- ��ֵ����: 0.498

## dag_transformer.transformer_layers.2.self_attention.w_k
- ��״: [1, 13, 256]
- ��ֵ: -0.041832
- ��׼��: 0.596062
- ��Χ: [-1.924339, 1.953141]
- ��ֵ����: 0.000
- ��ֵ����: 0.533

## dag_transformer.transformer_layers.2.self_attention.w_v
- ��״: [1, 13, 256]
- ��ֵ: 0.031281
- ��׼��: 0.561322
- ��Χ: [-1.795413, 1.826639]
- ��ֵ����: 0.000
- ��ֵ����: 0.487

## dag_transformer.transformer_layers.2.self_attention.dropout
- ��״: [1, 8, 13, 13]
- ��ֵ: 0.076923
- ��׼��: 0.206415
- ��Χ: [0.000000, 0.999982]
- ��ֵ����: 0.000
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.2.self_attention.w_o
- ��״: [1, 13, 256]
- ��ֵ: 0.008961
- ��׼��: 0.314603
- ��Χ: [-1.106229, 1.020950]
- ��ֵ����: 0.000
- ��ֵ����: 0.514

## dag_transformer.transformer_layers.2.dropout
- ��״: [1, 13, 256]
- ��ֵ: 0.000065
- ��׼��: 0.224163
- ��Χ: [-0.781744, 0.686232]
- ��ֵ����: 0.000
- ��ֵ����: 0.477

## dag_transformer.transformer_layers.2.norm1
- ��״: [1, 13, 256]
- ��ֵ: -0.000000
- ��׼��: 1.000146
- ��Χ: [-3.082325, 3.380390]
- ��ֵ����: 0.000
- ��ֵ����: 0.502

## dag_transformer.transformer_layers.2.feed_forward.0
- ��״: [1, 13, 1024]
- ��ֵ: -0.015304
- ��׼��: 0.580804
- ��Χ: [-2.286156, 2.016079]
- ��ֵ����: 0.000
- ��ֵ����: 0.500

## dag_transformer.transformer_layers.2.feed_forward.1
- ��״: [1, 13, 1024]
- ��ֵ: 0.224007
- ��׼��: 0.328358
- ��Χ: [0.000000, 2.016079]
- ��ֵ����: 0.500
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.2.feed_forward.2
- ��״: [1, 13, 1024]
- ��ֵ: 0.224007
- ��׼��: 0.328358
- ��Χ: [0.000000, 2.016079]
- ��ֵ����: 0.500
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.2.feed_forward.3
- ��״: [1, 13, 256]
- ��ֵ: 0.000065
- ��׼��: 0.224163
- ��Χ: [-0.781744, 0.686232]
- ��ֵ����: 0.000
- ��ֵ����: 0.477

## dag_transformer.transformer_layers.2.norm2
- ��״: [1, 13, 256]
- ��ֵ: 0.000000
- ��׼��: 1.000145
- ��Χ: [-3.172660, 3.495747]
- ��ֵ����: 0.000
- ��ֵ����: 0.500

## dag_transformer.transformer_layers.3.self_attention.w_q
- ��״: [1, 13, 256]
- ��ֵ: -0.001005
- ��׼��: 0.578416
- ��Χ: [-1.917060, 2.010469]
- ��ֵ����: 0.000
- ��ֵ����: 0.498

## dag_transformer.transformer_layers.3.self_attention.w_k
- ��״: [1, 13, 256]
- ��ֵ: -0.037106
- ��׼��: 0.544830
- ��Χ: [-2.197100, 1.438525]
- ��ֵ����: 0.000
- ��ֵ����: 0.530

## dag_transformer.transformer_layers.3.self_attention.w_v
- ��״: [1, 13, 256]
- ��ֵ: 0.017943
- ��׼��: 0.572871
- ��Χ: [-1.795332, 1.746260]
- ��ֵ����: 0.000
- ��ֵ����: 0.465

## dag_transformer.transformer_layers.3.self_attention.dropout
- ��״: [1, 8, 13, 13]
- ��ֵ: 0.076923
- ��׼��: 0.206366
- ��Χ: [0.000000, 0.999979]
- ��ֵ����: 0.000
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.3.self_attention.w_o
- ��״: [1, 13, 256]
- ��ֵ: 0.070369
- ��׼��: 0.332705
- ��Χ: [-0.984729, 1.130119]
- ��ֵ����: 0.000
- ��ֵ����: 0.434

## dag_transformer.transformer_layers.3.dropout
- ��״: [1, 13, 256]
- ��ֵ: 0.011816
- ��׼��: 0.240789
- ��Χ: [-0.790476, 0.740840]
- ��ֵ����: 0.000
- ��ֵ����: 0.484

## dag_transformer.transformer_layers.3.norm1
- ��״: [1, 13, 256]
- ��ֵ: 0.000000
- ��׼��: 1.000146
- ��Χ: [-3.147003, 3.083953]
- ��ֵ����: 0.000
- ��ֵ����: 0.496

## dag_transformer.transformer_layers.3.feed_forward.0
- ��״: [1, 13, 1024]
- ��ֵ: -0.000141
- ��׼��: 0.578517
- ��Χ: [-2.457055, 1.892048]
- ��ֵ����: 0.000
- ��ֵ����: 0.505

## dag_transformer.transformer_layers.3.feed_forward.1
- ��״: [1, 13, 1024]
- ��ֵ: 0.229331
- ��׼��: 0.336958
- ��Χ: [0.000000, 1.892048]
- ��ֵ����: 0.505
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.3.feed_forward.2
- ��״: [1, 13, 1024]
- ��ֵ: 0.229331
- ��׼��: 0.336958
- ��Χ: [0.000000, 1.892048]
- ��ֵ����: 0.505
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.3.feed_forward.3
- ��״: [1, 13, 256]
- ��ֵ: 0.011816
- ��׼��: 0.240789
- ��Χ: [-0.790476, 0.740840]
- ��ֵ����: 0.000
- ��ֵ����: 0.484

## dag_transformer.transformer_layers.3.norm2
- ��״: [1, 13, 256]
- ��ֵ: 0.000000
- ��׼��: 1.000146
- ��Χ: [-3.314753, 3.313431]
- ��ֵ����: 0.000
- ��ֵ����: 0.495

## dag_transformer.transformer_layers.4.self_attention.w_q
- ��״: [1, 13, 256]
- ��ֵ: -0.041955
- ��׼��: 0.598317
- ��Χ: [-1.818128, 2.227568]
- ��ֵ����: 0.000
- ��ֵ����: 0.534

## dag_transformer.transformer_layers.4.self_attention.w_k
- ��״: [1, 13, 256]
- ��ֵ: -0.000835
- ��׼��: 0.614530
- ��Χ: [-2.095240, 2.151142]
- ��ֵ����: 0.000
- ��ֵ����: 0.529

## dag_transformer.transformer_layers.4.self_attention.w_v
- ��״: [1, 13, 256]
- ��ֵ: 0.005402
- ��׼��: 0.552735
- ��Χ: [-1.614846, 1.881270]
- ��ֵ����: 0.000
- ��ֵ����: 0.510

## dag_transformer.transformer_layers.4.self_attention.dropout
- ��״: [1, 8, 13, 13]
- ��ֵ: 0.076923
- ��׼��: 0.206479
- ��Χ: [0.000000, 0.999985]
- ��ֵ����: 0.000
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.4.self_attention.w_o
- ��״: [1, 13, 256]
- ��ֵ: -0.008622
- ��׼��: 0.313644
- ��Χ: [-1.107137, 0.872470]
- ��ֵ����: 0.000
- ��ֵ����: 0.517

## dag_transformer.transformer_layers.4.dropout
- ��״: [1, 13, 256]
- ��ֵ: -0.007088
- ��׼��: 0.219483
- ��Χ: [-0.685407, 0.782675]
- ��ֵ����: 0.000
- ��ֵ����: 0.526

## dag_transformer.transformer_layers.4.norm1
- ��״: [1, 13, 256]
- ��ֵ: 0.000000
- ��׼��: 1.000146
- ��Χ: [-3.131589, 3.178431]
- ��ֵ����: 0.000
- ��ֵ����: 0.497

## dag_transformer.transformer_layers.4.feed_forward.0
- ��״: [1, 13, 1024]
- ��ֵ: -0.015424
- ��׼��: 0.581509
- ��Χ: [-1.985712, 2.169360]
- ��ֵ����: 0.000
- ��ֵ����: 0.514

## dag_transformer.transformer_layers.4.feed_forward.1
- ��״: [1, 13, 1024]
- ��ֵ: 0.224553
- ��׼��: 0.338010
- ��Χ: [0.000000, 2.169360]
- ��ֵ����: 0.514
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.4.feed_forward.2
- ��״: [1, 13, 1024]
- ��ֵ: 0.224553
- ��׼��: 0.338010
- ��Χ: [0.000000, 2.169360]
- ��ֵ����: 0.514
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.4.feed_forward.3
- ��״: [1, 13, 256]
- ��ֵ: -0.007088
- ��׼��: 0.219483
- ��Χ: [-0.685407, 0.782675]
- ��ֵ����: 0.000
- ��ֵ����: 0.526

## dag_transformer.transformer_layers.4.norm2
- ��״: [1, 13, 256]
- ��ֵ: 0.000000
- ��׼��: 1.000146
- ��Χ: [-3.024355, 3.506982]
- ��ֵ����: 0.000
- ��ֵ����: 0.498

## dag_transformer.transformer_layers.5.self_attention.w_q
- ��״: [1, 13, 256]
- ��ֵ: -0.055205
- ��׼��: 0.614707
- ��Χ: [-2.202350, 1.888900]
- ��ֵ����: 0.000
- ��ֵ����: 0.515

## dag_transformer.transformer_layers.5.self_attention.w_k
- ��״: [1, 13, 256]
- ��ֵ: 0.049645
- ��׼��: 0.545018
- ��Χ: [-1.494692, 1.909992]
- ��ֵ����: 0.000
- ��ֵ����: 0.460

## dag_transformer.transformer_layers.5.self_attention.w_v
- ��״: [1, 13, 256]
- ��ֵ: 0.034699
- ��׼��: 0.594400
- ��Χ: [-1.843366, 2.030018]
- ��ֵ����: 0.000
- ��ֵ����: 0.481

## dag_transformer.transformer_layers.5.self_attention.dropout
- ��״: [1, 8, 13, 13]
- ��ֵ: 0.076923
- ��׼��: 0.206605
- ��Χ: [0.000001, 0.999984]
- ��ֵ����: 0.000
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.5.self_attention.w_o
- ��״: [1, 13, 256]
- ��ֵ: -0.007979
- ��׼��: 0.320964
- ��Χ: [-1.110785, 0.874957]
- ��ֵ����: 0.000
- ��ֵ����: 0.502

## dag_transformer.transformer_layers.5.dropout
- ��״: [1, 13, 256]
- ��ֵ: -0.005494
- ��׼��: 0.234387
- ��Χ: [-0.642365, 0.708330]
- ��ֵ����: 0.000
- ��ֵ����: 0.522

## dag_transformer.transformer_layers.5.norm1
- ��״: [1, 13, 256]
- ��ֵ: 0.000000
- ��׼��: 1.000146
- ��Χ: [-2.923696, 3.488524]
- ��ֵ����: 0.000
- ��ֵ����: 0.493

## dag_transformer.transformer_layers.5.feed_forward.0
- ��״: [1, 13, 1024]
- ��ֵ: -0.009850
- ��׼��: 0.571934
- ��Χ: [-1.939038, 2.163356]
- ��ֵ����: 0.000
- ��ֵ����: 0.512

## dag_transformer.transformer_layers.5.feed_forward.1
- ��״: [1, 13, 1024]
- ��ֵ: 0.221709
- ��׼��: 0.335213
- ��Χ: [0.000000, 2.163356]
- ��ֵ����: 0.512
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.5.feed_forward.2
- ��״: [1, 13, 1024]
- ��ֵ: 0.221709
- ��׼��: 0.335213
- ��Χ: [0.000000, 2.163356]
- ��ֵ����: 0.512
- ��ֵ����: 0.000

## dag_transformer.transformer_layers.5.feed_forward.3
- ��״: [1, 13, 256]
- ��ֵ: -0.005494
- ��׼��: 0.234387
- ��Χ: [-0.642365, 0.708330]
- ��ֵ����: 0.000
- ��ֵ����: 0.522

## dag_transformer.transformer_layers.5.norm2
- ��״: [1, 13, 256]
- ��ֵ: 0.000000
- ��׼��: 1.000146
- ��Χ: [-2.990312, 3.345234]
- ��ֵ����: 0.000
- ��ֵ����: 0.498

## dag_transformer.output_projection
- ��״: [1, 13, 256]
- ��ֵ: 0.019947
- ��׼��: 0.537976
- ��Χ: [-1.863573, 1.513922]
- ��ֵ����: 0.000
- ��ֵ����: 0.478

## pinn_constraint_layer.constraint_embedding.dependency_encoder.0
- ��״: [1, 13, 256]
- ��ֵ: 0.013111
- ��׼��: 0.572269
- ��Χ: [-2.004456, 2.007536]
- ��ֵ����: 0.000
- ��ֵ����: 0.492

## pinn_constraint_layer.constraint_embedding.dependency_encoder.1
- ��״: [1, 13, 256]
- ��ֵ: 0.235422
- ��׼��: 0.338613
- ��Χ: [0.000000, 2.007536]
- ��ֵ����: 0.492
- ��ֵ����: 0.000

## pinn_constraint_layer.constraint_embedding.dependency_encoder.2
- ��״: [1, 13, 256]
- ��ֵ: 0.005570
- ��׼��: 0.234928
- ��Χ: [-0.788689, 0.812882]
- ��ֵ����: 0.000
- ��ֵ����: 0.495

## pinn_constraint_layer.constraint_embedding.resource_encoder.0
- ��״: [1, 15, 256]
- ��ֵ: -0.007705
- ��׼��: 0.596529
- ��Χ: [-2.076796, 2.223016]
- ��ֵ����: 0.000
- ��ֵ����: 0.516

## pinn_constraint_layer.constraint_embedding.resource_encoder.1
- ��״: [1, 15, 256]
- ��ֵ: 0.235212
- ��׼��: 0.350349
- ��Χ: [0.000000, 2.223016]
- ��ֵ����: 0.516
- ��ֵ����: 0.000

## pinn_constraint_layer.constraint_embedding.resource_encoder.2
- ��״: [1, 15, 256]
- ��ֵ: -0.002999
- ��׼��: 0.240169
- ��Χ: [-0.959101, 0.845287]
- ��ֵ����: 0.000
- ��ֵ����: 0.512

## pinn_constraint_layer.constraint_aware_transform.0
- ��״: [1, 13, 512]
- ��ֵ: 0.000607
- ��׼��: 0.043066
- ��Χ: [-0.108140, 0.111332]
- ��ֵ����: 0.000
- ��ֵ����: 0.475

## pinn_constraint_layer.constraint_aware_transform.1
- ��״: [1, 13, 512]
- ��ֵ: 0.018168
- ��׼��: 0.023943
- ��Χ: [0.000000, 0.111332]
- ��ֵ����: 0.475
- ��ֵ����: 0.000

## pinn_constraint_layer.constraint_aware_transform.2
- ��״: [1, 13, 512]
- ��ֵ: 0.018168
- ��׼��: 0.023943
- ��Χ: [0.000000, 0.111332]
- ��ֵ����: 0.475
- ��ֵ����: 0.000

## pinn_constraint_layer.constraint_aware_transform.3
- ��״: [1, 13, 256]
- ��ֵ: -0.000215
- ��׼��: 0.030167
- ��Χ: [-0.072139, 0.085539]
- ��ֵ����: 0.000
- ��ֵ����: 0.483

## pinn_constraint_layer.constraint_aware_transform.4
- ��״: [1, 13, 256]
- ��ֵ: -0.000000
- ��׼��: 0.994699
- ��Χ: [-2.371455, 2.828140]
- ��ֵ����: 0.000
- ��ֵ����: 0.475

## pinn_constraint_layer.constraint_validator.0
- ��״: [1, 13, 256]
- ��ֵ: -0.021323
- ��׼��: 0.597734
- ��Χ: [-1.401561, 1.414173]
- ��ֵ����: 0.000
- ��ֵ����: 0.503

## pinn_constraint_layer.constraint_validator.1
- ��״: [1, 13, 256]
- ��ֵ: 0.235680
- ��׼��: 0.333977
- ��Χ: [0.000000, 1.414173]
- ��ֵ����: 0.503
- ��ֵ����: 0.000

## pinn_constraint_layer.constraint_validator.2
- ��״: [1, 13, 1]
- ��ֵ: -0.251954
- ��׼��: 0.000488
- ��Χ: [-0.252881, -0.251129]
- ��ֵ����: 0.000
- ��ֵ����: 1.000

## pinn_constraint_layer.constraint_validator.3
- ��״: [1, 13, 1]
- ��ֵ: 0.437343
- ��׼��: 0.000120
- ��Χ: [0.437115, 0.437546]
- ��ֵ����: 0.000
- ��ֵ����: 0.000

## layer_fusion.0.0
- ��״: [1, 13, 256]
- ��ֵ: 0.009337
- ��׼��: 0.301108
- ��Χ: [-0.905543, 1.180633]
- ��ֵ����: 0.000
- ��ֵ����: 0.502

## layer_fusion.0.1
- ��״: [1, 13, 256]
- ��ֵ: 0.123921
- ��׼��: 0.188758
- ��Χ: [0.000000, 1.180633]
- ��ֵ����: 0.502
- ��ֵ����: 0.000

## layer_fusion.0.2
- ��״: [1, 13, 256]
- ��ֵ: 0.123921
- ��׼��: 0.188758
- ��Χ: [0.000000, 1.180633]
- ��ֵ����: 0.502
- ��ֵ����: 0.000

## layer_fusion.0.3
- ��״: [1, 13, 256]
- ��ֵ: -0.000000
- ��׼��: 1.000009
- ��Χ: [-0.683257, 5.238470]
- ��ֵ����: 0.000
- ��ֵ����: 0.667

## gat_scheduler.task_input_projection
- ��״: [13, 256]
- ��ֵ: -0.017045
- ��׼��: 0.600903
- ��Χ: [-1.834497, 1.998566]
- ��ֵ����: 0.000
- ��ֵ����: 0.505

## gat_scheduler.node_input_projection
- ��״: [15, 256]
- ��ֵ: 24.834410
- ��׼��: 513.341431
- ��Χ: [-2499.477783, 2584.365967]
- ��ֵ����: 0.000
- ��ֵ����: 0.470

## gat_scheduler.gat_layers.0.task_task_attention.lin_src
- ��״: [13, 256]
- ��ֵ: -0.037011
- ��׼��: 0.640952
- ��Χ: [-2.069383, 1.575317]
- ��ֵ����: 0.000
- ��ֵ����: 0.525

## gat_scheduler.gat_layers.0.task_task_attention.aggr_module
- ��״: [13, 8, 32]
- ��ֵ: -0.004967
- ��׼��: 0.253020
- ��Χ: [-1.761573, 1.575317]
- ��ֵ����: 0.846
- ��ֵ����: 0.081

## gat_scheduler.gat_layers.0.node_feature_transform.0
- ��״: [15, 256]
- ��ֵ: 10.035728
- ��׼��: 275.641510
- ��Χ: [-2081.732666, 2187.675537]
- ��ֵ����: 0.000
- ��ֵ����: 0.467

## gat_scheduler.gat_layers.0.node_feature_transform.1
- ��״: [15, 256]
- ��ֵ: 89.568817
- ��׼��: 179.081177
- ��Χ: [0.000000, 2187.675537]
- ��ֵ����: 0.467
- ��ֵ����: 0.000

## gat_scheduler.gat_layers.0.node_feature_transform.2
- ��״: [15, 256]
- ��ֵ: 89.568817
- ��׼��: 179.081177
- ��Χ: [0.000000, 2187.675537]
- ��ֵ����: 0.467
- ��ֵ����: 0.000

## gat_scheduler.gat_layers.0.node_feature_transform.3
- ��״: [15, 256]
- ��ֵ: -0.000000
- ��׼��: 1.000130
- ��Χ: [-0.742392, 4.908022]
- ��ֵ����: 0.000
- ��ֵ����: 0.645

## gat_scheduler.gat_layers.0.compatibility_scorer.0
- ��״: [256]
- ��ֵ: -0.003022
- ��׼��: 0.418182
- ��Χ: [-1.148061, 0.988042]
- ��ֵ����: 0.000
- ��ֵ����: 0.500

## gat_scheduler.gat_layers.0.compatibility_scorer.1
- ��״: [256]
- ��ֵ: 0.165160
- ��׼��: 0.239859
- ��Χ: [0.000000, 0.988042]
- ��ֵ����: 0.500
- ��ֵ����: 0.000

## gat_scheduler.gat_layers.0.compatibility_scorer.2
- ��״: [1]
- ��ֵ: -0.037301
- ��׼��: nan
- ��Χ: [-0.037301, -0.037301]
- ��ֵ����: 0.000
- ��ֵ����: 1.000

## gat_scheduler.gat_layers.1.task_task_attention.lin_src
- ��״: [13, 256]
- ��ֵ: 0.001670
- ��׼��: 0.261042
- ��Χ: [-1.727482, 2.063559]
- ��ֵ����: 0.846
- ��ֵ����: 0.078

## gat_scheduler.gat_layers.1.task_task_attention.aggr_module
- ��״: [13, 8, 32]
- ��ֵ: 0.001000
- ��׼��: 0.183841
- ��Χ: [-1.727482, 2.063559]
- ��ֵ����: 0.923
- ��ֵ����: 0.039

## gat_scheduler.gat_layers.1.node_feature_transform.0
- ��״: [15, 256]
- ��ֵ: -1.181340
- ��׼��: 282.122559
- ��Χ: [-2070.685303, 2020.694214]
- ��ֵ����: 0.000
- ��ֵ����: 0.485

## gat_scheduler.gat_layers.1.node_feature_transform.1
- ��״: [15, 256]
- ��ֵ: 87.614738
- ��׼��: 173.106171
- ��Χ: [0.000000, 2020.694214]
- ��ֵ����: 0.485
- ��ֵ����: 0.000

## gat_scheduler.gat_layers.1.node_feature_transform.2
- ��״: [15, 256]
- ��ֵ: 87.614738
- ��׼��: 173.106171
- ��Χ: [0.000000, 2020.694214]
- ��ֵ����: 0.485
- ��ֵ����: 0.000

## gat_scheduler.gat_layers.1.node_feature_transform.3
- ��״: [15, 256]
- ��ֵ: 0.000000
- ��׼��: 1.000130
- ��Χ: [-0.735118, 4.396846]
- ��ֵ����: 0.000
- ��ֵ����: 0.640

## gat_scheduler.gat_layers.1.compatibility_scorer.0
- ��״: [256]
- ��ֵ: 0.036391
- ��׼��: 0.405783
- ��Χ: [-1.105155, 1.042976]
- ��ֵ����: 0.000
- ��ֵ����: 0.453

## gat_scheduler.gat_layers.1.compatibility_scorer.1
- ��״: [256]
- ��ֵ: 0.180749
- ��׼��: 0.239701
- ��Χ: [0.000000, 1.042976]
- ��ֵ����: 0.453
- ��ֵ����: 0.000

## gat_scheduler.gat_layers.1.compatibility_scorer.2
- ��״: [1]
- ��ֵ: 0.012071
- ��׼��: nan
- ��Χ: [0.012071, 0.012071]
- ��ֵ����: 0.000
- ��ֵ����: 0.000

## gat_scheduler.gat_layers.2.task_task_attention.lin_src
- ��״: [13, 256]
- ��ֵ: -0.002249
- ��׼��: 0.173533
- ��Χ: [-1.936573, 1.852876]
- ��ֵ����: 0.923
- ��ֵ����: 0.041

## gat_scheduler.gat_layers.2.task_task_attention.aggr_module
- ��״: [13, 8, 32]
- ��ֵ: 0.000000
- ��׼��: 0.000000
- ��Χ: [0.000000, 0.000000]
- ��ֵ����: 1.000
- ��ֵ����: 0.000

## gat_scheduler.gat_layers.2.node_feature_transform.0
- ��״: [15, 256]
- ��ֵ: 10.053816
- ��׼��: 292.476227
- ��Χ: [-2083.460938, 2310.448730]
- ��ֵ����: 0.000
- ��ֵ����: 0.484

## gat_scheduler.gat_layers.2.node_feature_transform.1
- ��״: [15, 256]
- ��ֵ: 94.533356
- ��׼��: 191.190033
- ��Χ: [0.000000, 2310.448730]
- ��ֵ����: 0.484
- ��ֵ����: 0.000

## gat_scheduler.gat_layers.2.node_feature_transform.2
- ��״: [15, 256]
- ��ֵ: 94.533356
- ��׼��: 191.190033
- ��Χ: [0.000000, 2310.448730]
- ��ֵ����: 0.484
- ��ֵ����: 0.000

## gat_scheduler.gat_layers.2.node_feature_transform.3
- ��״: [15, 256]
- ��ֵ: 0.000000
- ��׼��: 1.000130
- ��Χ: [-0.705476, 4.242801]
- ��ֵ����: 0.000
- ��ֵ����: 0.661

## gat_scheduler.gat_layers.2.compatibility_scorer.0
- ��״: [256]
- ��ֵ: 0.038931
- ��׼��: 0.386938
- ��Χ: [-1.357776, 1.013075]
- ��ֵ����: 0.000
- ��ֵ����: 0.496

## gat_scheduler.gat_layers.2.compatibility_scorer.1
- ��״: [256]
- ��ֵ: 0.172784
- ��׼��: 0.242158
- ��Χ: [0.000000, 1.013075]
- ��ֵ����: 0.496
- ��ֵ����: 0.000

## gat_scheduler.gat_layers.2.compatibility_scorer.2
- ��״: [1]
- ��ֵ: 0.119643
- ��׼��: nan
- ��Χ: [0.119643, 0.119643]
- ��ֵ����: 0.000
- ��ֵ����: 0.000

## gat_scheduler.task_node_matcher.resource_attention.w_q
- ��״: [1, 13, 256]
- ��ֵ: -0.000989
- ��׼��: 0.037552
- ��Χ: [-0.062321, 0.061792]
- ��ֵ����: 0.000
- ��ֵ����: 0.496

## gat_scheduler.task_node_matcher.resource_attention.w_k
- ��״: [1, 15, 256]
- ��ֵ: -1.289635
- ��׼��: 280.405090
- ��Χ: [-1792.462769, 2015.019409]
- ��ֵ����: 0.000
- ��ֵ����: 0.500

## gat_scheduler.task_node_matcher.resource_attention.w_v
- ��״: [1, 15, 256]
- ��ֵ: -2.948405
- ��׼��: 304.647064
- ��Χ: [-1987.414307, 2245.046143]
- ��ֵ����: 0.000
- ��ֵ����: 0.499

## gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.0
- ��״: [1, 15, 64]
- ��ֵ: 0.004469
- ��׼��: 0.620475
- ��Χ: [-1.918079, 1.864008]
- ��ֵ����: 0.000
- ��ֵ����: 0.486

## gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.1
- ��״: [1, 15, 64]
- ��ֵ: 0.247679
- ��׼��: 0.358775
- ��Χ: [0.000000, 1.864008]
- ��ֵ����: 0.486
- ��ֵ����: 0.000

## gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.2
- ��״: [1, 15, 128]
- ��ֵ: 0.035605
- ��׼��: 0.265552
- ��Χ: [-0.903056, 1.064395]
- ��ֵ����: 0.000
- ��ֵ����: 0.444

## gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.3
- ��״: [1, 15, 128]
- ��ֵ: 0.121813
- ��׼��: 0.167250
- ��Χ: [0.000000, 1.064395]
- ��ֵ����: 0.444
- ��ֵ����: 0.000

## gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.4
- ��״: [1, 15, 8]
- ��ֵ: 0.013249
- ��׼��: 0.085378
- ��Χ: [-0.243040, 0.257772]
- ��ֵ����: 0.000
- ��ֵ����: 0.433

## gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.5
- ��״: [1, 15, 8]
- ��ֵ: 0.125000
- ��׼��: 0.010309
- ��Χ: [0.097889, 0.149449]
- ��ֵ����: 0.000
- ��ֵ����: 0.000

## gat_scheduler.task_node_matcher.resource_attention.dropout
- ��״: [1, 13, 15]
- ��ֵ: 0.066667
- ��׼��: 0.016821
- ��Χ: [0.029439, 0.093996]
- ��ֵ����: 0.000
- ��ֵ����: 0.000

## gat_scheduler.task_node_matcher.resource_attention.output_linear
- ��״: [1, 13, 256]
- ��ֵ: -12.776796
- ��׼��: 114.937157
- ��Χ: [-356.364838, 255.822449]
- ��ֵ����: 0.000
- ��ֵ����: 0.535

## gat_scheduler.task_node_matcher.matching_network.0
- ��״: [1, 256]
- ��ֵ: -30.632702
- ��׼��: 253.232361
- ��Χ: [-752.175232, 612.888306]
- ��ֵ����: 0.000
- ��ֵ����: 0.527

## gat_scheduler.task_node_matcher.matching_network.1
- ��״: [1, 256]
- ��ֵ: 82.285271
- ��׼��: 133.363785
- ��Χ: [0.000000, 612.888306]
- ��ֵ����: 0.527
- ��ֵ����: 0.000

## gat_scheduler.task_node_matcher.matching_network.2
- ��״: [1, 256]
- ��ֵ: 82.285271
- ��׼��: 133.363785
- ��Χ: [0.000000, 612.888306]
- ��ֵ����: 0.527
- ��ֵ����: 0.000

## gat_scheduler.task_node_matcher.matching_network.3
- ��״: [1, 128]
- ��ֵ: 10.677790
- ��׼��: 89.503708
- ��Χ: [-220.718491, 217.683228]
- ��ֵ����: 0.000
- ��ֵ����: 0.453

## gat_scheduler.task_node_matcher.matching_network.4
- ��״: [1, 128]
- ��ֵ: 41.513134
- ��׼��: 54.375843
- ��Χ: [0.000000, 217.683228]
- ��ֵ����: 0.453
- ��ֵ����: 0.000

## gat_scheduler.task_node_matcher.matching_network.5
- ��״: [1, 1]
- ��ֵ: 85.407898
- ��׼��: nan
- ��Χ: [85.407898, 85.407898]
- ��ֵ����: 0.000
- ��ֵ����: 0.000
