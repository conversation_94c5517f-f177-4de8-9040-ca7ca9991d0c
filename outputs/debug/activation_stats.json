{"dag_transformer.input_projection": {"mean": -0.34138303995132446, "std": 11.594953536987305, "min": -43.28291702270508, "max": 45.423946380615234, "zero_fraction": 0.0, "negative_fraction": 0.5168269276618958, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.positional_encoding.topo_embedding": {"mean": -0.04198640584945679, "std": 1.0083087682724, "min": -2.968310832977295, "max": 2.980642080307007, "zero_fraction": 0.0, "negative_fraction": 0.49879807233810425, "shape": [1, 13, 64], "num_elements": 832}, "dag_transformer.positional_encoding.depth_embedding": {"mean": -0.014261736534535885, "std": 0.9325831532478333, "min": -2.678738594055176, "max": 3.177635908126831, "zero_fraction": 0.0, "negative_fraction": 0.47235578298568726, "shape": [1, 13, 64], "num_elements": 832}, "dag_transformer.positional_encoding.critical_path_embedding": {"mean": -0.0006341704865917563, "std": 1.03733491897583, "min": -2.956415891647339, "max": 4.349565505981445, "zero_fraction": 0.0, "negative_fraction": 0.49399039149284363, "shape": [1, 13, 64], "num_elements": 832}, "dag_transformer.positional_encoding.dependency_embedding": {"mean": 0.053511835634708405, "std": 0.902269184589386, "min": -2.7024245262145996, "max": 2.770935535430908, "zero_fraction": 0.0, "negative_fraction": 0.4555288553237915, "shape": [1, 13, 64], "num_elements": 832}, "dag_transformer.dropout": {"mean": 0.12330511957406998, "std": 11.602142333984375, "min": -42.05959701538086, "max": 44.46543884277344, "zero_fraction": 0.0, "negative_fraction": 0.49609375, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.self_attention.w_q": {"mean": -0.25887012481689453, "std": 6.48779296875, "min": -31.512662887573242, "max": 34.31354904174805, "zero_fraction": 0.0, "negative_fraction": 0.5093148946762085, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.self_attention.w_k": {"mean": 0.392680823802948, "std": 6.381443977355957, "min": -25.39912986755371, "max": 31.552026748657227, "zero_fraction": 0.0, "negative_fraction": 0.48467546701431274, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.self_attention.w_v": {"mean": 0.04314667358994484, "std": 6.779699325561523, "min": -31.917802810668945, "max": 31.31774139404297, "zero_fraction": 0.0, "negative_fraction": 0.4921875, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.self_attention.dropout": {"mean": 0.07692307978868484, "std": 0.25343021750450134, "min": 0.0, "max": 1.0, "zero_fraction": 0.048076923936605453, "negative_fraction": 0.0, "shape": [1, 8, 13, 13], "num_elements": 1352}, "dag_transformer.transformer_layers.0.self_attention.w_o": {"mean": -0.2871347963809967, "std": 4.662825584411621, "min": -15.484602928161621, "max": 16.17347526550293, "zero_fraction": 0.0, "negative_fraction": 0.5138221383094788, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.dropout": {"mean": 0.005126973148435354, "std": 0.23742468655109406, "min": -0.7246265411376953, "max": 0.765819787979126, "zero_fraction": 0.0, "negative_fraction": 0.5045071840286255, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.norm1": {"mean": -4.58497284583359e-09, "std": 1.000150203704834, "min": -2.883661985397339, "max": 2.992051124572754, "zero_fraction": 0.0, "negative_fraction": 0.49639421701431274, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.feed_forward.0": {"mean": 0.018911104649305344, "std": 0.591133177280426, "min": -2.009856700897217, "max": 2.1397042274475098, "zero_fraction": 0.0, "negative_fraction": 0.49399039149284363, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.0.feed_forward.1": {"mean": 0.24580353498458862, "std": 0.3605422079563141, "min": 0.0, "max": 2.1397042274475098, "zero_fraction": 0.49399039149284363, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.0.feed_forward.2": {"mean": 0.24580353498458862, "std": 0.3605422079563141, "min": 0.0, "max": 2.1397042274475098, "zero_fraction": 0.49399039149284363, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.0.feed_forward.3": {"mean": 0.005126973148435354, "std": 0.23742468655109406, "min": -0.7246265411376953, "max": 0.765819787979126, "zero_fraction": 0.0, "negative_fraction": 0.5045071840286255, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.norm2": {"mean": 5.7312159462696854e-09, "std": 1.000145435333252, "min": -2.976180076599121, "max": 2.809821605682373, "zero_fraction": 0.0, "negative_fraction": 0.4969951808452606, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.self_attention.w_q": {"mean": 0.019620098173618317, "std": 0.5390259623527527, "min": -1.6805812120437622, "max": 1.6549739837646484, "zero_fraction": 0.0, "negative_fraction": 0.4867788553237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.self_attention.w_k": {"mean": 0.04466843977570534, "std": 0.5881898403167725, "min": -1.8265902996063232, "max": 2.193218231201172, "zero_fraction": 0.0, "negative_fraction": 0.4711538553237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.self_attention.w_v": {"mean": 0.03713728114962578, "std": 0.5781666040420532, "min": -1.7978839874267578, "max": 1.774609088897705, "zero_fraction": 0.0, "negative_fraction": 0.4759615361690521, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.self_attention.dropout": {"mean": 0.07692307978868484, "std": 0.20653493702411652, "min": 4.4147964217700064e-07, "max": 0.9999786615371704, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 8, 13, 13], "num_elements": 1352}, "dag_transformer.transformer_layers.1.self_attention.w_o": {"mean": -0.011437765322625637, "std": 0.31688880920410156, "min": -0.9345358610153198, "max": 1.4876112937927246, "zero_fraction": 0.0, "negative_fraction": 0.515625, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.dropout": {"mean": -0.007948962971568108, "std": 0.24685227870941162, "min": -1.0238752365112305, "max": 0.8176260590553284, "zero_fraction": 0.0, "negative_fraction": 0.4927884638309479, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.norm1": {"mean": 0.0, "std": 1.000145673751831, "min": -2.870378017425537, "max": 3.538646697998047, "zero_fraction": 0.0, "negative_fraction": 0.5042067170143127, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.feed_forward.0": {"mean": -0.0020394830498844385, "std": 0.5764483213424683, "min": -2.1407370567321777, "max": 1.9523109197616577, "zero_fraction": 0.0, "negative_fraction": 0.5089393258094788, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.1.feed_forward.1": {"mean": 0.22965416312217712, "std": 0.3380270004272461, "min": 0.0, "max": 1.9523109197616577, "zero_fraction": 0.5089393258094788, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.1.feed_forward.2": {"mean": 0.22965416312217712, "std": 0.3380270004272461, "min": 0.0, "max": 1.9523109197616577, "zero_fraction": 0.5089393258094788, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.1.feed_forward.3": {"mean": -0.007948962971568108, "std": 0.24685227870941162, "min": -1.0238752365112305, "max": 0.8176260590553284, "zero_fraction": 0.0, "negative_fraction": 0.4927884638309479, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.norm2": {"mean": 2.292486422916795e-09, "std": 1.000145673751831, "min": -3.1300745010375977, "max": 3.6109580993652344, "zero_fraction": 0.0, "negative_fraction": 0.5024038553237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.self_attention.w_q": {"mean": -0.0075703882612288, "std": 0.5622601509094238, "min": -1.9391001462936401, "max": 1.8801521062850952, "zero_fraction": 0.0, "negative_fraction": 0.49819710850715637, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.self_attention.w_k": {"mean": -0.04183238744735718, "std": 0.5960615277290344, "min": -1.9243388175964355, "max": 1.9531408548355103, "zero_fraction": 0.0, "negative_fraction": 0.5333533883094788, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.self_attention.w_v": {"mean": 0.03128056600689888, "std": 0.5613223910331726, "min": -1.7954127788543701, "max": 1.8266386985778809, "zero_fraction": 0.0, "negative_fraction": 0.4873798191547394, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.self_attention.dropout": {"mean": 0.07692307978868484, "std": 0.20641480386257172, "min": 4.948959713146905e-07, "max": 0.9999816417694092, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 8, 13, 13], "num_elements": 1352}, "dag_transformer.transformer_layers.2.self_attention.w_o": {"mean": 0.008960695005953312, "std": 0.31460338830947876, "min": -1.1062285900115967, "max": 1.0209500789642334, "zero_fraction": 0.0, "negative_fraction": 0.5141226053237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.dropout": {"mean": 6.460040458478034e-05, "std": 0.22416286170482635, "min": -0.7817442417144775, "max": 0.6862320899963379, "zero_fraction": 0.0, "negative_fraction": 0.47686296701431274, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.norm1": {"mean": -4.58497284583359e-09, "std": 1.0001459121704102, "min": -3.082324743270874, "max": 3.380390167236328, "zero_fraction": 0.0, "negative_fraction": 0.5018028616905212, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.feed_forward.0": {"mean": -0.015303509309887886, "std": 0.580803632736206, "min": -2.286156415939331, "max": 2.0160787105560303, "zero_fraction": 0.0, "negative_fraction": 0.4996244013309479, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.2.feed_forward.1": {"mean": 0.22400668263435364, "std": 0.3283576965332031, "min": 0.0, "max": 2.0160787105560303, "zero_fraction": 0.4996244013309479, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.2.feed_forward.2": {"mean": 0.22400668263435364, "std": 0.3283576965332031, "min": 0.0, "max": 2.0160787105560303, "zero_fraction": 0.4996244013309479, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.2.feed_forward.3": {"mean": 6.460040458478034e-05, "std": 0.22416286170482635, "min": -0.7817442417144775, "max": 0.6862320899963379, "zero_fraction": 0.0, "negative_fraction": 0.47686296701431274, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.norm2": {"mean": 0.0, "std": 1.000145435333252, "min": -3.1726596355438232, "max": 3.4957473278045654, "zero_fraction": 0.0, "negative_fraction": 0.5, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.self_attention.w_q": {"mean": -0.0010049549164250493, "std": 0.5784161686897278, "min": -1.9170604944229126, "max": 2.0104691982269287, "zero_fraction": 0.0, "negative_fraction": 0.49789664149284363, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.self_attention.w_k": {"mean": -0.03710644692182541, "std": 0.5448301434516907, "min": -2.1971001625061035, "max": 1.438524842262268, "zero_fraction": 0.0, "negative_fraction": 0.5300480723381042, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.self_attention.w_v": {"mean": 0.017943190410733223, "std": 0.5728712677955627, "min": -1.7953317165374756, "max": 1.746260404586792, "zero_fraction": 0.0, "negative_fraction": 0.46514421701431274, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.self_attention.dropout": {"mean": 0.07692308723926544, "std": 0.20636621117591858, "min": 4.824378265766427e-07, "max": 0.9999788999557495, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 8, 13, 13], "num_elements": 1352}, "dag_transformer.transformer_layers.3.self_attention.w_o": {"mean": 0.07036853581666946, "std": 0.3327047526836395, "min": -0.9847293496131897, "max": 1.1301190853118896, "zero_fraction": 0.0, "negative_fraction": 0.4344951808452606, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.dropout": {"mean": 0.011816086247563362, "std": 0.24078893661499023, "min": -0.7904757857322693, "max": 0.7408400774002075, "zero_fraction": 0.0, "negative_fraction": 0.4837740361690521, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.norm1": {"mean": 5.7312159462696854e-09, "std": 1.000145673751831, "min": -3.147003412246704, "max": 3.083953380584717, "zero_fraction": 0.0, "negative_fraction": 0.49579328298568726, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.feed_forward.0": {"mean": -0.00014078960521146655, "std": 0.578516960144043, "min": -2.457054615020752, "max": 1.8920481204986572, "zero_fraction": 0.0, "negative_fraction": 0.5054086446762085, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.3.feed_forward.1": {"mean": 0.22933143377304077, "std": 0.33695849776268005, "min": 0.0, "max": 1.8920481204986572, "zero_fraction": 0.5054086446762085, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.3.feed_forward.2": {"mean": 0.22933143377304077, "std": 0.33695849776268005, "min": 0.0, "max": 1.8920481204986572, "zero_fraction": 0.5054086446762085, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.3.feed_forward.3": {"mean": 0.011816086247563362, "std": 0.24078893661499023, "min": -0.7904757857322693, "max": 0.7408400774002075, "zero_fraction": 0.0, "negative_fraction": 0.4837740361690521, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.norm2": {"mean": 0.0, "std": 1.000145673751831, "min": -3.3147525787353516, "max": 3.3134310245513916, "zero_fraction": 0.0, "negative_fraction": 0.49489182233810425, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.self_attention.w_q": {"mean": -0.041954610496759415, "std": 0.5983172059059143, "min": -1.8181275129318237, "max": 2.2275681495666504, "zero_fraction": 0.0, "negative_fraction": 0.5336538553237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.self_attention.w_k": {"mean": -0.0008351625292561948, "std": 0.6145296096801758, "min": -2.0952401161193848, "max": 2.151142120361328, "zero_fraction": 0.0, "negative_fraction": 0.5294471383094788, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.self_attention.w_v": {"mean": 0.005402286536991596, "std": 0.5527353882789612, "min": -1.6148463487625122, "max": 1.881269931793213, "zero_fraction": 0.0, "negative_fraction": 0.5099158883094788, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.self_attention.dropout": {"mean": 0.07692307978868484, "std": 0.20647922158241272, "min": 4.394399581997277e-07, "max": 0.9999854564666748, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 8, 13, 13], "num_elements": 1352}, "dag_transformer.transformer_layers.4.self_attention.w_o": {"mean": -0.00862153246998787, "std": 0.3136439919471741, "min": -1.107136845588684, "max": 0.8724704384803772, "zero_fraction": 0.0, "negative_fraction": 0.5174278616905212, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.dropout": {"mean": -0.0070884376764297485, "std": 0.2194826900959015, "min": -0.6854065656661987, "max": 0.7826749086380005, "zero_fraction": 0.0, "negative_fraction": 0.5258413553237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.norm1": {"mean": 1.1462432114583976e-09, "std": 1.0001455545425415, "min": -3.13158917427063, "max": 3.1784305572509766, "zero_fraction": 0.0, "negative_fraction": 0.49729567766189575, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.feed_forward.0": {"mean": -0.015423914417624474, "std": 0.5815085172653198, "min": -1.9857121706008911, "max": 2.1693596839904785, "zero_fraction": 0.0, "negative_fraction": 0.5141977071762085, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.4.feed_forward.1": {"mean": 0.22455260157585144, "std": 0.3380102813243866, "min": 0.0, "max": 2.1693596839904785, "zero_fraction": 0.5141977071762085, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.4.feed_forward.2": {"mean": 0.22455260157585144, "std": 0.3380102813243866, "min": 0.0, "max": 2.1693596839904785, "zero_fraction": 0.5141977071762085, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.4.feed_forward.3": {"mean": -0.0070884376764297485, "std": 0.2194826900959015, "min": -0.6854065656661987, "max": 0.7826749086380005, "zero_fraction": 0.0, "negative_fraction": 0.5258413553237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.norm2": {"mean": 1.1462432114583976e-09, "std": 1.0001455545425415, "min": -3.024355173110962, "max": 3.5069820880889893, "zero_fraction": 0.0, "negative_fraction": 0.49819710850715637, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.self_attention.w_q": {"mean": -0.055204663425683975, "std": 0.6147074103355408, "min": -2.2023496627807617, "max": 1.8888996839523315, "zero_fraction": 0.0, "negative_fraction": 0.514723539352417, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.self_attention.w_k": {"mean": 0.04964467138051987, "std": 0.5450180768966675, "min": -1.4946922063827515, "max": 1.9099922180175781, "zero_fraction": 0.0, "negative_fraction": 0.4600360691547394, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.self_attention.w_v": {"mean": 0.034698549658060074, "std": 0.5944003462791443, "min": -1.8433663845062256, "max": 2.030017852783203, "zero_fraction": 0.0, "negative_fraction": 0.4813701808452606, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.self_attention.dropout": {"mean": 0.07692307233810425, "std": 0.2066045105457306, "min": 5.126376549924316e-07, "max": 0.999983549118042, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 8, 13, 13], "num_elements": 1352}, "dag_transformer.transformer_layers.5.self_attention.w_o": {"mean": -0.007978538051247597, "std": 0.3209635615348816, "min": -1.1107854843139648, "max": 0.8749573230743408, "zero_fraction": 0.0, "negative_fraction": 0.5015023946762085, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.dropout": {"mean": -0.005493955686688423, "std": 0.2343870997428894, "min": -0.6423646807670593, "max": 0.7083296179771423, "zero_fraction": 0.0, "negative_fraction": 0.5219351053237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.norm1": {"mean": 3.4387295233528903e-09, "std": 1.0001459121704102, "min": -2.9236955642700195, "max": 3.4885244369506836, "zero_fraction": 0.0, "negative_fraction": 0.49338942766189575, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.feed_forward.0": {"mean": -0.009850033558905125, "std": 0.5719338655471802, "min": -1.9390380382537842, "max": 2.163356065750122, "zero_fraction": 0.0, "negative_fraction": 0.51171875, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.5.feed_forward.1": {"mean": 0.22170868515968323, "std": 0.3352130353450775, "min": 0.0, "max": 2.163356065750122, "zero_fraction": 0.51171875, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.5.feed_forward.2": {"mean": 0.22170868515968323, "std": 0.3352130353450775, "min": 0.0, "max": 2.163356065750122, "zero_fraction": 0.51171875, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.5.feed_forward.3": {"mean": -0.005493955686688423, "std": 0.2343870997428894, "min": -0.6423646807670593, "max": 0.7083296179771423, "zero_fraction": 0.0, "negative_fraction": 0.5219351053237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.norm2": {"mean": 2.292486422916795e-09, "std": 1.0001455545425415, "min": -2.990311622619629, "max": 3.3452343940734863, "zero_fraction": 0.0, "negative_fraction": 0.4975961446762085, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.output_projection": {"mean": 0.019946644082665443, "std": 0.5379757881164551, "min": -1.8635728359222412, "max": 1.5139224529266357, "zero_fraction": 0.0, "negative_fraction": 0.47836539149284363, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_embedding.dependency_encoder.0": {"mean": 0.013111351989209652, "std": 0.5722687840461731, "min": -2.004455804824829, "max": 2.0075364112854004, "zero_fraction": 0.0, "negative_fraction": 0.4915865361690521, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_embedding.dependency_encoder.1": {"mean": 0.23542162775993347, "std": 0.33861303329467773, "min": 0.0, "max": 2.0075364112854004, "zero_fraction": 0.4915865361690521, "negative_fraction": 0.0, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_embedding.dependency_encoder.2": {"mean": 0.005569957196712494, "std": 0.23492781817913055, "min": -0.788689374923706, "max": 0.8128820061683655, "zero_fraction": 0.0, "negative_fraction": 0.4954927861690521, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_embedding.resource_encoder.0": {"mean": -0.007705322001129389, "std": 0.5965285897254944, "min": -2.076796054840088, "max": 2.223015546798706, "zero_fraction": 0.0, "negative_fraction": 0.5161458253860474, "shape": [1, 15, 256], "num_elements": 3840}, "pinn_constraint_layer.constraint_embedding.resource_encoder.1": {"mean": 0.2352115511894226, "std": 0.3503486216068268, "min": 0.0, "max": 2.223015546798706, "zero_fraction": 0.5161458253860474, "negative_fraction": 0.0, "shape": [1, 15, 256], "num_elements": 3840}, "pinn_constraint_layer.constraint_embedding.resource_encoder.2": {"mean": -0.0029992773197591305, "std": 0.24016925692558289, "min": -0.9591007232666016, "max": 0.8452867865562439, "zero_fraction": 0.0, "negative_fraction": 0.5119791626930237, "shape": [1, 15, 256], "num_elements": 3840}, "pinn_constraint_layer.constraint_aware_transform.0": {"mean": 0.0006072373362258077, "std": 0.04306644946336746, "min": -0.10813982784748077, "max": 0.11133165657520294, "zero_fraction": 0.0, "negative_fraction": 0.47490984201431274, "shape": [1, 13, 512], "num_elements": 6656}, "pinn_constraint_layer.constraint_aware_transform.1": {"mean": 0.018167881295084953, "std": 0.02394273318350315, "min": 0.0, "max": 0.11133165657520294, "zero_fraction": 0.47490984201431274, "negative_fraction": 0.0, "shape": [1, 13, 512], "num_elements": 6656}, "pinn_constraint_layer.constraint_aware_transform.2": {"mean": 0.018167881295084953, "std": 0.02394273318350315, "min": 0.0, "max": 0.11133165657520294, "zero_fraction": 0.47490984201431274, "negative_fraction": 0.0, "shape": [1, 13, 512], "num_elements": 6656}, "pinn_constraint_layer.constraint_aware_transform.3": {"mean": -0.00021502624440472573, "std": 0.030167140066623688, "min": -0.07213893532752991, "max": 0.08553893864154816, "zero_fraction": 0.0, "negative_fraction": 0.4834735691547394, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_aware_transform.4": {"mean": -3.4387295233528903e-09, "std": 0.9946985244750977, "min": -2.3714547157287598, "max": 2.828139543533325, "zero_fraction": 0.0, "negative_fraction": 0.47536057233810425, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_validator.0": {"mean": -0.021323224529623985, "std": 0.5977339744567871, "min": -1.4015614986419678, "max": 1.4141727685928345, "zero_fraction": 0.0, "negative_fraction": 0.503004789352417, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_validator.1": {"mean": 0.23567993938922882, "std": 0.33397746086120605, "min": 0.0, "max": 1.4141727685928345, "zero_fraction": 0.503004789352417, "negative_fraction": 0.0, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_validator.2": {"mean": -0.25195372104644775, "std": 0.0004878247273154557, "min": -0.25288069248199463, "max": -0.25112926959991455, "zero_fraction": 0.0, "negative_fraction": 1.0, "shape": [1, 13, 1], "num_elements": 13}, "pinn_constraint_layer.constraint_validator.3": {"mean": 0.43734267354011536, "std": 0.0001200431288452819, "min": 0.43711456656455994, "max": 0.4375455677509308, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 13, 1], "num_elements": 13}, "layer_fusion.0.0": {"mean": 0.00933708157390356, "std": 0.30110839009284973, "min": -0.9055431485176086, "max": 1.1806325912475586, "zero_fraction": 0.0, "negative_fraction": 0.5018028616905212, "shape": [1, 13, 256], "num_elements": 3328}, "layer_fusion.0.1": {"mean": 0.12392112612724304, "std": 0.18875767290592194, "min": 0.0, "max": 1.1806325912475586, "zero_fraction": 0.5018028616905212, "negative_fraction": 0.0, "shape": [1, 13, 256], "num_elements": 3328}, "layer_fusion.0.2": {"mean": 0.12392112612724304, "std": 0.18875767290592194, "min": 0.0, "max": 1.1806325912475586, "zero_fraction": 0.5018028616905212, "negative_fraction": 0.0, "shape": [1, 13, 256], "num_elements": 3328}, "layer_fusion.0.3": {"mean": -9.16994569166718e-09, "std": 1.0000089406967163, "min": -0.6832571029663086, "max": 5.23846960067749, "zero_fraction": 0.0, "negative_fraction": 0.6673678159713745, "shape": [1, 13, 256], "num_elements": 3328}, "gat_scheduler.task_input_projection": {"mean": -0.01704513654112816, "std": 0.6009032726287842, "min": -1.8344966173171997, "max": 1.9985660314559937, "zero_fraction": 0.0, "negative_fraction": 0.5054086446762085, "shape": [13, 256], "num_elements": 3328}, "gat_scheduler.node_input_projection": {"mean": 24.834409713745117, "std": 513.3414306640625, "min": -2499.477783203125, "max": 2584.365966796875, "zero_fraction": 0.0, "negative_fraction": 0.4703125059604645, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.0.task_task_attention.lin_src": {"mean": -0.03701067715883255, "std": 0.6409521698951721, "min": -2.069382905960083, "max": 1.5753165483474731, "zero_fraction": 0.0, "negative_fraction": 0.5249398946762085, "shape": [13, 256], "num_elements": 3328}, "gat_scheduler.gat_layers.0.task_task_attention.aggr_module": {"mean": -0.004967215936630964, "std": 0.2530198395252228, "min": -1.7615734338760376, "max": 1.5753165483474731, "zero_fraction": 0.8461538553237915, "negative_fraction": 0.08052884787321091, "shape": [13, 8, 32], "num_elements": 3328}, "gat_scheduler.gat_layers.0.node_feature_transform.0": {"mean": 10.035727500915527, "std": 275.6415100097656, "min": -2081.732666015625, "max": 2187.675537109375, "zero_fraction": 0.0, "negative_fraction": 0.4674479067325592, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.0.node_feature_transform.1": {"mean": 89.56881713867188, "std": 179.0811767578125, "min": 0.0, "max": 2187.675537109375, "zero_fraction": 0.4674479067325592, "negative_fraction": 0.0, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.0.node_feature_transform.2": {"mean": 89.56881713867188, "std": 179.0811767578125, "min": 0.0, "max": 2187.675537109375, "zero_fraction": 0.4674479067325592, "negative_fraction": 0.0, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.0.node_feature_transform.3": {"mean": -5.9604645663569045e-09, "std": 1.000130295753479, "min": -0.74239182472229, "max": 4.908022403717041, "zero_fraction": 0.0, "negative_fraction": 0.64453125, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.0.compatibility_scorer.0": {"mean": -0.0030220546759665012, "std": 0.4181816577911377, "min": -1.1480605602264404, "max": 0.9880422353744507, "zero_fraction": 0.0, "negative_fraction": 0.5, "shape": [256], "num_elements": 256}, "gat_scheduler.gat_layers.0.compatibility_scorer.1": {"mean": 0.16516034305095673, "std": 0.2398589849472046, "min": 0.0, "max": 0.9880422353744507, "zero_fraction": 0.5, "negative_fraction": 0.0, "shape": [256], "num_elements": 256}, "gat_scheduler.gat_layers.0.compatibility_scorer.2": {"mean": -0.03730110079050064, "std": NaN, "min": -0.03730110079050064, "max": -0.03730110079050064, "zero_fraction": 0.0, "negative_fraction": 1.0, "shape": [1], "num_elements": 1}, "gat_scheduler.gat_layers.1.task_task_attention.lin_src": {"mean": 0.0016695453086867929, "std": 0.26104187965393066, "min": -1.7274816036224365, "max": 2.0635592937469482, "zero_fraction": 0.8461538553237915, "negative_fraction": 0.078125, "shape": [13, 256], "num_elements": 3328}, "gat_scheduler.gat_layers.1.task_task_attention.aggr_module": {"mean": 0.0010003281058743596, "std": 0.18384122848510742, "min": -1.7274816036224365, "max": 2.0635592937469482, "zero_fraction": 0.9230769276618958, "negative_fraction": 0.03876201808452606, "shape": [13, 8, 32], "num_elements": 3328}, "gat_scheduler.gat_layers.1.node_feature_transform.0": {"mean": -1.1813395023345947, "std": 282.12255859375, "min": -2070.685302734375, "max": 2020.6942138671875, "zero_fraction": 0.0, "negative_fraction": 0.48515623807907104, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.1.node_feature_transform.1": {"mean": 87.61473846435547, "std": 173.10617065429688, "min": 0.0, "max": 2020.6942138671875, "zero_fraction": 0.48515623807907104, "negative_fraction": 0.0, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.1.node_feature_transform.2": {"mean": 87.61473846435547, "std": 173.10617065429688, "min": 0.0, "max": 2020.6942138671875, "zero_fraction": 0.48515623807907104, "negative_fraction": 0.0, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.1.node_feature_transform.3": {"mean": 1.9868215961338365e-09, "std": 1.0001301765441895, "min": -0.7351184487342834, "max": 4.396845817565918, "zero_fraction": 0.0, "negative_fraction": 0.639843761920929, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.1.compatibility_scorer.0": {"mean": 0.03639109060168266, "std": 0.4057828187942505, "min": -1.1051548719406128, "max": 1.0429760217666626, "zero_fraction": 0.0, "negative_fraction": 0.453125, "shape": [256], "num_elements": 256}, "gat_scheduler.gat_layers.1.compatibility_scorer.1": {"mean": 0.18074920773506165, "std": 0.2397010326385498, "min": 0.0, "max": 1.0429760217666626, "zero_fraction": 0.453125, "negative_fraction": 0.0, "shape": [256], "num_elements": 256}, "gat_scheduler.gat_layers.1.compatibility_scorer.2": {"mean": 0.012071236968040466, "std": NaN, "min": 0.012071236968040466, "max": 0.012071236968040466, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1], "num_elements": 1}, "gat_scheduler.gat_layers.2.task_task_attention.lin_src": {"mean": -0.0022490662522614002, "std": 0.17353269457817078, "min": -1.9365730285644531, "max": 1.85287606716156, "zero_fraction": 0.9230769276618958, "negative_fraction": 0.04116586595773697, "shape": [13, 256], "num_elements": 3328}, "gat_scheduler.gat_layers.2.task_task_attention.aggr_module": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "zero_fraction": 1.0, "negative_fraction": 0.0, "shape": [13, 8, 32], "num_elements": 3328}, "gat_scheduler.gat_layers.2.node_feature_transform.0": {"mean": 10.053815841674805, "std": 292.4762268066406, "min": -2083.4609375, "max": 2310.44873046875, "zero_fraction": 0.0, "negative_fraction": 0.4841145873069763, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.2.node_feature_transform.1": {"mean": 94.53335571289062, "std": 191.19003295898438, "min": 0.0, "max": 2310.44873046875, "zero_fraction": 0.4841145873069763, "negative_fraction": 0.0, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.2.node_feature_transform.2": {"mean": 94.53335571289062, "std": 191.19003295898438, "min": 0.0, "max": 2310.44873046875, "zero_fraction": 0.4841145873069763, "negative_fraction": 0.0, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.2.node_feature_transform.3": {"mean": 0.0, "std": 1.0001301765441895, "min": -0.7054756879806519, "max": 4.242800712585449, "zero_fraction": 0.0, "negative_fraction": 0.6606770753860474, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.2.compatibility_scorer.0": {"mean": 0.038930922746658325, "std": 0.386938214302063, "min": -1.357776403427124, "max": 1.0130754709243774, "zero_fraction": 0.0, "negative_fraction": 0.49609375, "shape": [256], "num_elements": 256}, "gat_scheduler.gat_layers.2.compatibility_scorer.1": {"mean": 0.1727842092514038, "std": 0.24215835332870483, "min": 0.0, "max": 1.0130754709243774, "zero_fraction": 0.49609375, "negative_fraction": 0.0, "shape": [256], "num_elements": 256}, "gat_scheduler.gat_layers.2.compatibility_scorer.2": {"mean": 0.11964266747236252, "std": NaN, "min": 0.11964266747236252, "max": 0.11964266747236252, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1], "num_elements": 1}, "gat_scheduler.task_node_matcher.resource_attention.w_q": {"mean": -0.0009889970533549786, "std": 0.03755160793662071, "min": -0.062320858240127563, "max": 0.06179215759038925, "zero_fraction": 0.0, "negative_fraction": 0.49609375, "shape": [1, 13, 256], "num_elements": 3328}, "gat_scheduler.task_node_matcher.resource_attention.w_k": {"mean": -1.2896347045898438, "std": 280.40509033203125, "min": -1792.4627685546875, "max": 2015.0194091796875, "zero_fraction": 0.0, "negative_fraction": 0.5002604126930237, "shape": [1, 15, 256], "num_elements": 3840}, "gat_scheduler.task_node_matcher.resource_attention.w_v": {"mean": -2.948404550552368, "std": 304.6470642089844, "min": -1987.414306640625, "max": 2245.046142578125, "zero_fraction": 0.0, "negative_fraction": 0.49921876192092896, "shape": [1, 15, 256], "num_elements": 3840}, "gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.0": {"mean": 0.00446851784363389, "std": 0.6204749345779419, "min": -1.9180785417556763, "max": 1.8640077114105225, "zero_fraction": 0.0, "negative_fraction": 0.48645833134651184, "shape": [1, 15, 64], "num_elements": 960}, "gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.1": {"mean": 0.24767877161502838, "std": 0.3587753474712372, "min": 0.0, "max": 1.8640077114105225, "zero_fraction": 0.48645833134651184, "negative_fraction": 0.0, "shape": [1, 15, 64], "num_elements": 960}, "gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.2": {"mean": 0.03560483083128929, "std": 0.26555198431015015, "min": -0.9030558466911316, "max": 1.0643953084945679, "zero_fraction": 0.0, "negative_fraction": 0.4437499940395355, "shape": [1, 15, 128], "num_elements": 1920}, "gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.3": {"mean": 0.12181325256824493, "std": 0.16725020110607147, "min": 0.0, "max": 1.0643953084945679, "zero_fraction": 0.4437499940395355, "negative_fraction": 0.0, "shape": [1, 15, 128], "num_elements": 1920}, "gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.4": {"mean": 0.01324941124767065, "std": 0.0853775143623352, "min": -0.24303963780403137, "max": 0.25777193903923035, "zero_fraction": 0.0, "negative_fraction": 0.4333333373069763, "shape": [1, 15, 8], "num_elements": 120}, "gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.5": {"mean": 0.125, "std": 0.010309039615094662, "min": 0.0978892371058464, "max": 0.14944931864738464, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 15, 8], "num_elements": 120}, "gat_scheduler.task_node_matcher.resource_attention.dropout": {"mean": 0.06666667014360428, "std": 0.016821181401610374, "min": 0.029438868165016174, "max": 0.09399597346782684, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 13, 15], "num_elements": 195}, "gat_scheduler.task_node_matcher.resource_attention.output_linear": {"mean": -12.776796340942383, "std": 114.9371566772461, "min": -356.3648376464844, "max": 255.82244873046875, "zero_fraction": 0.0, "negative_fraction": 0.53515625, "shape": [1, 13, 256], "num_elements": 3328}, "gat_scheduler.task_node_matcher.matching_network.0": {"mean": -30.632701873779297, "std": 253.23236083984375, "min": -752.1752319335938, "max": 612.8883056640625, "zero_fraction": 0.0, "negative_fraction": 0.52734375, "shape": [1, 256], "num_elements": 256}, "gat_scheduler.task_node_matcher.matching_network.1": {"mean": 82.28527069091797, "std": 133.36378479003906, "min": 0.0, "max": 612.8883056640625, "zero_fraction": 0.52734375, "negative_fraction": 0.0, "shape": [1, 256], "num_elements": 256}, "gat_scheduler.task_node_matcher.matching_network.2": {"mean": 82.28527069091797, "std": 133.36378479003906, "min": 0.0, "max": 612.8883056640625, "zero_fraction": 0.52734375, "negative_fraction": 0.0, "shape": [1, 256], "num_elements": 256}, "gat_scheduler.task_node_matcher.matching_network.3": {"mean": 10.677789688110352, "std": 89.50370788574219, "min": -220.71849060058594, "max": 217.6832275390625, "zero_fraction": 0.0, "negative_fraction": 0.453125, "shape": [1, 128], "num_elements": 128}, "gat_scheduler.task_node_matcher.matching_network.4": {"mean": 41.51313400268555, "std": 54.3758430480957, "min": 0.0, "max": 217.6832275390625, "zero_fraction": 0.453125, "negative_fraction": 0.0, "shape": [1, 128], "num_elements": 128}, "gat_scheduler.task_node_matcher.matching_network.5": {"mean": 85.40789794921875, "std": NaN, "min": 85.40789794921875, "max": 85.40789794921875, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 1], "num_elements": 1}}