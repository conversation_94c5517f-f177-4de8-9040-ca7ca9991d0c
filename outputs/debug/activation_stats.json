{"dag_transformer.input_projection": {"mean": -0.3413829803466797, "std": 11.594953536987305, "min": -43.28291702270508, "max": 45.423946380615234, "zero_fraction": 0.0, "negative_fraction": 0.5168269276618958, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.positional_encoding.topo_embedding": {"mean": -0.04198640584945679, "std": 1.0083087682724, "min": -2.968310832977295, "max": 2.980642080307007, "zero_fraction": 0.0, "negative_fraction": 0.49879807233810425, "shape": [1, 13, 64], "num_elements": 832}, "dag_transformer.positional_encoding.depth_embedding": {"mean": -0.014261736534535885, "std": 0.9325831532478333, "min": -2.678738594055176, "max": 3.177635908126831, "zero_fraction": 0.0, "negative_fraction": 0.47235578298568726, "shape": [1, 13, 64], "num_elements": 832}, "dag_transformer.positional_encoding.critical_path_embedding": {"mean": -0.0006341704865917563, "std": 1.03733491897583, "min": -2.956415891647339, "max": 4.349565505981445, "zero_fraction": 0.0, "negative_fraction": 0.49399039149284363, "shape": [1, 13, 64], "num_elements": 832}, "dag_transformer.positional_encoding.dependency_embedding": {"mean": 0.053511835634708405, "std": 0.902269184589386, "min": -2.7024245262145996, "max": 2.770935535430908, "zero_fraction": 0.0, "negative_fraction": 0.4555288553237915, "shape": [1, 13, 64], "num_elements": 832}, "dag_transformer.dropout": {"mean": 0.12330513447523117, "std": 11.602142333984375, "min": -42.05959701538086, "max": 44.46543884277344, "zero_fraction": 0.0, "negative_fraction": 0.49609375, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.self_attention.w_q": {"mean": -0.25887012481689453, "std": 6.48779296875, "min": -31.512664794921875, "max": 34.31355285644531, "zero_fraction": 0.0, "negative_fraction": 0.5093148946762085, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.self_attention.w_k": {"mean": 0.3926807641983032, "std": 6.381443977355957, "min": -25.399133682250977, "max": 31.552026748657227, "zero_fraction": 0.0, "negative_fraction": 0.48467546701431274, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.self_attention.w_v": {"mean": 0.04314662888646126, "std": 6.779699325561523, "min": -31.917802810668945, "max": 31.317752838134766, "zero_fraction": 0.0, "negative_fraction": 0.4921875, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.self_attention.dropout": {"mean": 0.07692307978868484, "std": 0.25343021750450134, "min": 0.0, "max": 1.0, "zero_fraction": 0.048076923936605453, "negative_fraction": 0.0, "shape": [1, 8, 13, 13], "num_elements": 1352}, "dag_transformer.transformer_layers.0.self_attention.w_o": {"mean": -0.2871347665786743, "std": 4.662824630737305, "min": -15.484601020812988, "max": 16.173471450805664, "zero_fraction": 0.0, "negative_fraction": 0.5138221383094788, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.dropout": {"mean": 0.00512697221711278, "std": 0.23742468655109406, "min": -0.7246265411376953, "max": 0.7658196687698364, "zero_fraction": 0.0, "negative_fraction": 0.5045071840286255, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.norm1": {"mean": 1.0316188792103276e-08, "std": 1.000150203704834, "min": -2.883661985397339, "max": 2.992051362991333, "zero_fraction": 0.0, "negative_fraction": 0.49639421701431274, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.feed_forward.0": {"mean": 0.018911106511950493, "std": 0.5911332368850708, "min": -2.0098562240600586, "max": 2.1397039890289307, "zero_fraction": 0.0, "negative_fraction": 0.49399039149284363, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.0.feed_forward.1": {"mean": 0.24580353498458862, "std": 0.3605422079563141, "min": 0.0, "max": 2.1397039890289307, "zero_fraction": 0.49399039149284363, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.0.feed_forward.2": {"mean": 0.24580353498458862, "std": 0.3605422079563141, "min": 0.0, "max": 2.1397039890289307, "zero_fraction": 0.49399039149284363, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.0.feed_forward.3": {"mean": 0.00512697221711278, "std": 0.23742468655109406, "min": -0.7246265411376953, "max": 0.7658196687698364, "zero_fraction": 0.0, "negative_fraction": 0.5045071840286255, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.0.norm2": {"mean": -3.4387295233528903e-09, "std": 1.000145435333252, "min": -2.9761805534362793, "max": 2.8098220825195312, "zero_fraction": 0.0, "negative_fraction": 0.4969951808452606, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.self_attention.w_q": {"mean": 0.019620079547166824, "std": 0.5390259623527527, "min": -1.6805812120437622, "max": 1.654974341392517, "zero_fraction": 0.0, "negative_fraction": 0.4867788553237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.self_attention.w_k": {"mean": 0.04466843232512474, "std": 0.5881898403167725, "min": -1.8265902996063232, "max": 2.193218469619751, "zero_fraction": 0.0, "negative_fraction": 0.4711538553237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.self_attention.w_v": {"mean": 0.03713729605078697, "std": 0.5781666040420532, "min": -1.797884464263916, "max": 1.774609088897705, "zero_fraction": 0.0, "negative_fraction": 0.4759615361690521, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.self_attention.dropout": {"mean": 0.07692307978868484, "std": 0.20653493702411652, "min": 4.4147938638161577e-07, "max": 0.9999786615371704, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 8, 13, 13], "num_elements": 1352}, "dag_transformer.transformer_layers.1.self_attention.w_o": {"mean": -0.011437765322625637, "std": 0.31688880920410156, "min": -0.9345355033874512, "max": 1.4876112937927246, "zero_fraction": 0.0, "negative_fraction": 0.515625, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.dropout": {"mean": -0.00794895924627781, "std": 0.24685227870941162, "min": -1.0238752365112305, "max": 0.8176258206367493, "zero_fraction": 0.0, "negative_fraction": 0.4927884638309479, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.norm1": {"mean": -2.292486422916795e-09, "std": 1.000145673751831, "min": -2.8703784942626953, "max": 3.5386476516723633, "zero_fraction": 0.0, "negative_fraction": 0.5042067170143127, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.feed_forward.0": {"mean": -0.0020394849125295877, "std": 0.5764483213424683, "min": -2.140737533569336, "max": 1.9523106813430786, "zero_fraction": 0.0, "negative_fraction": 0.5089393258094788, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.1.feed_forward.1": {"mean": 0.22965414822101593, "std": 0.3380270004272461, "min": 0.0, "max": 1.9523106813430786, "zero_fraction": 0.5089393258094788, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.1.feed_forward.2": {"mean": 0.22965414822101593, "std": 0.3380270004272461, "min": 0.0, "max": 1.9523106813430786, "zero_fraction": 0.5089393258094788, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.1.feed_forward.3": {"mean": -0.00794895924627781, "std": 0.24685227870941162, "min": -1.0238752365112305, "max": 0.8176258206367493, "zero_fraction": 0.0, "negative_fraction": 0.4927884638309479, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.1.norm2": {"mean": 5.7312159462696854e-09, "std": 1.0001455545425415, "min": -3.1300745010375977, "max": 3.6109580993652344, "zero_fraction": 0.0, "negative_fraction": 0.5024038553237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.self_attention.w_q": {"mean": -0.007570385932922363, "std": 0.5622601509094238, "min": -1.9391002655029297, "max": 1.8801519870758057, "zero_fraction": 0.0, "negative_fraction": 0.49819710850715637, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.self_attention.w_k": {"mean": -0.04183238372206688, "std": 0.5960615277290344, "min": -1.9243394136428833, "max": 1.9531406164169312, "zero_fraction": 0.0, "negative_fraction": 0.5333533883094788, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.self_attention.w_v": {"mean": 0.03128057345747948, "std": 0.5613223910331726, "min": -1.795412540435791, "max": 1.82663893699646, "zero_fraction": 0.0, "negative_fraction": 0.4873798191547394, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.self_attention.dropout": {"mean": 0.07692307978868484, "std": 0.20641480386257172, "min": 4.948955165673397e-07, "max": 0.9999816417694092, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 8, 13, 13], "num_elements": 1352}, "dag_transformer.transformer_layers.2.self_attention.w_o": {"mean": 0.008960697799921036, "std": 0.31460338830947876, "min": -1.1062281131744385, "max": 1.0209503173828125, "zero_fraction": 0.0, "negative_fraction": 0.5141226053237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.dropout": {"mean": 6.460011354647577e-05, "std": 0.22416286170482635, "min": -0.7817442417144775, "max": 0.6862317323684692, "zero_fraction": 0.0, "negative_fraction": 0.47686296701431274, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.norm1": {"mean": 0.0, "std": 1.0001459121704102, "min": -3.082324981689453, "max": 3.3803904056549072, "zero_fraction": 0.0, "negative_fraction": 0.5018028616905212, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.feed_forward.0": {"mean": -0.015303508378565311, "std": 0.580803632736206, "min": -2.286155939102173, "max": 2.016077995300293, "zero_fraction": 0.0, "negative_fraction": 0.4996244013309479, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.2.feed_forward.1": {"mean": 0.22400668263435364, "std": 0.3283576965332031, "min": 0.0, "max": 2.016077995300293, "zero_fraction": 0.4996244013309479, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.2.feed_forward.2": {"mean": 0.22400668263435364, "std": 0.3283576965332031, "min": 0.0, "max": 2.016077995300293, "zero_fraction": 0.4996244013309479, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.2.feed_forward.3": {"mean": 6.460011354647577e-05, "std": 0.22416286170482635, "min": -0.7817442417144775, "max": 0.6862317323684692, "zero_fraction": 0.0, "negative_fraction": 0.47686296701431274, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.2.norm2": {"mean": 0.0, "std": 1.000145435333252, "min": -3.172659397125244, "max": 3.4957473278045654, "zero_fraction": 0.0, "negative_fraction": 0.5, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.self_attention.w_q": {"mean": -0.0010049549164250493, "std": 0.5784161686897278, "min": -1.9170607328414917, "max": 2.0104682445526123, "zero_fraction": 0.0, "negative_fraction": 0.49789664149284363, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.self_attention.w_k": {"mean": -0.03710643947124481, "std": 0.5448302030563354, "min": -2.1970999240875244, "max": 1.4385244846343994, "zero_fraction": 0.0, "negative_fraction": 0.5300480723381042, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.self_attention.w_v": {"mean": 0.017943190410733223, "std": 0.5728712677955627, "min": -1.7953321933746338, "max": 1.7462599277496338, "zero_fraction": 0.0, "negative_fraction": 0.46514421701431274, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.self_attention.dropout": {"mean": 0.07692307978868484, "std": 0.20636621117591858, "min": 4.824379971068993e-07, "max": 0.99997878074646, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 8, 13, 13], "num_elements": 1352}, "dag_transformer.transformer_layers.3.self_attention.w_o": {"mean": 0.07036854326725006, "std": 0.3327047526836395, "min": -0.9847291111946106, "max": 1.130118727684021, "zero_fraction": 0.0, "negative_fraction": 0.4344951808452606, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.dropout": {"mean": 0.011816086247563362, "std": 0.24078895151615143, "min": -0.7904758453369141, "max": 0.7408400774002075, "zero_fraction": 0.0, "negative_fraction": 0.4837740361690521, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.norm1": {"mean": -1.1462432114583976e-09, "std": 1.000145673751831, "min": -3.147003650665283, "max": 3.083954334259033, "zero_fraction": 0.0, "negative_fraction": 0.49579328298568726, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.feed_forward.0": {"mean": -0.00014078502135816962, "std": 0.578516960144043, "min": -2.4570536613464355, "max": 1.8920491933822632, "zero_fraction": 0.0, "negative_fraction": 0.5054086446762085, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.3.feed_forward.1": {"mean": 0.22933143377304077, "std": 0.33695849776268005, "min": 0.0, "max": 1.8920491933822632, "zero_fraction": 0.5054086446762085, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.3.feed_forward.2": {"mean": 0.22933143377304077, "std": 0.33695849776268005, "min": 0.0, "max": 1.8920491933822632, "zero_fraction": 0.5054086446762085, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.3.feed_forward.3": {"mean": 0.011816086247563362, "std": 0.24078895151615143, "min": -0.7904758453369141, "max": 0.7408400774002075, "zero_fraction": 0.0, "negative_fraction": 0.4837740361690521, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.3.norm2": {"mean": -2.292486422916795e-09, "std": 1.000145673751831, "min": -3.3147528171539307, "max": 3.31343150138855, "zero_fraction": 0.0, "negative_fraction": 0.49489182233810425, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.self_attention.w_q": {"mean": -0.04195459932088852, "std": 0.5983172059059143, "min": -1.8181273937225342, "max": 2.2275686264038086, "zero_fraction": 0.0, "negative_fraction": 0.5336538553237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.self_attention.w_k": {"mean": -0.0008351585129275918, "std": 0.6145296096801758, "min": -2.095240354537964, "max": 2.1511411666870117, "zero_fraction": 0.0, "negative_fraction": 0.5294471383094788, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.self_attention.w_v": {"mean": 0.00540230143815279, "std": 0.5527353882789612, "min": -1.614845871925354, "max": 1.8812694549560547, "zero_fraction": 0.0, "negative_fraction": 0.5099158883094788, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.self_attention.dropout": {"mean": 0.07692307978868484, "std": 0.20647922158241272, "min": 4.394403276819503e-07, "max": 0.9999854564666748, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 8, 13, 13], "num_elements": 1352}, "dag_transformer.transformer_layers.4.self_attention.w_o": {"mean": -0.008621527813374996, "std": 0.3136439919471741, "min": -1.1071364879608154, "max": 0.8724703192710876, "zero_fraction": 0.0, "negative_fraction": 0.5174278616905212, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.dropout": {"mean": -0.0070884330198168755, "std": 0.2194826900959015, "min": -0.6854066848754883, "max": 0.7826748490333557, "zero_fraction": 0.0, "negative_fraction": 0.5258413553237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.norm1": {"mean": -4.58497284583359e-09, "std": 1.0001455545425415, "min": -3.131589651107788, "max": 3.178431272506714, "zero_fraction": 0.0, "negative_fraction": 0.49729567766189575, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.feed_forward.0": {"mean": -0.015423915348947048, "std": 0.5815085172653198, "min": -1.9857133626937866, "max": 2.169358730316162, "zero_fraction": 0.0, "negative_fraction": 0.5141977071762085, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.4.feed_forward.1": {"mean": 0.22455261647701263, "std": 0.3380102813243866, "min": 0.0, "max": 2.169358730316162, "zero_fraction": 0.5141977071762085, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.4.feed_forward.2": {"mean": 0.22455261647701263, "std": 0.3380102813243866, "min": 0.0, "max": 2.169358730316162, "zero_fraction": 0.5141977071762085, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.4.feed_forward.3": {"mean": -0.0070884330198168755, "std": 0.2194826900959015, "min": -0.6854066848754883, "max": 0.7826748490333557, "zero_fraction": 0.0, "negative_fraction": 0.5258413553237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.4.norm2": {"mean": 3.4387295233528903e-09, "std": 1.0001455545425415, "min": -3.024355411529541, "max": 3.5069830417633057, "zero_fraction": 0.0, "negative_fraction": 0.49819710850715637, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.self_attention.w_q": {"mean": -0.055204663425683975, "std": 0.6147074103355408, "min": -2.202349901199341, "max": 1.8888996839523315, "zero_fraction": 0.0, "negative_fraction": 0.514723539352417, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.self_attention.w_k": {"mean": 0.04964468255639076, "std": 0.5450180768966675, "min": -1.4946919679641724, "max": 1.90999174118042, "zero_fraction": 0.0, "negative_fraction": 0.4600360691547394, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.self_attention.w_v": {"mean": 0.034698549658060074, "std": 0.5944003462791443, "min": -1.843367099761963, "max": 2.0300180912017822, "zero_fraction": 0.0, "negative_fraction": 0.4813701808452606, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.self_attention.dropout": {"mean": 0.07692307978868484, "std": 0.2066045105457306, "min": 5.126375413055939e-07, "max": 0.999983549118042, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 8, 13, 13], "num_elements": 1352}, "dag_transformer.transformer_layers.5.self_attention.w_o": {"mean": -0.007978535257279873, "std": 0.3209635615348816, "min": -1.1107853651046753, "max": 0.874957263469696, "zero_fraction": 0.0, "negative_fraction": 0.5015023946762085, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.dropout": {"mean": -0.005493953824043274, "std": 0.2343870997428894, "min": -0.6423642635345459, "max": 0.708329439163208, "zero_fraction": 0.0, "negative_fraction": 0.5219351053237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.norm1": {"mean": 0.0, "std": 1.0001459121704102, "min": -2.9236950874328613, "max": 3.488525390625, "zero_fraction": 0.0, "negative_fraction": 0.49338942766189575, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.feed_forward.0": {"mean": -0.009850036352872849, "std": 0.5719338655471802, "min": -1.9390385150909424, "max": 2.1633567810058594, "zero_fraction": 0.0, "negative_fraction": 0.51171875, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.5.feed_forward.1": {"mean": 0.22170868515968323, "std": 0.3352130353450775, "min": 0.0, "max": 2.1633567810058594, "zero_fraction": 0.51171875, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.5.feed_forward.2": {"mean": 0.22170868515968323, "std": 0.3352130353450775, "min": 0.0, "max": 2.1633567810058594, "zero_fraction": 0.51171875, "negative_fraction": 0.0, "shape": [1, 13, 1024], "num_elements": 13312}, "dag_transformer.transformer_layers.5.feed_forward.3": {"mean": -0.005493953824043274, "std": 0.2343870997428894, "min": -0.6423642635345459, "max": 0.708329439163208, "zero_fraction": 0.0, "negative_fraction": 0.5219351053237915, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.transformer_layers.5.norm2": {"mean": 1.1462432114583976e-09, "std": 1.0001455545425415, "min": -2.990312099456787, "max": 3.3452353477478027, "zero_fraction": 0.0, "negative_fraction": 0.4975961446762085, "shape": [1, 13, 256], "num_elements": 3328}, "dag_transformer.output_projection": {"mean": 0.019946657121181488, "std": 0.5379757881164551, "min": -1.8635715246200562, "max": 1.5139236450195312, "zero_fraction": 0.0, "negative_fraction": 0.47836539149284363, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_embedding.dependency_encoder.0": {"mean": 0.013111351057887077, "std": 0.5722687840461731, "min": -2.004456043243408, "max": 2.007535934448242, "zero_fraction": 0.0, "negative_fraction": 0.4915865361690521, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_embedding.dependency_encoder.1": {"mean": 0.23542159795761108, "std": 0.33861303329467773, "min": 0.0, "max": 2.007535934448242, "zero_fraction": 0.4915865361690521, "negative_fraction": 0.0, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_embedding.dependency_encoder.2": {"mean": 0.0055699581280350685, "std": 0.23492781817913055, "min": -0.788689374923706, "max": 0.8128821849822998, "zero_fraction": 0.0, "negative_fraction": 0.4954927861690521, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_embedding.resource_encoder.0": {"mean": -0.007705320604145527, "std": 0.5965285897254944, "min": -2.076796054840088, "max": 2.2230169773101807, "zero_fraction": 0.0, "negative_fraction": 0.5161458253860474, "shape": [1, 15, 256], "num_elements": 3840}, "pinn_constraint_layer.constraint_embedding.resource_encoder.1": {"mean": 0.2352115511894226, "std": 0.3503486216068268, "min": 0.0, "max": 2.2230169773101807, "zero_fraction": 0.5161458253860474, "negative_fraction": 0.0, "shape": [1, 15, 256], "num_elements": 3840}, "pinn_constraint_layer.constraint_embedding.resource_encoder.2": {"mean": -0.002999276854097843, "std": 0.24016925692558289, "min": -0.9591007828712463, "max": 0.8452865481376648, "zero_fraction": 0.0, "negative_fraction": 0.5119791626930237, "shape": [1, 15, 256], "num_elements": 3840}, "pinn_constraint_layer.constraint_aware_transform.0": {"mean": 0.0006072369287721813, "std": 0.04306644946336746, "min": -0.10813982784748077, "max": 0.11133164167404175, "zero_fraction": 0.0, "negative_fraction": 0.47490984201431274, "shape": [1, 13, 512], "num_elements": 6656}, "pinn_constraint_layer.constraint_aware_transform.1": {"mean": 0.018167881295084953, "std": 0.02394273318350315, "min": 0.0, "max": 0.11133164167404175, "zero_fraction": 0.47490984201431274, "negative_fraction": 0.0, "shape": [1, 13, 512], "num_elements": 6656}, "pinn_constraint_layer.constraint_aware_transform.2": {"mean": 0.018167881295084953, "std": 0.02394273318350315, "min": 0.0, "max": 0.11133164167404175, "zero_fraction": 0.47490984201431274, "negative_fraction": 0.0, "shape": [1, 13, 512], "num_elements": 6656}, "pinn_constraint_layer.constraint_aware_transform.3": {"mean": -0.0002150262298528105, "std": 0.030167140066623688, "min": -0.07213893532752991, "max": 0.08553893119096756, "zero_fraction": 0.0, "negative_fraction": 0.4834735691547394, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_aware_transform.4": {"mean": 1.0316188792103276e-08, "std": 0.9946985244750977, "min": -2.371454954147339, "max": 2.828139305114746, "zero_fraction": 0.0, "negative_fraction": 0.47536057233810425, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_validator.0": {"mean": -0.021323226392269135, "std": 0.5977339744567871, "min": -1.4015613794326782, "max": 1.4141730070114136, "zero_fraction": 0.0, "negative_fraction": 0.503004789352417, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_validator.1": {"mean": 0.23567993938922882, "std": 0.33397746086120605, "min": 0.0, "max": 1.4141730070114136, "zero_fraction": 0.503004789352417, "negative_fraction": 0.0, "shape": [1, 13, 256], "num_elements": 3328}, "pinn_constraint_layer.constraint_validator.2": {"mean": -0.25195375084877014, "std": 0.00048784405225887895, "min": -0.25288069248199463, "max": -0.2511292099952698, "zero_fraction": 0.0, "negative_fraction": 1.0, "shape": [1, 13, 1], "num_elements": 13}, "pinn_constraint_layer.constraint_validator.3": {"mean": 0.43734267354011536, "std": 0.00012004634481854737, "min": 0.43711456656455994, "max": 0.4375455677509308, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 13, 1], "num_elements": 13}, "layer_fusion.0.0": {"mean": 0.009337076917290688, "std": 0.30110839009284973, "min": -0.905543327331543, "max": 1.1806325912475586, "zero_fraction": 0.0, "negative_fraction": 0.5018028616905212, "shape": [1, 13, 256], "num_elements": 3328}, "layer_fusion.0.1": {"mean": 0.12392112612724304, "std": 0.18875768780708313, "min": 0.0, "max": 1.1806325912475586, "zero_fraction": 0.5018028616905212, "negative_fraction": 0.0, "shape": [1, 13, 256], "num_elements": 3328}, "layer_fusion.0.2": {"mean": 0.12392112612724304, "std": 0.18875768780708313, "min": 0.0, "max": 1.1806325912475586, "zero_fraction": 0.5018028616905212, "negative_fraction": 0.0, "shape": [1, 13, 256], "num_elements": 3328}, "layer_fusion.0.3": {"mean": -2.292486422916795e-09, "std": 1.0000090599060059, "min": -0.6832571625709534, "max": 5.23846960067749, "zero_fraction": 0.0, "negative_fraction": 0.6673678159713745, "shape": [1, 13, 256], "num_elements": 3328}, "gat_scheduler.task_input_projection": {"mean": -0.017045125365257263, "std": 0.600903332233429, "min": -1.8344972133636475, "max": 1.9985665082931519, "zero_fraction": 0.0, "negative_fraction": 0.5054086446762085, "shape": [13, 256], "num_elements": 3328}, "gat_scheduler.node_input_projection": {"mean": 24.834407806396484, "std": 513.3414306640625, "min": -2499.477783203125, "max": 2584.365966796875, "zero_fraction": 0.0, "negative_fraction": 0.4703125059604645, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.0.task_task_attention.lin_src": {"mean": -0.03701067343354225, "std": 0.6409521698951721, "min": -2.069382429122925, "max": 1.5753169059753418, "zero_fraction": 0.0, "negative_fraction": 0.5249398946762085, "shape": [13, 256], "num_elements": 3328}, "gat_scheduler.gat_layers.0.task_task_attention.aggr_module": {"mean": -0.004967212677001953, "std": 0.25301986932754517, "min": -1.7615735530853271, "max": 1.5753169059753418, "zero_fraction": 0.8461538553237915, "negative_fraction": 0.08052884787321091, "shape": [13, 8, 32], "num_elements": 3328}, "gat_scheduler.gat_layers.0.node_feature_transform.0": {"mean": 10.035727500915527, "std": 275.6414794921875, "min": -2081.7314453125, "max": 2187.67529296875, "zero_fraction": 0.0, "negative_fraction": 0.4674479067325592, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.0.node_feature_transform.1": {"mean": 89.5688247680664, "std": 179.08116149902344, "min": 0.0, "max": 2187.67529296875, "zero_fraction": 0.4674479067325592, "negative_fraction": 0.0, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.0.node_feature_transform.2": {"mean": 89.5688247680664, "std": 179.08116149902344, "min": 0.0, "max": 2187.67529296875, "zero_fraction": 0.4674479067325592, "negative_fraction": 0.0, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.0.node_feature_transform.3": {"mean": -1.5894572769070692e-08, "std": 1.0001301765441895, "min": -0.7423917651176453, "max": 4.908021926879883, "zero_fraction": 0.0, "negative_fraction": 0.64453125, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.0.compatibility_scorer.0": {"mean": -0.003022049553692341, "std": 0.4181816577911377, "min": -1.14806067943573, "max": 0.9880422353744507, "zero_fraction": 0.0, "negative_fraction": 0.5, "shape": [256], "num_elements": 256}, "gat_scheduler.gat_layers.0.compatibility_scorer.1": {"mean": 0.16516035795211792, "std": 0.2398589849472046, "min": 0.0, "max": 0.9880422353744507, "zero_fraction": 0.5, "negative_fraction": 0.0, "shape": [256], "num_elements": 256}, "gat_scheduler.gat_layers.0.compatibility_scorer.2": {"mean": -0.03730112314224243, "std": NaN, "min": -0.03730112314224243, "max": -0.03730112314224243, "zero_fraction": 0.0, "negative_fraction": 1.0, "shape": [1], "num_elements": 1}, "gat_scheduler.gat_layers.1.task_task_attention.lin_src": {"mean": 0.0016695482190698385, "std": 0.26104193925857544, "min": -1.7274812459945679, "max": 2.0635600090026855, "zero_fraction": 0.8461538553237915, "negative_fraction": 0.078125, "shape": [13, 256], "num_elements": 3328}, "gat_scheduler.gat_layers.1.task_task_attention.aggr_module": {"mean": 0.0010003295028582215, "std": 0.18384122848510742, "min": -1.7274812459945679, "max": 2.0635600090026855, "zero_fraction": 0.9230769276618958, "negative_fraction": 0.03876201808452606, "shape": [13, 8, 32], "num_elements": 3328}, "gat_scheduler.gat_layers.1.node_feature_transform.0": {"mean": -1.1813385486602783, "std": 282.12255859375, "min": -2070.684814453125, "max": 2020.6942138671875, "zero_fraction": 0.0, "negative_fraction": 0.48515623807907104, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.1.node_feature_transform.1": {"mean": 87.61474609375, "std": 173.10617065429688, "min": 0.0, "max": 2020.6942138671875, "zero_fraction": 0.48515623807907104, "negative_fraction": 0.0, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.1.node_feature_transform.2": {"mean": 87.61474609375, "std": 173.10617065429688, "min": 0.0, "max": 2020.6942138671875, "zero_fraction": 0.48515623807907104, "negative_fraction": 0.0, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.1.node_feature_transform.3": {"mean": 1.9868215961338365e-09, "std": 1.0001301765441895, "min": -0.7351185083389282, "max": 4.396845817565918, "zero_fraction": 0.0, "negative_fraction": 0.639843761920929, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.1.compatibility_scorer.0": {"mean": 0.03639107197523117, "std": 0.4057828187942505, "min": -1.1051546335220337, "max": 1.0429761409759521, "zero_fraction": 0.0, "negative_fraction": 0.453125, "shape": [256], "num_elements": 256}, "gat_scheduler.gat_layers.1.compatibility_scorer.1": {"mean": 0.18074919283390045, "std": 0.2397010326385498, "min": 0.0, "max": 1.0429761409759521, "zero_fraction": 0.453125, "negative_fraction": 0.0, "shape": [256], "num_elements": 256}, "gat_scheduler.gat_layers.1.compatibility_scorer.2": {"mean": 0.012071236968040466, "std": NaN, "min": 0.012071236968040466, "max": 0.012071236968040466, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1], "num_elements": 1}, "gat_scheduler.gat_layers.2.task_task_attention.lin_src": {"mean": -0.002249066950753331, "std": 0.17353269457817078, "min": -1.93657386302948, "max": 1.8528753519058228, "zero_fraction": 0.9230769276618958, "negative_fraction": 0.04116586595773697, "shape": [13, 256], "num_elements": 3328}, "gat_scheduler.gat_layers.2.task_task_attention.aggr_module": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0, "zero_fraction": 1.0, "negative_fraction": 0.0, "shape": [13, 8, 32], "num_elements": 3328}, "gat_scheduler.gat_layers.2.node_feature_transform.0": {"mean": 10.053816795349121, "std": 292.4762268066406, "min": -2083.460693359375, "max": 2310.448486328125, "zero_fraction": 0.0, "negative_fraction": 0.4841145873069763, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.2.node_feature_transform.1": {"mean": 94.53335571289062, "std": 191.19003295898438, "min": 0.0, "max": 2310.448486328125, "zero_fraction": 0.4841145873069763, "negative_fraction": 0.0, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.2.node_feature_transform.2": {"mean": 94.53335571289062, "std": 191.19003295898438, "min": 0.0, "max": 2310.448486328125, "zero_fraction": 0.4841145873069763, "negative_fraction": 0.0, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.2.node_feature_transform.3": {"mean": -1.9868215517249155e-08, "std": 1.000130295753479, "min": -0.7054756283760071, "max": 4.242798805236816, "zero_fraction": 0.0, "negative_fraction": 0.6606770753860474, "shape": [15, 256], "num_elements": 3840}, "gat_scheduler.gat_layers.2.compatibility_scorer.0": {"mean": 0.03893091902136803, "std": 0.386938214302063, "min": -1.3577766418457031, "max": 1.013075351715088, "zero_fraction": 0.0, "negative_fraction": 0.49609375, "shape": [256], "num_elements": 256}, "gat_scheduler.gat_layers.2.compatibility_scorer.1": {"mean": 0.1727842092514038, "std": 0.24215835332870483, "min": 0.0, "max": 1.013075351715088, "zero_fraction": 0.49609375, "negative_fraction": 0.0, "shape": [256], "num_elements": 256}, "gat_scheduler.gat_layers.2.compatibility_scorer.2": {"mean": 0.11964275687932968, "std": NaN, "min": 0.11964275687932968, "max": 0.11964275687932968, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1], "num_elements": 1}, "gat_scheduler.task_node_matcher.resource_attention.w_q": {"mean": -0.0009889970533549786, "std": 0.03755160793662071, "min": -0.062320858240127563, "max": 0.06179215759038925, "zero_fraction": 0.0, "negative_fraction": 0.49609375, "shape": [1, 13, 256], "num_elements": 3328}, "gat_scheduler.task_node_matcher.resource_attention.w_k": {"mean": -1.2896336317062378, "std": 280.4050598144531, "min": -1792.462890625, "max": 2015.019775390625, "zero_fraction": 0.0, "negative_fraction": 0.5002604126930237, "shape": [1, 15, 256], "num_elements": 3840}, "gat_scheduler.task_node_matcher.resource_attention.w_v": {"mean": -2.94840145111084, "std": 304.6470642089844, "min": -1987.414306640625, "max": 2245.04638671875, "zero_fraction": 0.0, "negative_fraction": 0.49921876192092896, "shape": [1, 15, 256], "num_elements": 3840}, "gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.0": {"mean": 0.0044685183092951775, "std": 0.6204749345779419, "min": -1.9180785417556763, "max": 1.8640077114105225, "zero_fraction": 0.0, "negative_fraction": 0.48645833134651184, "shape": [1, 15, 64], "num_elements": 960}, "gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.1": {"mean": 0.24767878651618958, "std": 0.3587753474712372, "min": 0.0, "max": 1.8640077114105225, "zero_fraction": 0.48645833134651184, "negative_fraction": 0.0, "shape": [1, 15, 64], "num_elements": 960}, "gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.2": {"mean": 0.03560483455657959, "std": 0.26555198431015015, "min": -0.903055727481842, "max": 1.0643951892852783, "zero_fraction": 0.0, "negative_fraction": 0.4437499940395355, "shape": [1, 15, 128], "num_elements": 1920}, "gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.3": {"mean": 0.12181325256824493, "std": 0.16725020110607147, "min": 0.0, "max": 1.0643951892852783, "zero_fraction": 0.4437499940395355, "negative_fraction": 0.0, "shape": [1, 15, 128], "num_elements": 1920}, "gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.4": {"mean": 0.0132494131103158, "std": 0.0853775143623352, "min": -0.2430395781993866, "max": 0.25777190923690796, "zero_fraction": 0.0, "negative_fraction": 0.4333333373069763, "shape": [1, 15, 8], "num_elements": 120}, "gat_scheduler.task_node_matcher.resource_attention.resource_weight_network.5": {"mean": 0.125, "std": 0.010309039615094662, "min": 0.097889244556427, "max": 0.14944930374622345, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 15, 8], "num_elements": 120}, "gat_scheduler.task_node_matcher.resource_attention.dropout": {"mean": 0.06666667014360428, "std": 0.016821179538965225, "min": 0.029438942670822144, "max": 0.09399598836898804, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 13, 15], "num_elements": 195}, "gat_scheduler.task_node_matcher.resource_attention.output_linear": {"mean": -12.776798248291016, "std": 114.93717193603516, "min": -356.364990234375, "max": 255.82252502441406, "zero_fraction": 0.0, "negative_fraction": 0.53515625, "shape": [1, 13, 256], "num_elements": 3328}, "gat_scheduler.task_node_matcher.matching_network.0": {"mean": -30.632707595825195, "std": 253.23236083984375, "min": -752.1754150390625, "max": 612.8883056640625, "zero_fraction": 0.0, "negative_fraction": 0.52734375, "shape": [1, 256], "num_elements": 256}, "gat_scheduler.task_node_matcher.matching_network.1": {"mean": 82.28527069091797, "std": 133.36378479003906, "min": 0.0, "max": 612.8883056640625, "zero_fraction": 0.52734375, "negative_fraction": 0.0, "shape": [1, 256], "num_elements": 256}, "gat_scheduler.task_node_matcher.matching_network.2": {"mean": 82.28527069091797, "std": 133.36378479003906, "min": 0.0, "max": 612.8883056640625, "zero_fraction": 0.52734375, "negative_fraction": 0.0, "shape": [1, 256], "num_elements": 256}, "gat_scheduler.task_node_matcher.matching_network.3": {"mean": 10.6777925491333, "std": 89.50370788574219, "min": -220.718505859375, "max": 217.6832275390625, "zero_fraction": 0.0, "negative_fraction": 0.453125, "shape": [1, 128], "num_elements": 128}, "gat_scheduler.task_node_matcher.matching_network.4": {"mean": 41.51313781738281, "std": 54.3758430480957, "min": 0.0, "max": 217.6832275390625, "zero_fraction": 0.453125, "negative_fraction": 0.0, "shape": [1, 128], "num_elements": 128}, "gat_scheduler.task_node_matcher.matching_network.5": {"mean": 85.40792083740234, "std": NaN, "min": 85.40792083740234, "max": 85.40792083740234, "zero_fraction": 0.0, "negative_fraction": 0.0, "shape": [1, 1], "num_elements": 1}}