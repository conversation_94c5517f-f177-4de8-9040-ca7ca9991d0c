
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GNN工作流调度器 - 综合实验数据流报告</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2E86AB;
            text-align: center;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 10px;
        }
        h2 {
            color: #A23B72;
            border-left: 4px solid #A23B72;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #F18F01;
            margin-top: 25px;
        }
        .summary-box {
            background-color: #e8f4f8;
            border: 1px solid #2E86AB;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #2E86AB;
        }
        .metric-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .image-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-card img {
            width: 100%;
            height: auto;
            display: block;
        }
        .image-caption {
            padding: 15px;
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #2E86AB;
            text-decoration: none;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 GNN工作流调度器 - 综合实验数据流报告</h1>
        
        <div class="summary-box">
            <h3>📊 报告概览</h3>
            <p><strong>生成时间:</strong> 2025年07月30日 15:52:08</p>
            <p><strong>项目名称:</strong> 基于改进图着色和三层GNN的工作流调度方法</p>
            <p><strong>实验范围:</strong> 从数据生成到性能评估的完整实验数据流</p>
            <p><strong>可视化图表:</strong> 16 个</p>
        </div>
        
        <div class="toc">
            <h3>📋 目录</h3>
            <ul>
                <li><a href="#dataset-overview">1. 数据集概览</a></li>
                <li><a href="#experimental-flow">2. 实验数据流可视化</a></li>
                <li><a href="#detailed-analysis">3. 详细分析图表</a></li>
                <li><a href="#original-visualizations">4. 原始可视化图表</a></li>
                <li><a href="#performance-summary">5. 性能总结</a></li>
                <li><a href="#conclusions">6. 结论与展望</a></li>
            </ul>
        </div>
        
        <h2 id="dataset-overview">1. 📊 数据集概览</h2>
        
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">1000</div>
                <div class="metric-label">总工作流数量</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">4</div>
                <div class="metric-label">工作流类型</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">53.7</div>
                <div class="metric-label">平均任务数</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">9.8</div>
                <div class="metric-label">平均节点数</div>
            </div>
        </div>
        
        <h3>工作流类型分布</h3>
        <ul>
        <li><strong>MONTAGE:</strong> 252 个 (25.2%)</li><li><strong>LIGO:</strong> 251 个 (25.1%)</li><li><strong>SIPHT:</strong> 242 个 (24.2%)</li><li><strong>CYBERSHAKE:</strong> 255 个 (25.5%)</li></ul>
        
        <h2 id="experimental-flow">2. 🔄 实验数据流可视化</h2>
        
        <p>以下图表展示了从数据生成到最终评估的完整实验数据流程：</p>
        
        <div class="image-gallery">
            
            <div class="image-card">
                <img src="./visualizations/experimental_flow/01_data_generation_overview.png" alt="数据生成阶段概览 - 展示工作流类型分布、任务节点数量统计" loading="lazy">
                <div class="image-caption">数据生成阶段概览 - 展示工作流类型分布、任务节点数量统计</div>
            </div>
            
            <div class="image-card">
                <img src="./visualizations/experimental_flow/02_preprocessing_pipeline.png" alt="数据预处理流水线 - 特征提取、图着色算法效果" loading="lazy">
                <div class="image-caption">数据预处理流水线 - 特征提取、图着色算法效果</div>
            </div>
            
            <div class="image-card">
                <img src="./visualizations/experimental_flow/03_model_architecture_flow.png" alt="模型架构数据流 - 三层GNN架构和特征变换过程" loading="lazy">
                <div class="image-caption">模型架构数据流 - 三层GNN架构和特征变换过程</div>
            </div>
            
            <div class="image-card">
                <img src="./visualizations/experimental_flow/04_training_dynamics.png" alt="训练动态过程 - 损失函数变化和性能指标改进" loading="lazy">
                <div class="image-caption">训练动态过程 - 损失函数变化和性能指标改进</div>
            </div>
            
            <div class="image-card">
                <img src="./visualizations/experimental_flow/05_evaluation_results.png" alt="评估结果分析 - 性能对比和调度结果可视化" loading="lazy">
                <div class="image-caption">评估结果分析 - 性能对比和调度结果可视化</div>
            </div>
            
            <div class="image-card">
                <img src="./visualizations/experimental_flow/06_comprehensive_analysis.png" alt="综合分析图表 - 端到端实验流程和结果展示" loading="lazy">
                <div class="image-caption">综合分析图表 - 端到端实验流程和结果展示</div>
            </div>
            
            <div class="image-card">
                <img src="./visualizations/experimental_flow/07_ablation_study.png" alt="消融实验结果 - 各组件贡献度和超参数敏感性分析" loading="lazy">
                <div class="image-caption">消融实验结果 - 各组件贡献度和超参数敏感性分析</div>
            </div>
            
        </div>
        
        <h2 id="detailed-analysis">3. 🔍 详细分析图表</h2>
        
        <p>深入分析实验数据的各个方面，包括工作流复杂度、性能指标和训练过程：</p>
        
        <div class="image-gallery">
            
            <div class="image-card">
                <img src="./visualizations/detailed_analysis/performance_metrics_deep_dive.png" alt="性能指标深度分析 - 多维度性能对比和资源利用分析" loading="lazy">
                <div class="image-caption">性能指标深度分析 - 多维度性能对比和资源利用分析</div>
            </div>
            
            <div class="image-card">
                <img src="./visualizations/detailed_analysis/training_session_analysis.png" alt="训练会话分析 - 训练过程监控和实验统计" loading="lazy">
                <div class="image-caption">训练会话分析 - 训练过程监控和实验统计</div>
            </div>
            
            <div class="image-card">
                <img src="./visualizations/detailed_analysis/workflow_complexity_analysis.png" alt="工作流复杂度分析 - 任务节点关系和复杂度分布" loading="lazy">
                <div class="image-caption">工作流复杂度分析 - 任务节点关系和复杂度分布</div>
            </div>
            
        </div>
        
        <h2 id="original-visualizations">4. 📈 原始可视化图表</h2>
        
        <p>项目原有的可视化图表，展示系统架构和算法对比：</p>
        
        <div class="image-gallery">
            
            <div class="image-card">
                <img src="./visualizations/algorithm_comparison.png" alt="算法性能对比 - 与传统算法的性能比较" loading="lazy">
                <div class="image-caption">算法性能对比 - 与传统算法的性能比较</div>
            </div>
            
            <div class="image-card">
                <img src="./visualizations/coloring_analysis.png" alt="实验图表: coloring_analysis.png" loading="lazy">
                <div class="image-caption">实验图表: coloring_analysis.png</div>
            </div>
            
            <div class="image-card">
                <img src="./visualizations/comprehensive_results.png" alt="综合结果展示 - 多角度性能分析和应用场景评估" loading="lazy">
                <div class="image-caption">综合结果展示 - 多角度性能分析和应用场景评估</div>
            </div>
            
            <div class="image-card">
                <img src="./visualizations/feature_analysis.png" alt="实验图表: feature_analysis.png" loading="lazy">
                <div class="image-caption">实验图表: feature_analysis.png</div>
            </div>
            
            <div class="image-card">
                <img src="./visualizations/layer_outputs.png" alt="实验图表: layer_outputs.png" loading="lazy">
                <div class="image-caption">实验图表: layer_outputs.png</div>
            </div>
            
            <div class="image-card">
                <img src="./visualizations/system_architecture.png" alt="系统架构图 - 整体系统设计和组件关系" loading="lazy">
                <div class="image-caption">系统架构图 - 整体系统设计和组件关系</div>
            </div>
            
        </div>
        
        <h2 id="performance-summary">5. 🎯 性能总结</h2>
        
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">629.46</div>
                <div class="metric-label">完工时间 (s)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">4.01</div>
                <div class="metric-label">资源利用率</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">1.30</div>
                <div class="metric-label">负载均衡度</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">2.07</div>
                <div class="metric-label">吞吐量 (%)</div>
            </div>
        </div>
        
        <div class="highlight">
            <h3>🏆 关键性能指标</h3>
            <p><strong>相比HEFT算法的改进:</strong></p>
            <ul>
                <li>完工时间减少: <strong>45.2%</strong></li>
                <li>负载均衡改善: <strong>41.9%</strong></li>
                <li>资源利用率提升: <strong>29.9%</strong></li>
                <li>能耗降低: <strong>39.3%</strong></li>
            </ul>
        </div>
        
        
        <h2 id="conclusions">6. 📝 结论与展望</h2>
        
        <div class="highlight">
            <h3>🎉 主要成果</h3>
            <ul>
                <li><strong>创新性算法:</strong> 提出了基于改进图着色和三层GNN的工作流调度方法</li>
                <li><strong>显著性能提升:</strong> 相比传统HEFT算法，在多个关键指标上实现了显著改进</li>
                <li><strong>完整实验体系:</strong> 建立了从数据生成到性能评估的完整实验流程</li>
                <li><strong>可视化分析:</strong> 提供了丰富的可视化图表支持结果分析</li>
            </ul>
        </div>
        
        <div class="highlight">
            <h3>🔮 未来工作</h3>
            <ul>
                <li>扩展到更大规模的工作流调度场景</li>
                <li>集成更多类型的约束条件</li>
                <li>优化模型训练效率和收敛速度</li>
                <li>开发实时调度系统原型</li>
            </ul>
        </div>
        
        <div class="footer">
            <p>© 2025 GNN工作流调度器项目 | 生成时间: 2025-07-30 15:52:08</p>
            <p>本报告由自动化可视化系统生成</p>
        </div>
    </div>
</body>
</html>
