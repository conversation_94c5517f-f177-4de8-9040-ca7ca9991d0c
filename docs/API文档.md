# GNN工作流调度器 API 文档

## 📋 概述

本文档详细描述了GNN工作流调度器的所有API接口，包括类、方法、参数和返回值。

## 🏗️ 核心架构

### 模块结构

```
src/
├── data_generation/     # 数据生成模块
├── preprocessing/       # 数据预处理模块
├── models/             # 模型定义模块
├── training/           # 训练模块
├── evaluation/         # 评估模块
└── utils/              # 工具模块
```

## 📊 数据生成模块

### WorkflowSimGenerator

生成科学工作流数据的核心类。

```python
class WorkflowSimGenerator:
    def __init__(self, config: DataConfig)
```

**参数:**
- `config`: 数据配置对象

**方法:**

#### generate_dataset()
```python
def generate_dataset(self, output_dir: str) -> None
```
生成完整的工作流数据集。

**参数:**
- `output_dir`: 输出目录路径

**输出文件:**
- `workflows.json`: 工作流定义
- `dataset_statistics.json`: 数据集统计

#### generate_single_workflow()
```python
def generate_single_workflow(self, workflow_type: str, num_tasks: int, num_nodes: int) -> Dict[str, Any]
```
生成单个工作流。

**参数:**
- `workflow_type`: 工作流类型 ('montage', 'cybershake', 'ligo', 'sipht')
- `num_tasks`: 任务数量
- `num_nodes`: 节点数量

**返回:**
- `Dict`: 包含DAG、节点信息的工作流数据

### WorkflowDataset

PyTorch数据集类，用于加载和处理工作流数据。

```python
class WorkflowDataset(Dataset):
    def __init__(self, processed_data: List[Dict], augment: bool = False)
```

**参数:**
- `processed_data`: 预处理后的工作流数据列表
- `augment`: 是否启用数据增强

**方法:**

#### __getitem__()
```python
def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]
```
获取单个数据样本。

**返回字典包含:**
- `task_features`: 任务特征张量 (num_tasks, 128)
- `node_features`: 节点特征张量 (num_nodes, 32)
- `adjacency_matrix`: 邻接矩阵 (num_tasks, num_tasks)
- `edge_index`: 边索引 (2, num_edges)
- `ground_truth_assignments`: 真实分配 (num_tasks,)
- `constraint_data`: 约束数据字典

## 🔄 预处理模块

### WorkflowFeatureExtractor

增强的特征提取器，支持多维特征工程。

```python
class WorkflowFeatureExtractor:
    def __init__(self, feature_dim: int = 128)
```

**参数:**
- `feature_dim`: 特征维度

**方法:**

#### extract_task_features()
```python
def extract_task_features(self, dag: nx.DiGraph, workflow_type: str) -> Tuple[torch.Tensor, Dict]
```
提取任务特征。

**参数:**
- `dag`: 工作流DAG
- `workflow_type`: 工作流类型

**返回:**
- `torch.Tensor`: 任务特征矩阵 (num_tasks, 128)
- `Dict`: 元数据信息

**特征组成 (128维):**
- 基础任务特征 (32维): 执行时间、数据大小、计算强度
- 资源需求特征 (16维): CPU、内存、I/O、网络需求
- DAG结构特征 (24维): 度数、路径、中心性指标
- 图着色特征 (8维): 颜色编码、资源类型
- 工作流上下文特征 (24维): 类型编码、并行组信息
- 统计特征 (24维): 运行时间统计、邻居特征统计

#### extract_node_features()
```python
def extract_node_features(self, nodes: List[Dict]) -> torch.Tensor
```
提取节点特征。

**参数:**
- `nodes`: 节点信息列表

**返回:**
- `torch.Tensor`: 节点特征矩阵 (num_nodes, 32)

**特征组成 (32维):**
- 基础容量特征 (8维): CPU、内存、I/O、网络容量
- 效率和成本特征 (2维): 能源效率、成本
- 节点类型特征 (4维): 基于容量的类型编码
- 其他特征 (18维): 预留扩展空间

### ImprovedGraphColoring

改进的图着色算法，支持资源感知着色。

```python
class ImprovedGraphColoring:
    def __init__(self)
```

**方法:**

#### color_graph()
```python
def color_graph(self, dag: nx.DiGraph) -> Dict[str, Any]
```
对DAG进行着色。

**参数:**
- `dag`: 工作流DAG

**返回:**
- `Dict`: 着色结果，包含颜色分配、资源类型、质量指标

## 🧠 模型模块

### ThreeLayerGNNScheduler

三层融合GNN调度器主模型。

```python
class ThreeLayerGNNScheduler(nn.Module):
    def __init__(self, config: Dict[str, Any])
```

**配置参数:**
- `input_dim`: 输入特征维度
- `hidden_dim`: 隐藏层维度
- `num_heads`: 注意力头数
- `num_transformer_layers`: Transformer层数
- `num_gat_layers`: GAT层数
- `dropout`: Dropout率
- `constraint_weights`: 约束权重字典

**方法:**

#### forward()
```python
def forward(self, batch_data: Dict[str, torch.Tensor], debug_mode: bool = False) -> Tuple[torch.Tensor, Dict]
```
前向传播。

**参数:**
- `batch_data`: 批次数据字典
- `debug_mode`: 是否启用调试模式

**返回:**
- `torch.Tensor`: 分配概率矩阵 (batch_size, num_tasks, num_nodes)
- `Dict`: 调试信息（如果启用调试模式）

#### compute_total_loss()
```python
def compute_total_loss(self, assignment_probs: torch.Tensor, ground_truth: torch.Tensor, constraint_losses: torch.Tensor) -> Dict[str, torch.Tensor]
```
计算总损失。

**参数:**
- `assignment_probs`: 分配概率
- `ground_truth`: 真实标签
- `constraint_losses`: 约束损失

**返回:**
- `Dict`: 包含各种损失的字典

### 层级组件

#### DAGTransformer
```python
class DAGTransformer(nn.Module):
    def __init__(self, input_dim: int, d_model: int, num_heads: int, num_layers: int)
```
DAG专用Transformer层，处理任务依赖关系。

#### PINNConstraintLayer
```python
class PINNConstraintLayer(nn.Module):
    def __init__(self, input_dim: int, hidden_dim: int, constraint_weights: Dict[str, float])
```
物理约束增强层，嵌入调度约束。

#### GATScheduler
```python
class GATScheduler(nn.Module):
    def __init__(self, input_dim: int, hidden_dim: int, num_heads: int, num_layers: int)
```
图注意力网络调度决策层。

## 🚀 训练模块

### GNNTrainer

GNN模型训练器，支持完整的训练流程。

```python
class GNNTrainer:
    def __init__(self, model: ThreeLayerGNNScheduler, config: TrainingConfig, device: torch.device)
```

**参数:**
- `model`: GNN模型实例
- `config`: 训练配置
- `device`: 计算设备

**方法:**

#### train()
```python
def train(self, train_loader: DataLoader, val_loader: DataLoader, save_dir: str, debug_mode: bool = False) -> Dict[str, List]
```
完整训练流程。

**参数:**
- `train_loader`: 训练数据加载器
- `val_loader`: 验证数据加载器
- `save_dir`: 模型保存目录
- `debug_mode`: 调试模式

**返回:**
- `Dict`: 训练历史记录

#### train_epoch()
```python
def train_epoch(self, train_loader: DataLoader, debug_mode: bool = False) -> Dict[str, float]
```
训练一个epoch。

#### validate_epoch()
```python
def validate_epoch(self, val_loader: DataLoader) -> Dict[str, float]
```
验证一个epoch。

#### save_checkpoint()
```python
def save_checkpoint(self, epoch: int, save_dir: str, is_best: bool = False) -> str
```
保存模型检查点。

#### load_checkpoint()
```python
def load_checkpoint(self, checkpoint_path: str) -> Dict[str, Any]
```
加载模型检查点。

## 📈 评估模块

### SchedulingMetrics

调度性能指标计算器。

```python
class SchedulingMetrics:
    def __init__(self)
```

**方法:**

#### compute_batch_metrics()
```python
def compute_batch_metrics(self, assignment_probs: torch.Tensor, eval_data: Dict[str, torch.Tensor]) -> Dict[str, float]
```
计算批次性能指标。

**返回指标:**
- `makespan`: 完工时间
- `load_balance`: 负载均衡度
- `resource_utilization`: 资源利用率
- `energy_consumption`: 能耗
- `communication_cost`: 通信成本

#### aggregate_metrics()
```python
def aggregate_metrics(self, batch_metrics_list: List[Dict[str, float]]) -> Dict[str, float]
```
聚合多个批次的指标。

### EnhancedWorkflowVisualizer

增强的可视化器，支持交互式图表。

```python
class EnhancedWorkflowVisualizer:
    def __init__(self, output_dir: str)
```

**方法:**

#### create_training_dashboard()
```python
def create_training_dashboard(self, train_history: Dict[str, List]) -> str
```
创建训练监控仪表板。

#### create_interactive_dag_visualization()
```python
def create_interactive_dag_visualization(self, workflow_data: Dict, assignment_probs: Optional[torch.Tensor] = None) -> str
```
创建交互式DAG可视化。

#### generate_comprehensive_report()
```python
def generate_comprehensive_report(self, workflow_data: Dict, debug_info: Dict, assignment_probs: torch.Tensor, train_history: Dict) -> str
```
生成综合分析报告。

## 🛠️ 工具模块

### 数据加载工具

#### create_data_loaders()
```python
def create_data_loaders(processed_data: List[Dict], train_ratio: float = 0.8, val_ratio: float = 0.1, batch_size: int = 32, num_workers: int = 0, augment_train: bool = True) -> Tuple[DataLoader, DataLoader, DataLoader]
```
创建训练、验证和测试数据加载器。

#### collate_workflow_batch()
```python
def collate_workflow_batch(batch: List[Dict]) -> Dict[str, torch.Tensor]
```
批处理整理函数。

### 日志工具

#### setup_logging()
```python
def setup_logging(level: str = 'INFO', log_file: Optional[str] = None) -> None
```
设置日志系统。

#### get_logger()
```python
def get_logger(name: str) -> logging.Logger
```
获取日志记录器。

### 设备工具

#### setup_device()
```python
def setup_device(device_str: str) -> torch.device
```
设置计算设备。

### 可重现性工具

#### set_random_seeds()
```python
def set_random_seeds(seed: int) -> None
```
设置随机种子确保可重现性。

## 🔧 配置类

### DataConfig
```python
@dataclass
class DataConfig:
    num_workflows: int = 1000
    workflow_types: List[str] = None
    min_tasks: int = 10
    max_tasks: int = 100
    min_nodes: int = 4
    max_nodes: int = 16
    heterogeneity_factor: float = 0.5
```

### ModelConfig
```python
@dataclass
class ModelConfig:
    hidden_dim: int = 256
    num_heads: int = 8
    num_layers: int = 6
    dropout: float = 0.1
    activation: str = 'relu'
    constraint_weight: float = 1.0
```

### TrainingConfig
```python
@dataclass
class TrainingConfig:
    batch_size: int = 32
    learning_rate: float = 1e-3
    num_epochs: int = 100
    weight_decay: float = 1e-4
    patience: int = 10
```

### ExperimentConfig
```python
@dataclass
class ExperimentConfig:
    data: DataConfig
    model: ModelConfig
    training: TrainingConfig
    output_dir: str = "./outputs"
    log_level: str = "INFO"
    
    @classmethod
    def from_yaml(cls, config_path: str) -> 'ExperimentConfig'
```

## 📝 使用示例

### 完整训练流程

```python
from config.base_config import ExperimentConfig
from src.data_generation.workflowsim_generator import WorkflowSimGenerator
from src.preprocessing.feature_extraction import WorkflowFeatureExtractor
from src.models.three_layer_gnn import ThreeLayerGNNScheduler
from src.training.trainer import GNNTrainer
from src.data_generation.data_loader import create_data_loaders

# 1. 加载配置
config = ExperimentConfig.from_yaml('config/experiment_config.yaml')

# 2. 生成数据
generator = WorkflowSimGenerator(config.data)
generator.generate_dataset('./data')

# 3. 预处理数据
extractor = WorkflowFeatureExtractor()
# ... 预处理逻辑

# 4. 创建模型
model_config = {
    'input_dim': 128,
    'hidden_dim': config.model.hidden_dim,
    'num_heads': config.model.num_heads,
    'num_transformer_layers': config.model.num_layers,
    'num_gat_layers': 3,
    'dropout': config.model.dropout,
    'constraint_weights': {
        'dependency': 1.0,
        'resource': 1.0,
        'temporal': 0.5,
        'communication': 0.5
    }
}
model = ThreeLayerGNNScheduler(model_config)

# 5. 创建数据加载器
train_loader, val_loader, test_loader = create_data_loaders(processed_data)

# 6. 训练模型
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
trainer = GNNTrainer(model, config.training, device)
train_history = trainer.train(train_loader, val_loader, './outputs/models')
```

---

## 📞 技术支持

如需技术支持或有API相关问题，请参考：
- 用户手册
- GitHub Issues
- 项目文档
