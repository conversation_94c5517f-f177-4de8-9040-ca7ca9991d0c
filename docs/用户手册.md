# GNN工作流调度器用户手册

## 📖 目录

1. [快速开始](#快速开始)
2. [安装指南](#安装指南)
3. [配置说明](#配置说明)
4. [使用教程](#使用教程)
5. [API参考](#api参考)
6. [故障排除](#故障排除)
7. [最佳实践](#最佳实践)

## 🚀 快速开始

### 环境要求

- Python 3.8+
- PyTorch 1.12+
- CUDA 11.0+ (可选，用于GPU加速)
- 8GB+ RAM (推荐16GB+)

### 一键运行

```bash
# 克隆项目
git clone <repository-url>
cd GNN_Workflow_Scheduler

# 安装依赖
pip install -r requirements.txt

# 运行完整流程
python main.py --mode all --data-dir ./data --output-dir ./outputs
```

## 📦 安装指南

### 1. 基础安装

```bash
# 创建虚拟环境
conda create -n gnn_scheduler python=3.8
conda activate gnn_scheduler

# 安装PyTorch (根据您的CUDA版本选择)
# CPU版本
pip install torch torchvision torchaudio

# GPU版本 (CUDA 11.8)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装其他依赖
pip install -r requirements.txt
```

### 2. 验证安装

```bash
# 检查环境
python test_env.py

# 检查CUDA (如果使用GPU)
python test_cuda.py
```

### 3. 开发环境设置

```bash
# 安装开发依赖
pip install pytest pytest-cov black flake8

# 运行测试
pytest tests/ -v

# 代码格式化
black src/ tests/
```

## ⚙️ 配置说明

### 配置文件结构

项目使用YAML配置文件 `config/experiment_config.yaml`：

```yaml
# 数据配置
data:
  num_workflows: 1000          # 生成的工作流数量
  workflow_types:              # 支持的工作流类型
    - montage
    - cybershake
    - ligo
    - sipht
  min_tasks: 10               # 最小任务数
  max_tasks: 100              # 最大任务数
  min_nodes: 4                # 最小节点数
  max_nodes: 16               # 最大节点数
  heterogeneity_factor: 0.5   # 异构性因子

# 模型配置
model:
  hidden_dim: 256             # 隐藏层维度
  num_heads: 8                # 注意力头数
  num_layers: 6               # Transformer层数
  dropout: 0.1                # Dropout率
  activation: relu            # 激活函数
  constraint_weight: 1.0      # 约束权重

# 训练配置
training:
  batch_size: 32              # 批次大小
  learning_rate: 0.001        # 学习率
  num_epochs: 100             # 训练轮数
  weight_decay: 0.0001        # 权重衰减
  patience: 10                # 早停耐心值

# 输出配置
output_dir: "./outputs"       # 输出目录
log_level: "INFO"            # 日志级别
```

### 自定义配置

```python
from config.base_config import ExperimentConfig, DataConfig, ModelConfig, TrainingConfig

# 创建自定义配置
custom_config = ExperimentConfig(
    data=DataConfig(
        num_workflows=500,
        workflow_types=['montage', 'ligo'],
        min_tasks=20,
        max_tasks=50
    ),
    model=ModelConfig(
        hidden_dim=128,
        num_heads=4,
        num_layers=4
    ),
    training=TrainingConfig(
        batch_size=16,
        learning_rate=5e-4,
        num_epochs=50
    )
)
```

## 📚 使用教程

### 1. 数据生成

```bash
# 生成默认数据集
python main.py --mode generate --data-dir ./data

# 生成自定义数据集
python main.py --mode generate --data-dir ./custom_data --config ./custom_config.yaml
```

生成的数据包括：
- `workflows.json`: 工作流定义文件
- `dataset_statistics.json`: 数据集统计信息

### 2. 数据预处理

```bash
# 自动预处理（在训练时自动执行）
python main.py --mode train --data-dir ./data

# 手动预处理
python -c "
from main import preprocess_data
preprocess_data('./data', './outputs', debug=True)
"
```

预处理包括：
- 特征提取（128维任务特征，32维节点特征）
- 图着色算法应用
- 数据归一化和增强

### 3. 模型训练

```bash
# 基础训练
python main.py --mode train --data-dir ./data --output-dir ./outputs

# 调试模式训练
python main.py --mode debug --data-dir ./data --output-dir ./outputs --debug

# 自定义设备训练
python main.py --mode train --device cuda --data-dir ./data
```

训练输出：
- 模型检查点
- 训练日志
- 可视化图表
- 性能指标

### 4. 模型评估

```bash
# 评估模型
python main.py --mode evaluate --data-dir ./data --output-dir ./outputs

# 查看评估结果
cat outputs/evaluation_results.json
```

### 5. 可视化分析

```bash
# 生成可视化图表
python generate_visualizations.py --output-dir ./visualizations

# 查看生成的图表
ls visualizations/
# - algorithm_comparison.png
# - coloring_analysis.png
# - comprehensive_results.png
# - feature_analysis.png
# - layer_outputs.png
# - system_architecture.png
```

### 6. 交互式分析

```python
from src.evaluation.enhanced_visualization import EnhancedWorkflowVisualizer

# 创建可视化器
visualizer = EnhancedWorkflowVisualizer('./outputs/interactive')

# 生成交互式报告
report_path = visualizer.generate_comprehensive_report(
    workflow_data, debug_info, assignment_probs, train_history
)

print(f"交互式报告生成完成: {report_path}")
```

## 🔧 API参考

### 核心类

#### WorkflowSimGenerator
```python
from src.data_generation.workflowsim_generator import WorkflowSimGenerator

generator = WorkflowSimGenerator(config.data)
generator.generate_dataset('./data')
```

#### WorkflowFeatureExtractor
```python
from src.preprocessing.feature_extraction import WorkflowFeatureExtractor

extractor = WorkflowFeatureExtractor(feature_dim=128)
task_features, metadata = extractor.extract_task_features(dag, workflow_type)
node_features = extractor.extract_node_features(nodes)
```

#### ThreeLayerGNNScheduler
```python
from src.models.three_layer_gnn import ThreeLayerGNNScheduler

model_config = {
    'input_dim': 128,
    'hidden_dim': 256,
    'num_heads': 8,
    'num_transformer_layers': 6,
    'num_gat_layers': 3,
    'dropout': 0.1,
    'constraint_weights': {
        'dependency': 1.0,
        'resource': 1.0,
        'temporal': 0.5,
        'communication': 0.5
    }
}

model = ThreeLayerGNNScheduler(model_config)
assignment_probs, debug_info = model(batch_data, debug_mode=True)
```

#### GNNTrainer
```python
from src.training.trainer import GNNTrainer

trainer = GNNTrainer(model, config.training, device)
train_history = trainer.train(train_loader, val_loader, save_dir)
```

### 数据加载

```python
from src.data_generation.data_loader import create_data_loaders, load_processed_data

# 加载数据
processed_data = load_processed_data('./outputs/processed_workflows.npy')

# 创建数据加载器
train_loader, val_loader, test_loader = create_data_loaders(
    processed_data,
    train_ratio=0.8,
    val_ratio=0.1,
    batch_size=32,
    augment_train=True
)
```

### 评估指标

```python
from src.evaluation.metrics import SchedulingMetrics

metrics = SchedulingMetrics()
batch_metrics = metrics.compute_batch_metrics(assignment_probs, eval_data)

print(f"Makespan: {batch_metrics['makespan']:.4f}")
print(f"Load Balance: {batch_metrics['load_balance']:.4f}")
print(f"Resource Utilization: {batch_metrics['resource_utilization']:.4f}")
```

## 🐛 故障排除

### 常见问题

#### 1. 内存不足
```bash
# 减少批次大小
python main.py --mode train --config config_small_batch.yaml

# 使用调试模式（处理更少数据）
python main.py --mode debug --debug
```

#### 2. CUDA错误
```bash
# 检查CUDA可用性
python -c "import torch; print(torch.cuda.is_available())"

# 强制使用CPU
python main.py --mode train --device cpu
```

#### 3. 依赖问题
```bash
# 重新安装依赖
pip install --force-reinstall -r requirements.txt

# 检查版本兼容性
pip list | grep torch
```

#### 4. 配置文件错误
```bash
# 验证配置文件
python -c "
from config.base_config import ExperimentConfig
config = ExperimentConfig.from_yaml('config/experiment_config.yaml')
print('配置文件有效')
"
```

### 日志分析

```bash
# 查看最新日志
tail -f logs/gnn_scheduler_*.log

# 搜索错误信息
grep -i error logs/gnn_scheduler_*.log

# 查看训练进度
grep -i "epoch\|loss\|metric" logs/gnn_scheduler_*.log
```

## 💡 最佳实践

### 1. 数据准备
- 确保工作流数据质量，包含完整的任务和节点信息
- 使用合适的数据集大小（开发时用小数据集，生产时用大数据集）
- 定期备份重要的预处理数据

### 2. 模型训练
- 从小模型开始，逐步增加复杂度
- 使用调试模式验证模型架构
- 监控训练过程，及时调整超参数
- 使用早停避免过拟合

### 3. 性能优化
- 使用GPU加速训练（如果可用）
- 合理设置批次大小平衡内存和性能
- 使用数据并行处理大规模数据集
- 定期清理临时文件和日志

### 4. 结果分析
- 使用多种可视化方法分析结果
- 对比不同算法的性能
- 分析失败案例，改进模型
- 记录实验结果和配置

### 5. 部署建议
- 在生产环境中使用稳定的模型版本
- 实施模型版本控制
- 监控模型性能和资源使用
- 准备回滚策略

---

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看本手册的故障排除部分
2. 检查项目的GitHub Issues
3. 提交新的Issue描述问题
4. 参与项目讨论和改进

感谢您使用GNN工作流调度器！
