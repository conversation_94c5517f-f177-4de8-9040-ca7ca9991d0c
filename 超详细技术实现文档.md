# 基于改进图着色和三层GNN的工作流调度系统 - 超详细技术实现

## 📊 数据输入格式与维度详解

### 1. 原始输入数据结构

#### 1.1 工作流DAG数据格式
```python
# 输入工作流DAG结构
workflow_dag = {
    'nodes': {
        'task_0': {
            'runtime': 45.2,           # 执行时间(秒)
            'input_size': 1024.5,      # 输入数据大小(MB)
            'output_size': 512.3,      # 输出数据大小(MB)
            'cpu_demand': 2.5,         # CPU需求(核数)
            'memory_demand': 4096.0,   # 内存需求(MB)
            'io_operations': 150,      # I/O操作次数
            'network_transfer': 256.7, # 网络传输量(MB)
            'task_type': 'compute',    # 任务类型
            'priority': 1              # 优先级
        },
        'task_1': { ... },
        # ... 更多任务
    },
    'edges': [
        ('task_0', 'task_1', {'data_size': 128.5}),  # 依赖关系和数据传输量
        ('task_0', 'task_2', {'data_size': 64.2}),
        # ... 更多边
    ],
    'metadata': {
        'workflow_type': 'montage',    # 工作流类型
        'total_tasks': 25,             # 总任务数
        'critical_path_length': 156.8, # 关键路径长度
        'parallelism_degree': 3.2      # 并行度
    }
}
```

#### 1.2 异构节点数据格式
```python
# 计算节点配置
compute_nodes = [
    {
        'node_id': 'node_0',
        'cpu_capacity': 8.0,           # CPU容量(核数)
        'memory_capacity': 16384.0,    # 内存容量(MB)
        'io_capacity': 1000.0,         # I/O带宽(MB/s)
        'network_capacity': 1000.0,    # 网络带宽(MB/s)
        'energy_efficiency': 0.85,     # 能源效率
        'cost_per_hour': 0.12,         # 每小时成本($)
        'node_type': 'cpu_intensive',  # 节点类型
        'availability': 0.99,          # 可用性
        'location': 'datacenter_A'     # 位置信息
    },
    # ... 更多节点 (最多16个)
]
```

### 2. 特征提取阶段详细流程

#### 2.1 改进图着色算法输出
```python
# 图着色结果数据结构
coloring_result = {
    'colors': {
        'task_0': 0,    # 任务着色结果 (0-4颜色)
        'task_1': 1,
        'task_2': 0,
        # ...
    },
    'resource_types': {
        'task_0': 'cpu_intensive',     # 资源主导类型
        'task_1': 'memory_intensive',
        'task_2': 'cpu_intensive',
        # ...
    },
    'color_features': {
        'task_0': [1, 0, 0, 0, 0, 1, 0, 0],  # 8维着色特征向量
        'task_1': [0, 1, 0, 0, 0, 0, 1, 0],
        # ...
    },
    'quality_metrics': {
        'conflict_rate': 0.0,          # 冲突率
        'load_balance': 0.92,          # 负载均衡度
        'resource_consistency': 0.893, # 资源一致性
        'parallelization_efficiency': 0.42  # 并行化效率
    }
}
```

#### 2.2 任务特征提取详细过程 (128维)

##### 基础任务特征 (32维)
```python
def extract_basic_task_features(task_data):
    """提取基础任务特征"""
    features = np.zeros(32)
    
    # 执行时间特征 (4维)
    runtime = task_data['runtime']
    features[0] = np.log1p(runtime)                    # log变换运行时间
    features[1] = runtime / 100.0                      # 归一化运行时间
    features[2] = min(runtime, 200) / 200.0           # 截断归一化
    features[3] = 1.0 if runtime > 50 else 0.0        # 长任务标识
    
    # 数据大小特征 (8维)
    input_size = task_data['input_size']
    output_size = task_data['output_size']
    total_data = input_size + output_size
    
    features[4] = np.log1p(input_size)                 # log输入大小
    features[5] = np.log1p(output_size)                # log输出大小
    features[6] = np.log1p(total_data)                 # log总数据量
    features[7] = output_size / max(input_size, 1e-6)  # 输出/输入比率
    features[8] = total_data / max(runtime, 1e-6)      # 数据密集度
    features[9] = runtime / max(total_data, 1e-6)      # 计算密集度
    features[10] = input_size / max(total_data, 1e-6)  # 输入数据比例
    features[11] = output_size / max(total_data, 1e-6) # 输出数据比例
    
    # 计算特征 (8维)
    features[12] = task_data.get('cpu_demand', 0) / 8.0        # 归一化CPU需求
    features[13] = task_data.get('memory_demand', 0) / 16384.0 # 归一化内存需求
    features[14] = task_data.get('io_operations', 0) / 1000.0  # 归一化I/O操作
    features[15] = task_data.get('network_transfer', 0) / 1024.0 # 归一化网络传输
    features[16] = features[12] * features[1]                   # CPU-时间交互
    features[17] = features[13] * features[1]                   # 内存-时间交互
    features[18] = features[14] * features[1]                   # I/O-时间交互
    features[19] = features[15] * features[1]                   # 网络-时间交互
    
    # 任务类型特征 (4维) - 独热编码
    task_type = task_data.get('task_type', 'compute')
    type_mapping = {'compute': 0, 'io': 1, 'network': 2, 'memory': 3}
    type_idx = type_mapping.get(task_type, 0)
    features[20:24] = 0
    features[20 + type_idx] = 1.0
    
    # 优先级和其他特征 (8维)
    features[24] = task_data.get('priority', 1) / 5.0          # 归一化优先级
    features[25] = task_data.get('deadline', 1000) / 1000.0    # 归一化截止时间
    features[26] = task_data.get('criticality', 0.5)           # 关键性
    features[27] = task_data.get('parallelizable', 0)          # 可并行标识
    features[28:32] = np.random.normal(0, 0.01, 4)             # 预留特征
    
    return features
```

##### 资源需求特征 (16维)
```python
def extract_resource_demand_features(task_data):
    """提取资源需求特征"""
    features = np.zeros(16)
    
    # 原始资源需求 (4维)
    features[0] = task_data.get('cpu_demand', 0) / 8.0
    features[1] = task_data.get('memory_demand', 0) / 16384.0
    features[2] = task_data.get('io_operations', 0) / 1000.0
    features[3] = task_data.get('network_transfer', 0) / 1024.0
    
    # 资源强度特征 (4维)
    runtime = max(task_data.get('runtime', 1), 1e-6)
    features[4] = features[0] / runtime * 100           # CPU强度
    features[5] = features[1] / runtime * 100           # 内存强度
    features[6] = features[2] / runtime * 100           # I/O强度
    features[7] = features[3] / runtime * 100           # 网络强度
    
    # 资源比例特征 (4维)
    total_resource = sum(features[0:4]) + 1e-6
    features[8] = features[0] / total_resource           # CPU比例
    features[9] = features[1] / total_resource           # 内存比例
    features[10] = features[2] / total_resource          # I/O比例
    features[11] = features[3] / total_resource          # 网络比例
    
    # 资源主导性特征 (4维)
    dominant_resource = np.argmax(features[0:4])
    features[12:16] = 0
    features[12 + dominant_resource] = 1.0
    
    return features
```

##### DAG结构特征 (24维)
```python
def extract_dag_structure_features(dag, task_id):
    """提取DAG结构特征"""
    features = np.zeros(24)
    
    # 基础图特征 (8维)
    features[0] = dag.in_degree(task_id)                # 入度
    features[1] = dag.out_degree(task_id)               # 出度
    features[2] = dag.in_degree(task_id) + dag.out_degree(task_id)  # 总度数
    features[3] = len(list(nx.ancestors(dag, task_id))) # 祖先节点数
    features[4] = len(list(nx.descendants(dag, task_id))) # 后代节点数
    features[5] = nx.shortest_path_length(dag, source=task_id) if dag.out_degree(task_id) > 0 else 0  # 到叶子节点距离
    features[6] = 1.0 if dag.in_degree(task_id) == 0 else 0.0   # 是否为源节点
    features[7] = 1.0 if dag.out_degree(task_id) == 0 else 0.0  # 是否为汇节点
    
    # 中心性特征 (8维)
    try:
        betweenness = nx.betweenness_centrality(dag)
        closeness = nx.closeness_centrality(dag)
        eigenvector = nx.eigenvector_centrality(dag, max_iter=1000)
        pagerank = nx.pagerank(dag)
        
        features[8] = betweenness.get(task_id, 0)       # 介数中心性
        features[9] = closeness.get(task_id, 0)         # 接近中心性
        features[10] = eigenvector.get(task_id, 0)      # 特征向量中心性
        features[11] = pagerank.get(task_id, 0)         # PageRank值
    except:
        features[8:12] = 0.1  # 默认值
    
    # 路径特征 (8维)
    try:
        # 关键路径相关
        critical_path = nx.dag_longest_path(dag, weight='runtime')
        features[12] = 1.0 if task_id in critical_path else 0.0  # 是否在关键路径上
        features[13] = critical_path.index(task_id) / len(critical_path) if task_id in critical_path else 0.0  # 关键路径位置
        
        # 层次特征
        topo_order = list(nx.topological_sort(dag))
        features[14] = topo_order.index(task_id) / len(topo_order)  # 拓扑位置
        
        # 并行度特征
        levels = list(nx.topological_generations(dag))
        task_level = next(i for i, level in enumerate(levels) if task_id in level)
        features[15] = task_level / len(levels)          # 层级位置
        features[16] = len(levels[task_level])           # 同层任务数
        features[17] = len(levels[task_level]) / len(dag.nodes())  # 同层任务比例
        
    except:
        features[12:18] = 0.1  # 默认值
    
    # 邻居特征 (6维)
    predecessors = list(dag.predecessors(task_id))
    successors = list(dag.successors(task_id))
    
    features[18] = len(predecessors)                    # 前驱数量
    features[19] = len(successors)                      # 后继数量
    features[20] = np.mean([dag.nodes[p].get('runtime', 0) for p in predecessors]) if predecessors else 0  # 前驱平均运行时间
    features[21] = np.mean([dag.nodes[s].get('runtime', 0) for s in successors]) if successors else 0      # 后继平均运行时间
    features[22] = sum([dag.edges[p, task_id].get('data_size', 0) for p in predecessors])  # 输入数据总量
    features[23] = sum([dag.edges[task_id, s].get('data_size', 0) for s in successors])    # 输出数据总量
    
    return features
```

#### 2.3 节点特征提取详细过程 (32维)
```python
def extract_node_features(node_data):
    """提取节点特征"""
    features = np.zeros(32)
    
    # 基础容量特征 (8维)
    features[0] = node_data.get('cpu_capacity', 0) / 16.0      # 归一化CPU容量
    features[1] = node_data.get('memory_capacity', 0) / 32768.0 # 归一化内存容量
    features[2] = node_data.get('io_capacity', 0) / 2000.0     # 归一化I/O容量
    features[3] = node_data.get('network_capacity', 0) / 2000.0 # 归一化网络容量
    features[4] = features[0] * features[1]                    # CPU-内存交互
    features[5] = features[2] * features[3]                    # I/O-网络交互
    features[6] = (features[0] + features[1] + features[2] + features[3]) / 4  # 平均容量
    features[7] = max(features[0:4])                           # 最大容量维度
    
    # 效率和成本特征 (2维)
    features[8] = node_data.get('energy_efficiency', 0.5)      # 能源效率
    features[9] = 1.0 - min(node_data.get('cost_per_hour', 0.1), 1.0)  # 成本效益(反向)
    
    # 节点类型特征 (4维) - 基于容量的类型编码
    capacity_vector = features[0:4]
    dominant_type = np.argmax(capacity_vector)
    features[10:14] = 0
    features[10 + dominant_type] = 1.0
    
    # 可用性和位置特征 (4维)
    features[14] = node_data.get('availability', 0.99)         # 可用性
    features[15] = hash(node_data.get('location', '')) % 100 / 100.0  # 位置哈希
    features[16] = node_data.get('load_factor', 0.0)           # 当前负载
    features[17] = 1.0 - features[16]                          # 剩余容量
    
    # 扩展特征 (14维) - 预留空间
    features[18:32] = np.random.normal(0, 0.01, 14)
    
    return features
```

### 3. 批处理数据格式

#### 3.1 模型输入批次数据结构
```python
# 批处理后的数据格式
batch_data = {
    # 任务特征 [batch_size, max_tasks, 128]
    'task_features': torch.tensor([
        [[0.23, 0.45, ..., 0.67],  # batch 0, task 0的128维特征
         [0.34, 0.56, ..., 0.78],  # batch 0, task 1的128维特征
         ...],                      # 最多100个任务
        [[0.12, 0.34, ..., 0.56],  # batch 1, task 0的128维特征
         ...]
    ], dtype=torch.float32),
    
    # 节点特征 [total_nodes, 32] (所有batch的节点拼接)
    'node_features': torch.tensor([
        [0.8, 0.6, 0.9, 0.7, ...],  # node 0的32维特征
        [0.7, 0.8, 0.6, 0.9, ...],  # node 1的32维特征
        ...                          # 最多16个节点/batch
    ], dtype=torch.float32),
    
    # 邻接矩阵 [batch_size, max_tasks, max_tasks]
    'adjacency_matrix': torch.tensor([
        [[0, 1, 0, ...],  # batch 0的任务依赖关系
         [0, 0, 1, ...],
         ...],
        [[0, 1, 1, ...],  # batch 1的任务依赖关系
         ...]
    ], dtype=torch.float32),
    
    # 图边索引 [2, num_edges] (PyG格式)
    'task_edge_index': torch.tensor([
        [0, 1, 2, ...],  # 源节点索引
        [1, 2, 3, ...]   # 目标节点索引
    ], dtype=torch.long),
    
    # 批次索引
    'task_batch': torch.tensor([0, 0, 0, 1, 1, 1, ...]),  # 每个任务属于哪个batch
    'node_batch': torch.tensor([0, 0, 1, 1, ...]),        # 每个节点属于哪个batch
    
    # 资源约束 [batch_size, max_nodes, 4]
    'resource_constraints': torch.tensor([
        [[8.0, 16384, 1000, 1000],   # batch 0, node 0的资源容量
         [4.0, 8192, 500, 500],      # batch 0, node 1的资源容量
         ...],
        [...]
    ], dtype=torch.float32),
    
    # 约束数据字典
    'constraint_data': {
        'dependency': torch.tensor(...),      # 依赖约束数据
        'resource': torch.tensor(...),        # 资源约束数据
        'temporal': torch.tensor(...),        # 时间约束数据
        'communication': torch.tensor(...)    # 通信约束数据
    }
}
```

## 🧠 三层GNN架构详细实现流程

### 第一层：DAG Transformer层

#### 4.1 输入数据处理
```python
# 输入: batch_data['task_features'] [batch_size, max_tasks, 128]
# 输入: batch_data['adjacency_matrix'] [batch_size, max_tasks, max_tasks]

def dag_transformer_forward(task_features, adjacency_matrix):
    """DAG Transformer层前向传播详细流程"""

    batch_size, max_tasks, input_dim = task_features.shape
    # batch_size=4, max_tasks=100, input_dim=128

    # Step 1: 输入投影 128维 -> 256维
    projected_features = self.input_projection(task_features)
    # 输出形状: [4, 100, 256]

    # Step 2: 计算DAG结构信息
    dag_info = self.compute_dag_info(adjacency_matrix)
    # dag_info = {
    #     'topo_order': [4, 100],      # 拓扑排序位置
    #     'depth': [4, 100],           # 节点深度
    #     'critical_path': [4, 100],   # 是否在关键路径上
    #     'dependency_count': [4, 100] # 依赖数量
    # }

    # Step 3: DAG位置编码
    dag_pos_encoding = self.dag_positional_encoding(projected_features, dag_info)
    # 输出形状: [4, 100, 256]

    # Step 4: 计算距离矩阵(用于注意力权重调整)
    distance_matrix = self.compute_distance_matrix(adjacency_matrix)
    # 输出形状: [4, 100, 100]

    # Step 5: 通过多层Transformer
    hidden_states = dag_pos_encoding
    attention_weights_list = []

    for layer_idx, transformer_layer in enumerate(self.transformer_layers):
        # 每层输入: [4, 100, 256]
        hidden_states, attention_weights = transformer_layer(
            hidden_states,
            distance_matrix,
            mask=None
        )
        # 输出: [4, 100, 256], attention_weights: [4, 8, 100, 100]
        attention_weights_list.append(attention_weights)

    # Step 6: 输出投影
    output_features = self.output_projection(hidden_states)
    # 输出形状: [4, 100, 256]

    return output_features, {
        'attention_weights': attention_weights_list,
        'dag_info': dag_info,
        'distance_matrix': distance_matrix
    }
```

#### 4.2 DAG位置编码详细实现
```python
def dag_positional_encoding(x, dag_info):
    """DAG位置编码详细实现"""
    batch_size, seq_len, d_model = x.shape

    # 传统位置编码 [1, seq_len, d_model]
    pos_encoding = self.pe[:, :seq_len, :]

    # DAG结构编码
    # 拓扑排序编码 [batch_size, seq_len, 64]
    topo_enc = self.topo_embedding(dag_info['topo_order'])

    # 深度编码 [batch_size, seq_len, 64]
    depth_enc = self.depth_embedding(dag_info['depth'])

    # 关键路径编码 [batch_size, seq_len, 64]
    critical_enc = self.critical_path_embedding(dag_info['critical_path'])

    # 依赖数量编码 [batch_size, seq_len, 64]
    dep_enc = self.dependency_embedding(dag_info['dependency_count'])

    # 拼接所有DAG编码 [batch_size, seq_len, 256]
    dag_encoding = torch.cat([topo_enc, depth_enc, critical_enc, dep_enc], dim=-1)

    # 投影到模型维度 [batch_size, seq_len, d_model]
    dag_encoding = self.dag_projection(dag_encoding)

    # 融合传统位置编码和DAG编码
    enhanced_encoding = x + pos_encoding + dag_encoding

    return enhanced_encoding
```

#### 4.3 依赖感知注意力机制
```python
def dependency_aware_attention(query, key, value, distance_matrix):
    """依赖感知注意力机制"""

    # 标准注意力计算
    # query, key, value: [batch_size, num_heads, seq_len, head_dim]
    attention_scores = torch.matmul(query, key.transpose(-2, -1))
    # attention_scores: [batch_size, num_heads, seq_len, seq_len]

    # 缩放
    attention_scores = attention_scores / math.sqrt(query.size(-1))

    # 距离偏置 - 根据DAG中的距离调整注意力
    # distance_matrix: [batch_size, seq_len, seq_len]
    distance_bias = self.distance_bias_embedding(distance_matrix.long())
    # distance_bias: [batch_size, seq_len, seq_len, num_heads]

    distance_bias = distance_bias.permute(0, 3, 1, 2)
    # distance_bias: [batch_size, num_heads, seq_len, seq_len]

    # 添加距离偏置
    attention_scores = attention_scores + distance_bias

    # 依赖关系掩码 - 强化直接依赖关系的注意力
    dependency_mask = (distance_matrix == 1).float()  # 直接依赖
    dependency_boost = dependency_mask.unsqueeze(1) * 2.0  # 增强因子
    attention_scores = attention_scores + dependency_boost

    # Softmax归一化
    attention_weights = F.softmax(attention_scores, dim=-1)

    # 应用注意力权重
    attended_values = torch.matmul(attention_weights, value)

    return attended_values, attention_weights
```

### 第二层：PINN约束增强层

#### 5.1 约束数据预处理
```python
def prepare_constraint_data(batch_data):
    """准备约束数据"""

    constraint_data = {}

    # 依赖约束数据 [batch_size, max_tasks, max_tasks]
    adjacency = batch_data['adjacency_matrix']
    constraint_data['dependency'] = adjacency

    # 资源约束数据 [batch_size, max_tasks, 4] -> [batch_size, max_nodes, 4]
    task_demands = extract_task_resource_demands(batch_data['task_features'])
    node_capacities = batch_data['resource_constraints']
    constraint_data['resource'] = {
        'task_demands': task_demands,
        'node_capacities': node_capacities
    }

    # 时间约束数据
    task_runtimes = extract_task_runtimes(batch_data['task_features'])
    constraint_data['temporal'] = {
        'task_runtimes': task_runtimes,
        'deadlines': extract_deadlines(batch_data['task_features'])
    }

    # 通信约束数据
    communication_matrix = compute_communication_costs(
        batch_data['adjacency_matrix'],
        batch_data['task_features']
    )
    constraint_data['communication'] = communication_matrix

    return constraint_data

def extract_task_resource_demands(task_features):
    """从任务特征中提取资源需求"""
    # task_features: [batch_size, max_tasks, 128]

    # 资源需求在特征的32-47维 (资源需求特征16维)
    resource_features = task_features[:, :, 32:48]  # [batch_size, max_tasks, 16]

    # 提取原始资源需求 (前4维)
    cpu_demand = resource_features[:, :, 0] * 8.0      # 反归一化
    memory_demand = resource_features[:, :, 1] * 16384.0
    io_demand = resource_features[:, :, 2] * 1000.0
    network_demand = resource_features[:, :, 3] * 1024.0

    # 组合成资源需求矩阵 [batch_size, max_tasks, 4]
    task_demands = torch.stack([cpu_demand, memory_demand, io_demand, network_demand], dim=-1)

    return task_demands
```

#### 5.2 PINN约束层前向传播
```python
def pinn_constraint_forward(task_features, constraint_data):
    """PINN约束层详细前向传播"""

    # 输入: task_features [batch_size, max_tasks, 256] (来自DAG Transformer)
    batch_size, max_tasks, feature_dim = task_features.shape

    # Step 1: 约束嵌入
    constraint_embeddings = []

    # 依赖约束嵌入
    if 'dependency' in constraint_data:
        dep_matrix = constraint_data['dependency']  # [batch_size, max_tasks, max_tasks]
        dep_embedding = self.dependency_encoder(dep_matrix)  # [batch_size, max_tasks, 128]
        constraint_embeddings.append(dep_embedding)

    # 资源约束嵌入
    if 'resource' in constraint_data:
        resource_data = constraint_data['resource']
        task_demands = resource_data['task_demands']  # [batch_size, max_tasks, 4]
        resource_embedding = self.resource_encoder(task_demands)  # [batch_size, max_tasks, 128]
        constraint_embeddings.append(resource_embedding)

    # 时间约束嵌入
    if 'temporal' in constraint_data:
        temporal_data = constraint_data['temporal']
        task_runtimes = temporal_data['task_runtimes']  # [batch_size, max_tasks]
        temporal_embedding = self.temporal_encoder(task_runtimes.unsqueeze(-1))  # [batch_size, max_tasks, 128]
        constraint_embeddings.append(temporal_embedding)

    # 通信约束嵌入
    if 'communication' in constraint_data:
        comm_matrix = constraint_data['communication']  # [batch_size, max_tasks, max_tasks]
        comm_embedding = self.communication_encoder(comm_matrix)  # [batch_size, max_tasks, 128]
        constraint_embeddings.append(comm_embedding)

    # Step 2: 约束融合
    if constraint_embeddings:
        # 拼接所有约束嵌入 [batch_size, max_tasks, 128*4=512]
        combined_constraints = torch.cat(constraint_embeddings, dim=-1)

        # 投影到特征维度 [batch_size, max_tasks, 256]
        constraint_features = self.constraint_projection(combined_constraints)

        # 多头注意力融合
        fused_features, attention_weights = self.constraint_fusion(
            query=task_features,           # [batch_size, max_tasks, 256]
            key=constraint_features,       # [batch_size, max_tasks, 256]
            value=constraint_features      # [batch_size, max_tasks, 256]
        )
        # fused_features: [batch_size, max_tasks, 256]
    else:
        fused_features = task_features
        attention_weights = None

    # Step 3: 约束感知变换
    constraint_aware_features = self.constraint_aware_transform(fused_features)
    # 输出: [batch_size, max_tasks, 256]

    # Step 4: 约束验证分数
    constraint_validity = self.constraint_validator(constraint_aware_features)
    # 输出: [batch_size, max_tasks, 1]

    return constraint_aware_features, {
        'constraint_validity': constraint_validity,
        'constraint_attention': attention_weights,
        'constraint_embeddings': constraint_embeddings
    }
```

#### 5.3 物理约束损失计算
```python
def compute_physics_informed_loss(assignment_probs, constraint_data):
    """计算物理约束损失"""

    # assignment_probs: [batch_size, max_tasks, max_nodes]
    batch_size, max_tasks, max_nodes = assignment_probs.shape

    total_loss = 0.0
    loss_components = {}

    # 1. 依赖关系约束损失
    if 'dependency' in constraint_data:
        dep_loss = compute_dependency_constraint_loss(
            assignment_probs,
            constraint_data['dependency'],
            constraint_data['temporal']['task_runtimes']
        )
        total_loss += self.constraint_weights['dependency'] * dep_loss
        loss_components['dependency'] = dep_loss

    # 2. 资源容量约束损失
    if 'resource' in constraint_data:
        resource_loss = compute_resource_constraint_loss(
            assignment_probs,
            constraint_data['resource']['task_demands'],
            constraint_data['resource']['node_capacities']
        )
        total_loss += self.constraint_weights['resource'] * resource_loss
        loss_components['resource'] = resource_loss

    # 3. 时间约束损失
    if 'temporal' in constraint_data:
        temporal_loss = compute_temporal_constraint_loss(
            assignment_probs,
            constraint_data['temporal']['task_runtimes'],
            constraint_data['temporal']['deadlines']
        )
        total_loss += self.constraint_weights['temporal'] * temporal_loss
        loss_components['temporal'] = temporal_loss

    # 4. 通信约束损失
    if 'communication' in constraint_data:
        comm_loss = compute_communication_constraint_loss(
            assignment_probs,
            constraint_data['communication']
        )
        total_loss += self.constraint_weights['communication'] * comm_loss
        loss_components['communication'] = comm_loss

    return total_loss, loss_components

def compute_dependency_constraint_loss(assignment_probs, adjacency_matrix, task_runtimes):
    """计算依赖关系约束损失"""

    batch_size, max_tasks, max_nodes = assignment_probs.shape

    # 计算每个任务在每个节点上的预期完成时间
    # assignment_probs: [batch_size, max_tasks, max_nodes]
    # task_runtimes: [batch_size, max_tasks]

    # 扩展运行时间维度 [batch_size, max_tasks, max_nodes]
    expanded_runtimes = task_runtimes.unsqueeze(-1).expand(-1, -1, max_nodes)

    # 计算加权完成时间 [batch_size, max_tasks, max_nodes]
    weighted_completion_times = assignment_probs * expanded_runtimes

    # 每个任务的期望完成时间 [batch_size, max_tasks]
    expected_completion_times = weighted_completion_times.sum(dim=-1)

    # 检查依赖关系违反
    dependency_violations = torch.zeros(batch_size, device=assignment_probs.device)

    for b in range(batch_size):
        adj = adjacency_matrix[b]  # [max_tasks, max_tasks]
        completion_times = expected_completion_times[b]  # [max_tasks]

        # 对于每条边 i -> j，检查 completion_time[i] <= start_time[j]
        # 简化假设：start_time[j] = completion_time[j] - runtime[j]
        start_times = completion_times - task_runtimes[b]

        for i in range(max_tasks):
            for j in range(max_tasks):
                if adj[i, j] > 0:  # 存在依赖关系 i -> j
                    # 违反条件：前驱任务完成时间 > 后继任务开始时间
                    violation = torch.relu(completion_times[i] - start_times[j])
                    dependency_violations[b] += violation

    return dependency_violations.mean()

def compute_resource_constraint_loss(assignment_probs, task_demands, node_capacities):
    """计算资源容量约束损失"""

    # assignment_probs: [batch_size, max_tasks, max_nodes]
    # task_demands: [batch_size, max_tasks, 4]
    # node_capacities: [batch_size, max_nodes, 4]

    batch_size, max_tasks, max_nodes = assignment_probs.shape

    # 计算每个节点的资源使用量
    # 使用爱因斯坦求和约定进行高效计算
    # 'btd,btn->bnd' 表示: batch, task, demand × batch, task, node -> batch, node, demand
    resource_usage = torch.einsum('btn,btd->bnd', assignment_probs, task_demands)
    # resource_usage: [batch_size, max_nodes, 4]

    # 计算资源超载量
    resource_violations = torch.relu(resource_usage - node_capacities)
    # resource_violations: [batch_size, max_nodes, 4]

    # 总违反量
    total_violation = resource_violations.sum(dim=(1, 2))  # [batch_size]

    return total_violation.mean()
```

### 第三层：GAT决策输出层

#### 6.1 任务-节点兼容性计算
```python
def compute_task_node_compatibility(task_features, node_features, task_batch, node_batch):
    """计算任务-节点兼容性矩阵"""

    # task_features: [total_tasks, 256] (来自PINN层)
    # node_features: [total_nodes, 32]
    # task_batch: [total_tasks] - 每个任务属于哪个batch
    # node_batch: [total_nodes] - 每个节点属于哪个batch

    total_tasks = task_features.size(0)
    total_nodes = node_features.size(0)

    # 扩展节点特征到与任务特征相同维度
    expanded_node_features = self.node_feature_projection(node_features)  # [total_nodes, 256]

    # 计算所有任务-节点对的兼容性
    compatibility_scores = torch.zeros(total_tasks, total_nodes, device=task_features.device)

    for task_idx in range(total_tasks):
        task_batch_id = task_batch[task_idx]
        task_feat = task_features[task_idx]  # [256]

        # 找到同一batch的所有节点
        same_batch_nodes = (node_batch == task_batch_id).nonzero(as_tuple=True)[0]

        for node_idx in same_batch_nodes:
            node_feat = expanded_node_features[node_idx]  # [256]

            # 计算兼容性特征
            combined_feat = torch.cat([task_feat, node_feat, task_feat * node_feat], dim=0)  # [768]

            # 通过兼容性网络
            compatibility_score = self.compatibility_net(combined_feat)  # [1]
            compatibility_scores[task_idx, node_idx] = compatibility_score.squeeze()

    return compatibility_scores

def compatibility_network_forward(combined_features):
    """兼容性网络详细实现"""

    # combined_features: [768] = [256 + 256 + 256] (task + node + interaction)

    # 第一层：特征分解
    x = self.compatibility_layers[0](combined_features)  # [768] -> [512]
    x = F.relu(x)
    x = F.dropout(x, p=0.1, training=self.training)

    # 第二层：特征融合
    x = self.compatibility_layers[1](x)  # [512] -> [256]
    x = F.relu(x)
    x = F.dropout(x, p=0.1, training=self.training)

    # 第三层：兼容性评分
    compatibility_score = self.compatibility_layers[2](x)  # [256] -> [1]
    compatibility_score = torch.sigmoid(compatibility_score)  # 归一化到[0,1]

    return compatibility_score
```

#### 6.2 GAT决策层前向传播
```python
def gat_decision_forward(task_features, node_features, task_edge_index, task_batch, node_batch, resource_constraints):
    """GAT决策层详细前向传播"""

    # 输入: task_features [total_tasks, 256] (来自PINN层)
    # 输入: node_features [total_nodes, 32]

    total_tasks = task_features.size(0)
    total_nodes = node_features.size(0)

    # Step 1: 节点特征投影到统一维度
    projected_node_features = self.node_projection(node_features)  # [total_nodes, 256]

    # Step 2: 构建异构图 (任务节点 + 计算节点)
    # 创建任务-节点连接边 (全连接，后续通过注意力筛选)
    task_node_edges = []
    for task_idx in range(total_tasks):
        task_batch_id = task_batch[task_idx]
        # 找到同一batch的节点
        same_batch_nodes = (node_batch == task_batch_id).nonzero(as_tuple=True)[0]
        for node_idx in same_batch_nodes:
            task_node_edges.append([task_idx, total_tasks + node_idx])  # 节点索引偏移
            task_node_edges.append([total_tasks + node_idx, task_idx])  # 双向连接

    task_node_edge_index = torch.tensor(task_node_edges, device=task_features.device).t()

    # 合并任务边和任务-节点边
    # 调整任务边索引 (原本的任务间连接)
    adjusted_task_edges = task_edge_index  # 已经是正确的索引

    # 合并所有边
    full_edge_index = torch.cat([adjusted_task_edges, task_node_edge_index], dim=1)

    # Step 3: 合并所有节点特征
    all_node_features = torch.cat([task_features, projected_node_features], dim=0)
    # all_node_features: [total_tasks + total_nodes, 256]

    # Step 4: 通过多层GAT
    x = all_node_features
    attention_weights_list = []

    for layer_idx, gat_layer in enumerate(self.gat_layers):
        x, attention_weights = gat_layer(x, full_edge_index, return_attention_weights=True)
        # x: [total_tasks + total_nodes, hidden_dim]
        # attention_weights: (edge_index, attention_values)

        x = F.relu(x)
        x = F.dropout(x, p=0.1, training=self.training)
        attention_weights_list.append(attention_weights)

    # Step 5: 提取任务特征 (前total_tasks个)
    final_task_features = x[:total_tasks]  # [total_tasks, hidden_dim]

    # Step 6: 计算任务-节点分配概率
    assignment_logits = self.decision_layer(final_task_features)  # [total_tasks, 1]

    # Step 7: 重构为批次格式的分配矩阵
    assignment_probs = self.reconstruct_assignment_matrix(
        assignment_logits, task_batch, node_batch, resource_constraints
    )
    # assignment_probs: [batch_size, max_tasks, max_nodes]

    return assignment_probs, {
        'gat_attention_weights': attention_weights_list,
        'final_task_features': final_task_features,
        'assignment_logits': assignment_logits
    }

def reconstruct_assignment_matrix(assignment_logits, task_batch, node_batch, resource_constraints):
    """重构分配概率矩阵"""

    # assignment_logits: [total_tasks, 1]
    # task_batch: [total_tasks]
    # node_batch: [total_nodes]

    batch_size = task_batch.max().item() + 1
    max_tasks = 100  # 预设最大任务数
    max_nodes = 16   # 预设最大节点数

    # 初始化分配概率矩阵
    assignment_probs = torch.zeros(batch_size, max_tasks, max_nodes, device=assignment_logits.device)

    for b in range(batch_size):
        # 找到当前batch的任务和节点
        batch_task_mask = (task_batch == b)
        batch_node_mask = (node_batch == b)

        batch_task_indices = batch_task_mask.nonzero(as_tuple=True)[0]
        batch_node_indices = batch_node_mask.nonzero(as_tuple=True)[0]

        num_batch_tasks = len(batch_task_indices)
        num_batch_nodes = len(batch_node_indices)

        if num_batch_tasks > 0 and num_batch_nodes > 0:
            # 提取当前batch的任务logits
            batch_logits = assignment_logits[batch_task_indices]  # [num_batch_tasks, 1]

            # 扩展到所有节点 [num_batch_tasks, num_batch_nodes]
            expanded_logits = batch_logits.expand(-1, num_batch_nodes)

            # 应用资源约束掩码
            resource_mask = self.apply_resource_constraints(
                batch_task_indices, batch_node_indices, resource_constraints[b]
    )

            # 掩码无效分配
            masked_logits = expanded_logits + (resource_mask - 1) * 1e9

            # Softmax归一化
            batch_probs = F.softmax(masked_logits, dim=-1)

            # 填入分配矩阵
            assignment_probs[b, :num_batch_tasks, :num_batch_nodes] = batch_probs

    return assignment_probs

def apply_resource_constraints(task_indices, node_indices, node_capacities):
    """应用资源约束掩码"""

    num_tasks = len(task_indices)
    num_nodes = len(node_indices)

    # 简化的资源约束检查
    # 实际实现中应该根据任务需求和节点容量进行详细检查
    resource_mask = torch.ones(num_tasks, num_nodes, device=node_capacities.device)

    # 这里可以添加具体的资源约束逻辑
    # 例如：检查CPU、内存、I/O、网络容量是否满足任务需求

    return resource_mask
```

## 🔄 完整端到端数据流程

### 7. 完整前向传播流程

```python
def complete_forward_pass(batch_data):
    """完整的端到端前向传播流程"""

    print("🚀 开始端到端前向传播...")

    # ========== 输入数据检查 ==========
    print(f"📊 输入数据形状:")
    print(f"  - task_features: {batch_data['task_features'].shape}")      # [4, 100, 128]
    print(f"  - node_features: {batch_data['node_features'].shape}")      # [64, 32]
    print(f"  - adjacency_matrix: {batch_data['adjacency_matrix'].shape}") # [4, 100, 100]
    print(f"  - task_edge_index: {batch_data['task_edge_index'].shape}")  # [2, num_edges]

    # ========== 第一层：DAG Transformer ==========
    print("\n🧠 第一层：DAG Transformer处理...")

    transformer_output, transformer_debug = self.dag_transformer(
        task_features=batch_data['task_features'],        # [4, 100, 128]
        adjacency_matrix=batch_data['adjacency_matrix']   # [4, 100, 100]
    )

    print(f"  ✅ Transformer输出形状: {transformer_output.shape}")  # [4, 100, 256]
    print(f"  📈 注意力权重层数: {len(transformer_debug['attention_weights'])}")

    # ========== 第二层：PINN约束增强 ==========
    print("\n⚖️ 第二层：PINN约束增强处理...")

    # 准备约束数据
    constraint_data = self.prepare_constraint_data(batch_data)
    print(f"  📋 约束类型: {list(constraint_data.keys())}")

    # 重塑为图格式 (batch -> flat)
    flat_task_features = transformer_output.view(-1, transformer_output.size(-1))  # [400, 256]

    pinn_output, pinn_debug = self.pinn_constraint_layer(
        task_features=flat_task_features,     # [400, 256]
        constraint_data=constraint_data
    )

    print(f"  ✅ PINN输出形状: {pinn_output.shape}")  # [400, 256]
    print(f"  🎯 约束验证分数形状: {pinn_debug['constraint_validity'].shape}")  # [400, 1]

    # ========== 第三层：GAT决策输出 ==========
    print("\n🎯 第三层：GAT决策输出...")

    assignment_probs, gat_debug = self.gat_decision_layer(
        task_features=pinn_output,                    # [400, 256]
        node_features=batch_data['node_features'],    # [64, 32]
        task_edge_index=batch_data['task_edge_index'], # [2, num_edges]
        task_batch=batch_data['task_batch'],          # [400]
        node_batch=batch_data['node_batch'],          # [64]
        resource_constraints=batch_data['resource_constraints']  # [4, 16, 4]
    )

    print(f"  ✅ 最终分配概率形状: {assignment_probs.shape}")  # [4, 100, 16]
    print(f"  📊 概率值范围: [{assignment_probs.min():.4f}, {assignment_probs.max():.4f}]")

    # ========== 约束损失计算 ==========
    print("\n📐 计算物理约束损失...")

    constraint_losses, loss_components = self.physics_loss(
        assignment_probs=assignment_probs,
        constraint_data=constraint_data
    )

    print(f"  💰 总约束损失: {constraint_losses:.6f}")
    for constraint_type, loss_value in loss_components.items():
        print(f"    - {constraint_type}: {loss_value:.6f}")

    # ========== 输出整理 ==========
    final_output = {
        'assignment_probs': assignment_probs,          # [4, 100, 16]
        'constraint_losses': constraint_losses,        # scalar
        'loss_components': loss_components,            # dict
        'debug_info': {
            'transformer': transformer_debug,
            'pinn': pinn_debug,
            'gat': gat_debug
        }
    }

    print(f"\n🎉 端到端处理完成！")
    print(f"📈 输出分配矩阵形状: {assignment_probs.shape}")
    print(f"🔍 调试信息包含: {list(final_output['debug_info'].keys())}")

    return final_output
```

### 8. 数据维度变化总结

```python
"""
📊 完整数据流维度变化总结

输入阶段:
├── 原始工作流DAG: NetworkX图结构
├── 异构节点配置: List[Dict]
└── 工作流元数据: Dict

特征提取阶段:
├── 任务特征提取: DAG节点 → [num_tasks, 128]
│   ├── 基础特征: [32维] 执行时间、数据大小、计算强度
│   ├── 资源需求: [16维] CPU、内存、I/O、网络需求
│   ├── DAG结构: [24维] 度数、中心性、路径特征
│   ├── 图着色: [8维] 颜色编码、资源类型
│   ├── 工作流上下文: [24维] 类型编码、并行组信息
│   └── 统计特征: [24维] 运行时间统计、邻居特征
├── 节点特征提取: 节点配置 → [num_nodes, 32]
│   ├── 基础容量: [8维] CPU、内存、I/O、网络容量
│   ├── 效率成本: [2维] 能源效率、使用成本
│   ├── 节点类型: [4维] 基于容量的类型编码
│   └── 扩展特征: [18维] 预留扩展空间
└── 图着色结果: 颜色分配 + 质量指标

批处理阶段:
├── 任务特征批次: [batch_size, max_tasks, 128]
├── 节点特征批次: [total_nodes, 32]
├── 邻接矩阵批次: [batch_size, max_tasks, max_tasks]
├── 图边索引: [2, num_edges]
├── 批次索引: task_batch[total_tasks], node_batch[total_nodes]
└── 约束数据: 依赖、资源、时间、通信约束

第一层 - DAG Transformer:
输入: [batch_size, max_tasks, 128]
├── 输入投影: [batch_size, max_tasks, 128] → [batch_size, max_tasks, 256]
├── DAG位置编码: 添加拓扑、深度、关键路径、依赖编码
├── 多层Transformer: 4层 × [batch_size, max_tasks, 256]
├── 依赖感知注意力: [batch_size, 8_heads, max_tasks, max_tasks]
└── 输出投影: [batch_size, max_tasks, 256]

第二层 - PINN约束增强:
输入: [total_tasks, 256] (展平后)
├── 约束嵌入:
│   ├── 依赖约束: [batch_size, max_tasks, max_tasks] → [total_tasks, 128]
│   ├── 资源约束: [total_tasks, 4] → [total_tasks, 128]
│   ├── 时间约束: [total_tasks, 1] → [total_tasks, 128]
│   └── 通信约束: [batch_size, max_tasks, max_tasks] → [total_tasks, 128]
├── 约束融合: 多头注意力 [total_tasks, 256]
├── 约束感知变换: [total_tasks, 256] → [total_tasks, 256]
└── 约束验证: [total_tasks, 256] → [total_tasks, 1]

第三层 - GAT决策输出:
输入: [total_tasks, 256] + [total_nodes, 32]
├── 节点特征投影: [total_nodes, 32] → [total_nodes, 256]
├── 异构图构建: [total_tasks + total_nodes, 256]
├── 多层GAT: 3层 × [total_tasks + total_nodes, hidden_dim]
├── 任务特征提取: [total_tasks, hidden_dim]
├── 决策网络: [total_tasks, hidden_dim] → [total_tasks, 1]
└── 分配矩阵重构: [batch_size, max_tasks, max_nodes]

输出阶段:
├── 分配概率矩阵: [batch_size, max_tasks, max_nodes]
├── 约束损失: scalar + 组件损失字典
└── 调试信息: 注意力权重、中间特征、验证分数

性能指标:
├── 完工时间: 基于分配结果和任务运行时间计算
├── 资源利用率: 节点资源使用量 / 节点总容量
├── 负载均衡度: 节点负载标准差 / 平均负载
├── 约束满足率: 1 - (约束违反数 / 总约束数)
└── 能耗和成本: 基于节点效率和成本计算
"""
```

## 📈 关键技术指标

### 计算复杂度分析
- **特征提取**: O(V + E) - V个任务，E条依赖边
- **图着色**: O(V²) - 冲突图构建和着色
- **DAG Transformer**: O(V² × d) - 自注意力机制
- **PINN约束**: O(V × C) - V个任务，C个约束
- **GAT决策**: O((V+N)² × d) - V个任务，N个节点
- **总体复杂度**: O(V² × d + V × N × d)

### 内存使用分析
- **任务特征存储**: batch_size × max_tasks × 128 × 4 bytes
- **节点特征存储**: total_nodes × 32 × 4 bytes
- **邻接矩阵存储**: batch_size × max_tasks² × 4 bytes
- **中间特征存储**: 约3倍输入特征大小
- **注意力权重存储**: num_heads × max_tasks² × 4 bytes
- **峰值内存**: 约为输入数据的5-8倍

### 训练效率优化
- **梯度累积**: 支持大批次训练
- **混合精度**: 使用FP16减少内存使用
- **动态图**: 支持变长序列的高效处理
- **检查点**: 支持训练中断恢复
- **分布式训练**: 支持多GPU并行训练

这个超详细的技术文档涵盖了从原始数据输入到最终输出的每一个步骤，包括具体的数据格式、维度变化、计算流程和实现细节。
